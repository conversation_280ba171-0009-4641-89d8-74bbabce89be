package com.shootZ.app.shoot_z.config

import android.content.Context
import android.os.Environment
import android.util.Log
import java.io.File

class VideoStorageManager(context: Context) {

    companion object {
        private const val TEMPORARY_VIDEO_FOLDER = "temporary_videos"
        private const val FINAL_VIDEO_FOLDER = "final_videos"
    }

    private val context = context.applicationContext

    // 获取临时视频目录
    fun getTemporaryVideoDirectory(): File {
        return getOrCreateDirectory(TEMPORARY_VIDEO_FOLDER)
    }

    // 获取最终视频目录
    fun getFinalVideoDirectory(): File {
        return getOrCreateDirectory(FINAL_VIDEO_FOLDER)
    }

    // 创建临时视频文件
    fun createTemporaryVideoFile(): File {
        val dir = getTemporaryVideoDirectory()
        return File(dir, "temp_${System.currentTimeMillis()}.mp4")
    }

    // 创建最终视频文件
    fun createFinalVideoFile(): File {
        val dir = getFinalVideoDirectory()
        return File(dir, "final_${System.currentTimeMillis()}.mp4")
    }

    // 清理临时视频文件夹
    fun cleanupTemporaryFiles() {
        val dir = getTemporaryVideoDirectory()
        if (dir.exists() && dir.isDirectory) {
            dir.listFiles()?.forEach { file ->
                if (file.isFile && (file.name.endsWith(".mp4") || file.name.endsWith(".tmp"))) {
                    file.delete()
                }
            }
        }
    }

    // 获取所有最终视频文件
    fun getAllFinalVideos(): List<File> {
        val dir = getFinalVideoDirectory()
        return dir.listFiles()
            ?.filter { it.isFile && it.name.endsWith(".mp4") }
            ?.sortedByDescending { it.lastModified() }
            ?: emptyList()
    }

    // 辅助方法：确保目录存在
    private fun getOrCreateDirectory(folderName: String): File {
        //true文件存沙盒
        if(true){
            val moviesDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)
                ?: context.filesDir // 如果外部存储不可用，回退到内部存储

            val targetDir = File(moviesDir, folderName)

            if (!targetDir.exists()) {
                if (!targetDir.mkdirs()) {
                    // 如果无法创建目录，记录错误
                    Log.e("VideoStorage", "无法创建目录: ${targetDir.absolutePath}")
                }
            }

            return targetDir
        }else{
            val moviesDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)
                ?: context.filesDir // 如果外部存储不可用，回退到内部存储

            val targetDir = File(moviesDir, folderName)

            if (!targetDir.exists()) {
                if (!targetDir.mkdirs()) {
                    // 如果无法创建目录，记录错误
                    Log.e("VideoStorage", "无法创建目录: ${targetDir.absolutePath}")
                }
            }

            return targetDir
        }
    }

    // 获取存储信息统计
    fun getStorageStatistics(): StorageStats {
        val tempDir = getTemporaryVideoDirectory()
        val finalDir = getFinalVideoDirectory()

        return StorageStats(
            tempFileCount = countFiles(tempDir),
            tempTotalSize = calculateTotalSize(tempDir),
            finalFileCount = countFiles(finalDir),
            finalTotalSize = calculateTotalSize(finalDir),
            tempDirPath = tempDir.absolutePath,
            finalDirPath = finalDir.absolutePath
        )
    }

    // 内部方法：计算目录中文件数量
    private fun countFiles(dir: File): Int {
        return if (dir.exists() && dir.isDirectory) {
            dir.listFiles()?.size ?: 0
        } else {
            0
        }
    }

    // 内部方法：计算目录总大小
    private fun calculateTotalSize(dir: File): Long {
        return if (dir.exists() && dir.isDirectory) {
            dir.walk()
                .filter { it.isFile }
                .sumOf { it.length() }
        } else {
            0L
        }
    }

    // 数据类：存储统计数据
    data class StorageStats(
        val tempFileCount: Int,
        val tempTotalSize: Long,
        val finalFileCount: Int,
        val finalTotalSize: Long,
        val tempDirPath: String,
        val finalDirPath: String
    ) {
        // 格式化文件大小
        fun formatSize(bytes: Long): String {
            return when {
                bytes >= 1024 * 1024 -> "%.2f MB".format(bytes / (1024.0 * 1024.0))
                bytes >= 1024 -> "%.2f KB".format(bytes / 1024.0)
                else -> "$bytes bytes"
            }
        }

        // 格式化临时文件信息
        fun formatTempInfo(): String {
            return "临时文件夹: ${tempDirPath}\n" +
                    "文件数量: $tempFileCount\n" +
                    "大小: ${formatSize(tempTotalSize)}"
        }

        // 格式化最终文件信息
        fun formatFinalInfo(): String {
            return "最终视频文件夹: ${finalDirPath}\n" +
                    "文件数量: $finalFileCount\n" +
                    "大小: ${formatSize(finalTotalSize)}"
        }
    }
}