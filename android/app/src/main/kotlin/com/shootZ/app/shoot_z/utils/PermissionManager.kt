package com.shootZ.app.shoot_z.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.karumi.dexter.Dexter
import com.karumi.dexter.MultiplePermissionsReport
import com.karumi.dexter.PermissionToken
import com.karumi.dexter.listener.PermissionRequest
import com.karumi.dexter.listener.multi.MultiplePermissionsListener
import com.shootZ.app.shoot_z.interfaces.PermissionCallback
import com.shootZ.app.shoot_z.service.LogService

/**
 * 权限管理器
 * 处理摄像头、麦克风和存储访问权限
 */
class PermissionManager(private val activity: FragmentActivity) {

    companion object {
        private const val TAG = "PermissionManager"
        
        /**
         * 获取需要的权限列表（根据Android版本动态调整）
         */
        fun getRequiredPermissions(): Array<String> {
            val permissions = mutableListOf<String>()
            
            // 基础权限
            permissions.add(Manifest.permission.CAMERA)
            permissions.add(Manifest.permission.RECORD_AUDIO)
            // 存储权限根据Android版本处理
            if (Build.VERSION.SDK_INT >= 33) {
                // Android 13+ 使用分类媒体权限
                permissions.add(Manifest.permission.READ_MEDIA_VIDEO)
                permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
                permissions.add(Manifest.permission.READ_MEDIA_AUDIO)
            } else {
                // Android 12及以下使用传统存储权限
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
            
            return permissions.toTypedArray()
        }
        
        /**
         * 检查所有权限是否已授予
         */
        fun areAllPermissionsGranted(context: Context): Boolean {
            return getRequiredPermissions().all { permission ->
                ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
            }
        }
        
        /**
         * 获取未授予的权限列表
         */
        fun getDeniedPermissions(context: Context): List<String> {
            return getRequiredPermissions().filter { permission ->
                ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
            }
        }
        
        /**
         * 获取权限的用户友好名称
         */
        fun getPermissionDisplayName(permission: String): String {
            return when (permission) {
                Manifest.permission.CAMERA -> "摄像头"
                Manifest.permission.RECORD_AUDIO -> "麦克风"
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE -> "存储"
//                Manifest.permission.READ_MEDIA_VIDEO -> "视频文件"
//                Manifest.permission.READ_MEDIA_IMAGES -> "图片文件"
//                Manifest.permission.READ_MEDIA_AUDIO -> "音频文件"
                else -> permission
            }
        }
    }



    /**
     * 请求所有必要权限
     */
    fun requestPermissions(callback: PermissionCallback) {
        try {
            val requiredPermissions = getRequiredPermissions()
            
            // 检查是否所有权限都已授予
            if (areAllPermissionsGranted(activity)) {
                callback.onAllPermissionsGranted()
                return
            }

            // 使用Dexter请求权限
            Dexter.withContext(activity)
                .withPermissions(*requiredPermissions)
                .withListener(object : MultiplePermissionsListener {
                    override fun onPermissionsChecked(report: MultiplePermissionsReport?) {
                        Log.e(TAG, "onPermissionsChecked: 0"+report+"  ${report == null}")
                        report?.let {
                            if (it.areAllPermissionsGranted()) {
                                callback.onAllPermissionsGranted()
                                Log.e(TAG, "onPermissionsChecked: 1", )
                            } else {
                                val deniedPermissions = it.deniedPermissionResponses.map { response ->
                                    response.permissionName
                                }
                                val permanentlyDenied = it.deniedPermissionResponses.any { response ->
                                    response.isPermanentlyDenied
                                }
                                Log.e(TAG, "onPermissionsChecked:2"+deniedPermissions+"   "+permanentlyDenied )
                                callback.onPermissionsDenied(deniedPermissions, permanentlyDenied)
                            }
                        } ?: run {
                            callback.onPermissionError("权限检查结果为空")
                        }
                    }

                    override fun onPermissionRationaleShouldBeShown(
                        permissions: MutableList<PermissionRequest>?,
                        token: PermissionToken?
                    ) {
                        Log.e(TAG, "onPermissionsChecked:3" )
                        // 显示权限说明对话框
                        showPermissionRationale(permissions) {
                            token?.continuePermissionRequest()
                        }
                    }
                })
                .check()
                
        } catch (e: Exception) {
            callback.onPermissionError("权限请求失败: ${e.message}")
            Log.e(TAG, "onPermissionsChecked:4权限请求失败" )
        }
    }

    /**
     * 显示权限说明对话框
     */
    private fun showPermissionRationale(
        permissions: List<PermissionRequest>?, 
        onAccept: () -> Unit
    ) {
        if (permissions.isNullOrEmpty()) {
            onAccept()
            return
        }

        val permissionNames = permissions.map { request ->
            getPermissionDisplayName(request.name)
        }.distinct().joinToString("、")

        androidx.appcompat.app.AlertDialog.Builder(activity)
            .setTitle("需要权限")
            .setMessage("为了正常使用篮球检测功能，需要以下权限：\n\n" +
                    "$permissionNames\n\n" +
                    "• 摄像头：用于实时检测篮球投篮\n" +
                    "• 麦克风：用于录制投篮视频\n" +
                    "• 存储：用于保存投篮记录和视频")
            .setPositiveButton("授予权限") { _, _ ->
                onAccept()
            }
            .setNegativeButton("取消") { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 显示权限被拒绝的说明对话框
     */
    fun showPermissionDeniedDialog(
        deniedPermissions: List<String>,
        permanentlyDenied: Boolean,
        onRetry: () -> Unit,
        onCancel: () -> Unit
    ) {
        val permissionNames = deniedPermissions.map { permission ->
            getPermissionDisplayName(permission)
        }.distinct().joinToString("、")

        val message = if (permanentlyDenied) {
            "以下权限被永久拒绝：$permissionNames\n\n" +
                    "请前往设置页面手动开启这些权限，否则应用将无法正常工作。"
        } else {
            "需要以下权限才能正常使用：$permissionNames\n\n" +
                    "请点击重试并授予权限。"
        }

        androidx.appcompat.app.AlertDialog.Builder(activity)
            .setTitle("权限不足")
            .setMessage(message)
            .setPositiveButton(if (permanentlyDenied) "前往设置" else "重试") { _, _ ->
                if (permanentlyDenied) {
                    // 打开应用设置页面
                    openAppSettings()
                } else {
                    onRetry()
                }
            }
            .setNegativeButton("退出") { _, _ ->
                onCancel()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 打开应用设置页面
     */
    private fun openAppSettings() {
        try {
            val intent = android.content.Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = android.net.Uri.fromParts("package", activity.packageName, null)
            }
            activity.startActivity(intent)
        } catch (e: Exception) {
            LogService.error("无法打开应用设置: ${e.message}")
        }
    }
} 