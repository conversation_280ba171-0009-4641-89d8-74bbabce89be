package com.shootZ.app.shoot_z.service
import android.annotation.SuppressLint
import android.content.Context
import android.media.*
import android.os.Environment
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.util.Size
import com.shootZ.app.shoot_z.config.VideoStorageManager
import com.shootZ.app.shoot_z.model.ShootEventRecord
import io.microshow.rxffmpeg.RxFFmpegInvoke
import io.microshow.rxffmpeg.RxFFmpegInvoke.IFFmpegListener
import java.io.File
import java.text.SimpleDateFormat
import java.util.*


class VideoRecordingService(context: Context) {

    // 日志标签
    private val TAG = "VideoRecordingService"

    // 上下文
    private val appContext = context.applicationContext

    // 状态标志
    private var shouldSaveGoalVideos = true
    private var shouldSaveNoGoalVideos = true

    // 视频分辨率
    private var videoResolution: Size? = null
    private lateinit var storageManager: VideoStorageManager
    // 文件路径回调
    var filePathSuccessBlock: ((String,shootEventRecord : ShootEventRecord) -> Unit)? = null
    private val videoCacheList2 = mutableListOf<String>()
// 视频格式
//    companion object {
//        private const val VIDEO_MIME_TYPE = "video/avc" // H.264编码
//        public const val FRAME_RATE = 30 // 30 FPS
//        private const val IFRAME_INTERVAL = 1 // 1秒一个关键帧
//        private const val BIT_RATE = 8000000 // 8 Mbps
//        private const val MAX_CACHE_FILES = 5
//    }
// 创建临时视频文件

    private var tempVideoUrl: String? = null
    private var finalVideoUrl: String? = null
    init {

        // 初始化存储管理器
        storageManager = VideoStorageManager(appContext)
        storageManager.createTemporaryVideoFile()
        storageManager.createFinalVideoFile()
        tempVideoUrl= storageManager.getTemporaryVideoDirectory().absolutePath;
        finalVideoUrl= storageManager.getFinalVideoDirectory().absolutePath;
        // 初始化时自动清理旧文件
        cleanupOldVideoFiles()
        cleanupOldMergeVideoFiles2()
    }

    /**
     * 设置视频分辨率
     */
    fun setVideoResolution(width: Int, height: Int) {
        Log.d(TAG, "设置视频分辨率: ${width}x$height")
        videoResolution = Size(width, height)
    }

    /**
     * 设置是否保存进球视频
     */
    fun setShouldSaveGoalVideos(enabled: Boolean) {
        shouldSaveGoalVideos = enabled
        Log.d(TAG, "保存进球视频: $enabled")
    }

    /**
     * 设置是否保存未进球视频
     */
    fun setShouldSaveNoGoalVideos(enabled: Boolean) {
        shouldSaveNoGoalVideos = enabled
        Log.d(TAG, "保存未进球视频: $enabled")
    }

    /**
     * 清理旧视频文件
     */
    private fun cleanupOldVideoFiles() {
      //  val tempDir = File(appContext.getExternalFilesDir(Environment.DIRECTORY_MOVIES), "videos")
        val tempDir = storageManager.getTemporaryVideoDirectory()
        if (!tempDir?.exists()!!) return
        val files = tempDir.listFiles() ?: return
        // 按修改时间排序
        val sortedFiles = files.sortedBy { it.lastModified() }
        // 保留最新的5个文件，删除其余的
        if (sortedFiles.size > 25) {
            for (i in 0 until sortedFiles.size - 25) {
                sortedFiles[i].delete()
            }
        }

    }
    private fun cleanupOldMergeVideoFiles2() {
        val tempDir = storageManager.getFinalVideoDirectory()
        if (!tempDir?.exists()!!) return
        val files = tempDir.listFiles() ?: return
        // 按修改时间排序
        val sortedFiles = files.sortedBy { it.lastModified() }
        // 保留最新的5个文件，删除其余的
        if (sortedFiles.size > 200) {
            for (i in 0 until sortedFiles.size - 200) {
                sortedFiles[i].delete()
            }
        }

    }
    /**
     * 保存当前视频
     */
    private fun saveCurrentVideo2(isGoal: Boolean,shootEventRecord : ShootEventRecord) {
        Log.e(TAG, "didDetectShootEvent1111113" + "saveCurrentVideo: ${videoCacheList2.size}")
         // 如果缓存中有视频，处理最后一个片段
        if (videoCacheList2.isNotEmpty()) {
            VideoSegmentHelper.processLastSegment(
                context = appContext,
                segments = videoCacheList2,
                finalVideoUrl= finalVideoUrl.toString(),
                isGoal = isGoal,
                callback = { result ->
                    result?.let { filePath ->
                        // 保存文件路径
                        filePathSuccessBlock?.invoke(filePath,shootEventRecord)
                        Log.d(TAG, "didDetectShootEvent1111115视频保存成功: $filePath")
                    }
                }
            )
        }
    }
    var a=0
    fun addVideoList(path: String) {
     //   Log.e(TAG, "didDetectShootEvent1111112" + "addVideoList:${path} ${videoCacheList.size}")
        videoCacheList2.add(path)
        a++
        // 限制缓存文件数量
        if(a%20==0){
            cleanupOldVideoFiles()
        }
        if(a%45==0){
            cleanupOldMergeVideoFiles2()
        }
//        while (videoCacheList2.size > 20) {
//            val oldestFile = videoCacheList2.removeAt(0)
//            File(oldestFile).delete()
//        }
    }


    /**
    *保存投篮事件视频
    */
    fun saveShootEventVideo2(isGoal: Boolean,shootEventRecord : ShootEventRecord) {
        // 根据设置决定是否保存视频
        val shouldSave = if (isGoal) shouldSaveGoalVideos else shouldSaveNoGoalVideos
        if (!shouldSave) {
            Log.e(TAG, "didDetectShootEvent1111111跳过保存${if(isGoal) "进球" else "未进球"}事件视频（根据用户设置）")
            return
        }
        Log.e(TAG, "didDetectShootEvent1111111" + "保存${if(isGoal) "进球" else "未进球"}事件视频（根据用户设置）")
        // 延迟保存
//        videoHandler.postDelayed({
//            saveCurrentVideo(isGoal)
//        }, 2000) // 延迟1.5秒
       saveCurrentVideo2(isGoal,shootEventRecord)
    }
}

/**
 * 视频片段处理助手
 */
object VideoSegmentHelper {

    /**
     * 处理最后一个视频片段
     */
    fun processLastSegment(
        context: Context,
        segments: List<String>,
        finalVideoUrl:String,
        isGoal: Boolean,
        callback: (String?) -> Unit
    ) {
        Log.e("didDetectShootEvent1111118", "processLastSegment1")
        if (segments.isEmpty()) {
            callback(null)
            return
        }
        Log.e("didDetectShootEvent1111118", "processLastSegment2")
        // 获取最后一个片段
        val lastIndex = segments.size - 1
        val lastSegment = segments[lastIndex]

        // 在后台线程处理
        HandlerThread("VideoProcessThread").apply {
            start()
            Handler(looper).post {
                try {
                    // 检查时长
                    val duration = getVideoDuration(context, lastSegment)

                    // 如果时长≥10秒，直接使用
                    if (duration >= 10.0) {
                        callback(lastSegment)
                        return@post
                    }
                    // 需要补齐的时长
                    val neededSeconds = 10.0 - duration
                    Log.e("didDetectShootEvent1111118", "duration"+duration+" neededSeconds"+neededSeconds)

                    // 收集前面片段
                    val segmentsToMerge = mutableListOf<String>()
                    var collectedDuration = 0.0

                    // 从后往前遍历前面的片段
                    for (i in lastIndex-1 downTo 0) {
                        if (collectedDuration >= neededSeconds) break
                        val segmentPath = segments[i]
                        val segmentDuration = getVideoDuration(context, segmentPath)
                        Log.e("didDetectShootEvent1111118", "collectedDuration"+collectedDuration+" segmentDuration"+segmentDuration)

                        segmentsToMerge.add(0, segmentPath) // 按顺序添加到前面
                        collectedDuration += segmentDuration

                        if (collectedDuration >= neededSeconds) break
                    }
                    // 添加最后一个片段
                   segmentsToMerge.add(lastSegment)
                    Log.e("didDetectShootEvent1111118", "processLastSegment2"+segmentsToMerge)
                    val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
                    val outputFile = "${finalVideoUrl}/extended_last_${dateFormat.format(Date())}.mp4"
                    Log.e("didDetectShootEvent1111118", "outputFile"+outputFile)
                   mergeVideos(context,segmentsToMerge,outputFile, object : MergeListener {
                        override fun onMergeSuccess(outputPath: String) {
                            Log.e("didDetectShootEvent1111118", "mergeVideos合并成功: $outputPath")
                            callback(outputPath)
                        }

                       override fun onMergeFailed(exception: Exception) {
                           Log.e("didDetectShootEvent1111118", "mergeVideos合并失败"+ exception)
                       }

                       override fun onMergeCancelled() {
                           Log.e("didDetectShootEvent1111118", "mergeVideos合并取消onMergeCancelled")
                       }


                   })
                } catch (e: Exception) {
                    Log.e("didDetectShootEvent1111118", "mergeVideos处理失败: ${e.message}")
                    callback(lastSegment) // 返回原始文件作为备用
                }
            }
        }
    }





    @SuppressLint("WrongConstant")
    fun mergeVideos(context: Context, inputPaths: List<String>, outputPath: String, listener: MergeListener) {
        // 1. 检查输入文件是否为空
        if (inputPaths.isEmpty()) {
            listener.onMergeFailed(IllegalArgumentException("至少需要提供一个输入视频文件"))
            return
        }

        // 2. 验证所有输入文件都存在
        for (inputPath in inputPaths) {
            val file = File(inputPath)
            if (!file.exists() || !file.isFile) {
                listener.onMergeFailed(IllegalArgumentException("输入文件不存在: $inputPath"))
                return
            }
        }

        // 3. 确保输出目录存在
        val outputFile = File(outputPath)
        outputFile.parentFile?.mkdirs()

        try {
            // 4. 动态构建 filelist.txt 内容 (转义特殊字符)
            val fileListContent = inputPaths.joinToString("\n") {
                "file '${it.replace("'", "'\\''")}'" // 转义单引号
            }
            // 5. 创建临时文件存储 filelist.txt 内容
            val tempFileList = File(context.cacheDir, "ffmpeg_filelist_${System.currentTimeMillis()}.txt").apply {
                writeText(fileListContent)
            }

            // 6. 构建 FFmpeg 命令 (改进参数结构)
            // 注意：arrayOf 的每个参数必须单独传递
            val ffmpegCommand = arrayOf(
                "-y", // 覆盖输出文件
                "-f", "concat",
                "-safe", "0",
                "-i", tempFileList.absolutePath,
                "-c", "copy",
                outputPath
            )

            Log.e("didDetectShootEvent1111118FFmpegCommand", "执行命令: ${ffmpegCommand.joinToString(" ")}")

            // 7. 执行 FFmpeg 命令
            RxFFmpegInvoke.getInstance().runCommand(ffmpegCommand, object : IFFmpegListener {
                override fun onFinish() {
                    Log.e("didDetectShootEvent1111118VideoMerge", "视频合并成功: $outputPath")
                    // 确保完成后才删除临时文件
                    tempFileList.delete()
                    listener.onMergeSuccess(outputPath)
                }

                override fun onProgress(progress: Int, progressTime: Long) {
                    Log.e("didDetectShootEvent1111118VideoMerge", "合并进度: $progress% ($progressTime ms)")
                    // 可选: 更新进度给监听器
                }

                override fun onCancel() {
                    Log.e("didDetectShootEvent1111118VideoMerge", "合并取消")
                    tempFileList.delete()
                    listener.onMergeCancelled()
                }

                override fun onError(message: String) {
                    Log.e("didDetectShootEvent1111118VideoMerge", "合并错误: $message")
                    // 收集详细错误信息
                    collectExtraErrorInfo(tempFileList, inputPaths)
                    tempFileList.delete()
                    listener.onMergeFailed(Exception(message))
                }
            })

        } catch (e: Exception) {
            Log.e("didDetectShootEvent1111118VideoMerge", "合并过程异常", e)
            listener.onMergeFailed(e)
        }
    }

    // 辅助函数: 收集额外错误信息
    private fun collectExtraErrorInfo(tempFileList: File, inputPaths: List<String>) {
        // 1. 检查临时文件是否存在
        Log.e("didDetectShootEvent1111118ErrorDebug", "临时文件存在: ${tempFileList.exists()}")

        // 2. 检查临时文件内容
        try {
            val content = tempFileList.readText()
            Log.e("didDetectShootEvent1111118ErrorDebug", "临时文件内容:\n$content")
        } catch (e: Exception) {
            Log.e("didDetectShootEvent1111118ErrorDebug", "读取临时文件失败", e)
        }

        // 3. 检查输入文件权限
        inputPaths.forEachIndexed { i, path ->
            val file = File(path)
            Log.e("didDetectShootEvent1111118ErrorDebug", "输入文件 #$i: ${file.absolutePath}")
            Log.e("didDetectShootEvent1111118ErrorDebug", "  - 存在: ${file.exists()}")
            Log.e("didDetectShootEvent1111118ErrorDebug", "  - 可读: ${file.canRead()}")
            Log.e("didDetectShootEvent1111118ErrorDebug", "  - 大小: ${file.length()} 字节")
        }
    }

    // 扩展的监听器接口
    interface MergeListener {
        fun onMergeSuccess(outputPath: String)
        fun onMergeFailed(exception: Exception)
        fun onMergeCancelled()
    }
    /**
     * 获取视频时长
     */
    private fun getVideoDuration(context: Context, filePath: String): Double {
        val mediaMetadataRetriever = MediaMetadataRetriever()
        try {
            mediaMetadataRetriever.setDataSource(context, android.net.Uri.fromFile(File(filePath)))

            val durationStr = mediaMetadataRetriever.extractMetadata(
                MediaMetadataRetriever.METADATA_KEY_DURATION
            ) ?: "0"

            return durationStr.toLong() / 1000.0 // 转为秒
        } finally {
            mediaMetadataRetriever.release()
        }
    }

}