import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_logger.dart';
import 'package:utils_package/utils_package.dart';
import 'package:open_file/open_file.dart';

import '../../routes/app.dart';
import '../routes/route.dart';
import '../utils/FileDirPath.dart';
import 'router.dart';
import 'state.dart';
import 'package:path/path.dart' as path;

class WebviewLogic {
  final state = WebviewState();
  WebUri? uri;
  WebviewRouter? router;
  late final CookieManager cookieManager = CookieManager();

  void onInit(WebviewRouter router) {
    this.router = router;
  }

  void onReady() {}

  void onClose() {
    if (router?.closeHandle != null) {
      router?.closeHandle?.call(closeArgs);
    }
  }

  Future<bool> loadWebview() async {
    String http = router?.url ?? "";
    String? base = WxEnv.instance.webUrl;
    final domain = base?.split("://").last;
    if (router?.needBaseHttp == true) {
      if (http.indexOf("/") != 0) {
        http = "/$http";
      }
      http = "$base$http" ?? "";
    }
    uri = WebUri(http);
    // await cookieManager.setCookie(
    //     url: uri!,
    //     name: 'access_token',
    //     value: WxEnv.instance.currentToken ?? "");
    // await cookieManager.setCookie(
    //     url: uri!,
    //     name: 'thmim-refresh_token',
    //     value: WxEnv.instance.refreshToken ?? "");
    return true;
  }

  void reload() {
    state.webViewController?.reload();
  }

  dynamic closeArgs;

  void onWebViewCreated(InAppWebViewController controller) {
    state.webViewController = controller;
    controller.addJavaScriptHandler(
        handlerName: "closeWebView",
        callback: (args) {
          if (args.isNotEmpty) {
            closeArgs = args.first;
          }
          back();
        });
    controller.addJavaScriptHandler(
        handlerName: "navigateTo",
        callback: (args) {
          Completer<dynamic> completer = Completer();
          String url = args.first;
          // String? closeHandle;
          // if (args.length > 1) {
          //   closeHandle = args[1];
          // }
          String? title;
          if (args.length > 1) {
            title = args[1];
          }
          WebviewRouter router = WebviewRouter(
              url: url,
              showNavigationBar: title != null,
              title: title,
              closeHandle: (close) async {
                completer.complete(close);
                // if (closeHandle != null) {
                //   debug("closeHandle--------------");
                // String js =
                //     "console.log('返回回调-----');window.${closeHandle!}($close)";
                // final res = await state.webViewController
                //     ?.evaluateJavascript(source: js);
                // debug("closeHandle--------------$res");
                // }
              });
          AppPage.to(Routes.webview, arguments: router);
          return completer.future;
        });
    controller.addJavaScriptHandler(
        handlerName: "download",
        callback: (args) {
          download(args.first);
        });
    controller.addJavaScriptHandler(
        handlerName: "base64ToBitmap",
        callback: (args) {
          base64ToBitmap(args.first);
        });
    controller.addJavaScriptHandler(
        handlerName: "checkCameraPermission",
        callback: (args) async {
          final permission =
              await WxPermissionUtils.camera(openAppSetting: false);
          return permission;
        });
    controller.addJavaScriptHandler(
        handlerName: "copyToBoard",
        callback: (args) {
          Clipboard.setData(ClipboardData(text: args.first));
          WxLoading.showToast('复制成功，内容${args.first}');
        });
  }

  void onPageFinished() {
    if (state.loadState.value == WebviewLoadState.loading) {
      state.loadState.value = WebviewLoadState.success;
      if (state.refreshHandle != null) {
        state.refreshHandle?.call();
      }
    }
  }

  void onPageStarted() {
    state.loadState.value = WebviewLoadState.loading;
  }

  void onReceivedError() {
    state.loadState.value = WebviewLoadState.error;
  }

  void back() {
    if (router?.backHandle != null) {
      router?.backHandle?.call();
    } else {
      AppPage.back();
    }
  }

  void onRefresh() {
    // state.webViewController?.reload();
    state.loadState.value = WebviewLoadState.loading;
  }

  void base64ToBitmap(String base64) async {
    bool permission = false;
    if (Platform.isIOS) {
      permission = await WxPermissionUtils.addPhoto();
    } else {
      permission = await WxPermissionUtils.storage();
    }
    if (!permission) {
      WxLoading.showToast("没有存储权限");
      return;
    }
    Uint8List imageData = base64Decode(base64);
    final result = await ImageGallerySaver.saveImage(imageData, quality: 100);
    debug("base64ToBitmap---${result.toString()}");
    if (result["isSuccess"] == true) {
      WxLoading.showToast("保存成功");
    }
  }

  void download(String url) async {
    WxLoading.show();
    String? saveLocalPath = await FileDirPath().retryRequestPermission();
    if (saveLocalPath == null) {
      WxLoading.dismiss();
      WxLoading.showToast("没有存储权限");
      return;
    }
    String fileName = path.basename(url);
    String saveUrl = '$saveLocalPath/${fileName}';
    var res = await Api().doDownload(url, saveUrl);
    WxLoading.dismiss();
    if (res?.statusCode == 200) {
      OpenFile.open(saveUrl);
    }
  }
}
