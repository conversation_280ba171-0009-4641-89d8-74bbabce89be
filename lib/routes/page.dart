import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/inappwebview/AppWebViewH5Page.dart';
import 'package:shoot_z/pages/game/ai_report/ai_battle_report_page.dart';
import 'package:shoot_z/pages/game/comparison/comparison_binding.dart';
import 'package:shoot_z/pages/game/comparison/comparison_view.dart';
import 'package:shoot_z/pages/game/comparison/video_path/video_path_binding.dart';
import 'package:shoot_z/pages/game/comparison/video_path/video_path_view.dart';
import 'package:shoot_z/pages/game/details/view.dart';
import 'package:shoot_z/pages/game/player_report/composite_player_video/composite_player_video_binding.dart';
import 'package:shoot_z/pages/game/player_report/composite_player_video/composite_player_video_view.dart';
import 'package:shoot_z/pages/game/player_report/option_player_goal/option_player_goal_binding.dart';
import 'package:shoot_z/pages/game/player_report/option_player_goal/option_player_goal_view.dart';
import 'package:shoot_z/pages/game/unlock_data/competition_coupons/competition_coupons_binding.dart';
import 'package:shoot_z/pages/game/unlock_data/competition_coupons/competition_coupons_view.dart';
import 'package:shoot_z/pages/game/unlock_data/unlock_data_binding.dart';
import 'package:shoot_z/pages/game/unlock_data/unlock_data_view.dart';
import 'package:shoot_z/pages/login/bindPhone/bind_phone_page.dart';
import 'package:shoot_z/pages/tab1Home/featured_info/featured_info_binding.dart';
import 'package:shoot_z/pages/tab1Home/featured_info/featured_info_view.dart';
import 'package:shoot_z/pages/tab1Home/highlights/highlights_binding.dart';
import 'package:shoot_z/pages/tab1Home/highlights/highlights_view.dart';
import 'package:shoot_z/pages/tab1Home/short_videos/short_videos_view.dart';
import 'package:shoot_z/pages/tab1Home/short_videos2/short_videos2_view.dart';
import 'package:shoot_z/pages/tab1Home/video_player/video_player_binding.dart';
import 'package:shoot_z/pages/tab1Home/video_player/video_player_view.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/arena_details_binding.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/competition_list/arena_competition_list_page.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/competition_list/match_list_page.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/more_highlights/more_highlights_page.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/competition_detail_page.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/competition_sign_up_page.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/competition_teams_page.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/my_registration_page.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/schedule_home_page.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/composite_video/composite_video_binding.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/composite_video/composite_video_view.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/material_library/material_library_binding.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/material_library/material_library_view.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_goal/option_goal_binding.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_goal/option_goal_help/option_goal_help_binding.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_goal/option_goal_help/option_goal_help_view.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_goal/option_goal_view.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_site_binding.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_site_view.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/site_composite/shoot_goal/site_goal_binding.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/site_composite/shoot_goal/site_goal_view.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/site_composite/site_composite_video/site_composite_video_binding.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/site_composite/site_composite_video/site_composite_video_view.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/site_report/site_report_binding.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/site_report/site_report_view.dart';
import 'package:shoot_z/pages/login/loginCode/view.dart';
import 'package:shoot_z/pages/tab4PointsMall/address/address_view.dart';
import 'package:shoot_z/pages/tab4PointsMall/goods_info/goods_info_view.dart';
import 'package:shoot_z/pages/tab4PointsMall/points_exchange/points_exchange_binding.dart';
import 'package:shoot_z/pages/tab4PointsMall/points_exchange/points_exchange_view.dart';
import 'package:shoot_z/pages/tab4PointsMall/points_goods_type/points_goods_type_view.dart';
import 'package:shoot_z/pages/tab4PointsMall/points_mall_view.dart';
import 'package:shoot_z/pages/tab5Mine/about/view.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/career_highlights_home_page.dart';
import 'package:shoot_z/pages/tab5Mine/changePhone/bind_new_phone_page.dart';
import 'package:shoot_z/pages/tab5Mine/changePhone/origin_phone_verification_page.dart';
import 'package:shoot_z/pages/tab5Mine/coupons/coupons_binding.dart';
import 'package:shoot_z/pages/tab5Mine/coupons/coupons_view.dart';
import 'package:shoot_z/pages/tab5Mine/inviteCode/logic.dart';
import 'package:shoot_z/pages/tab5Mine/kf/view.dart';
import 'package:shoot_z/pages/tab5Mine/message/message_info/message_info_view.dart';
import 'package:shoot_z/pages/tab5Mine/message/message_list_view.dart';
import 'package:shoot_z/pages/tab5Mine/message/message_type/message_type_view.dart';
import 'package:shoot_z/pages/tab5Mine/orders/orders_binding.dart';
import 'package:shoot_z/pages/tab5Mine/orders/orders_view.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/points_details_binding.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_details/points_details_view.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_help/points_help_binding.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_help/points_help_view.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/battle_detail_page.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/battle_detail_team_page.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/create_battle_page.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/my_battle_page.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/purpose_history_page.dart';
import 'package:shoot_z/pages/tab5Mine/teams/add_team/add_team_binding.dart';
import 'package:shoot_z/pages/tab5Mine/teams/add_team/add_team_view.dart';
import 'package:shoot_z/pages/game/player_report/player_report_binding.dart';
import 'package:shoot_z/pages/game/player_report/player_report_view.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/audit_add_team/audit_add_team_binding.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/audit_add_team/audit_add_team_view.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/players/players_binding.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/players/players_view.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/team_info_binding.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/team_info_view.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/team_photos/team_photos_view.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_list_binding.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_list_view.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/update_member/update_team_member_binding.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/update_member/update_team_member_view.dart';
import 'package:shoot_z/pages/tab5Mine/teams/videos/videos_binding.dart';
import 'package:shoot_z/pages/tab5Mine/teams/videos/videos_view.dart';
import 'package:shoot_z/pages/tab5Mine/vip/view.dart';
import 'package:shoot_z/pages/tab5Mine/edit/view.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_binding.dart';
import 'package:shoot_z/pages/tab4PointsMall/points/points_view.dart';
import 'package:shoot_z/pages/tab5Mine/sssy/view.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/arena_details_view.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/ranking_rules.dart';
import 'package:shoot_z/pages/tab3Create/creation_way_binding.dart';
import 'package:shoot_z/pages/tab3Create/creation_way_view.dart';
import 'package:shoot_z/pages/tab3Create/place/search/view.dart';
import 'package:shoot_z/pages/tab3Create/place/view.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_binding.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_view.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_info/selfie_shot_info_binding.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_info/selfie_shot_info_view.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_report/selfie_shot_report_binding.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_report/selfie_shot_report_view.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_videos/selfie_shot_videos_binding.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot_videos/selfie_shot_videos_view.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/shoot_composite_video/shoot_composite_video_binding.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/shoot_composite_video/shoot_composite_video_view.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/shoot_goal/shoot_goal_binding.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/shoot_goal/shoot_goal_view.dart';
import '../inappwebview/AppWebViewPage.dart';
import '../pages/game/report/view.dart';
import '../pages/tab1Home/highlights/video/view.dart';
import '../pages/login/loading/view.dart';
import '../pages/login/inviteCode/view.dart';
import '../pages/login/view.dart';
import '../pages/tab5Mine/inviteCode/view.dart';
import '../pages/tab5Mine/settings/view.dart';
import '../pages/tab1Home/rankings_new/view.dart';
import '../pages/tab/logic.dart';
import '../pages/tab/view.dart';
import 'error.dart';
import 'route.dart';

class Pages {
  static List<GetPage> pages() {
    List<GetPage> pages = [
      GetPage(
        name: Routes.highlightsPage,
        page: () => const HighlightsPage(),
        binding: HighlightsBinding(),
      ),
      GetPage(
        name: Routes.featuredInfoPage,
        page: () => FeaturedInfoPage(),
        binding: FeaturedInfoBinding(),
      ),
      GetPage(
        name: Routes.shotVideosPage,
        page: () => const VideoFeedScreen(),
      ),
      GetPage(
        name: Routes.shotVideos2Page,
        page: () => const VideoFeed2Screen(),
      ),
      GetPage(
        name: Routes.webview,
        page: () {
          return AppWebviewPage(router: Get.arguments);
        },
      ),
      GetPage(
        name: Routes.webviewh5,
        page: () {
          return AppWebViewH5Page(router: Get.arguments);
        },
      ),
      GetPage(
        name: Routes.loading,
        page: () => const LoadingPage(),
        // page: () => TestPage(),
        transition: Transition.noTransition,
      ),
      GetPage(
        name: Routes.tab,
        page: () => const TabPage(),
        transition: Transition.noTransition,
        binding: BindingsBuilder(() {
          Get.lazyPut<TabLogic>(() => TabLogic());
        }),
      ),
      GetPage(
        name: Routes.login,
        page: () => const LoginPage(),
        transition: Transition.downToUp, // 单独设置从下往上动画
      ),
      GetPage(
        name: Routes.loginCode,
        page: () => const LoginCodePage(),
      ),
      GetPage(
        name: Routes.oringinPhoneVerification,
        page: () => OriginPhoneVerificationPage(),
      ),
      GetPage(
        name: Routes.bindNewPhone,
        page: () => BindNewPhonePage(),
      ),
      GetPage(
        name: Routes.bindPhonePage,
        page: () => BindPhonePage(),
      ),
      GetPage(
        name: Routes.highlightsVideo,
        page: () => const HighlightsVideoPage(),
        transition: Transition.downToUp,
      ),
      GetPage(
        name: Routes.videos,
        page: () => const VideosPage(),
        binding: VideosBinding(),
        transition: Transition.downToUp,
      ),
      GetPage(
        name: Routes.videoPath,
        page: () => const VideoPathPage(),
        binding: VideoPathBinding(),
        transition: Transition.downToUp,
      ),
      GetPage(
        name: Routes.videoPlayerPage,
        page: () => const VideoPlayerPage(),
        binding: VideoPlayerBinding(),
        transition: Transition.downToUp,
      ),
      GetPage(
        name: Routes.modifyInfo,
        page: () => const MineEditPage(),
      ),
      GetPage(
        name: Routes.place,
        page: () => const PlacePage(),
        transition: Transition.downToUp,
      ),
      GetPage(
          name: Routes.creationWayPage,
          page: () => const CreationWayPage(),
          transition: Transition.downToUp,
          binding: CreationWayBinding()),
      GetPage(
          name: Routes.selfieShotPage,
          page: () => const SelfieShotPage(),
          binding: SelfieShotBinding()),
      GetPage(
          name: Routes.selfieShotReportPage,
          page: () => SelfieShotReportPage(),
          binding: SelfieShotReportBinding()),
      GetPage(
          name: Routes.selfieShotVideosPage,
          page: () => SelfieShotVideosPage(),
          binding: SelfieShotVideosBinding()),
      GetPage(
          name: Routes.selfieShotInfoPage,
          page: () => SelfieShotInfoPage(),
          binding: SelfieShotInfoBinding()),
      GetPage(
        name: Routes.arenaDetailsPage,
        page: () => ArenaDetailsPage(),
        binding: ArenaDetailsBinding(),
      ),
      GetPage(
        name: Routes.moreHighlightsPage,
        page: () => MoreHighlightsPage(),
      ),
      GetPage(
        name: Routes.arenaCompetitionListPage,
        page: () => ArenaCompetitionListPage(),
      ),
      GetPage(
        name: Routes.matchListPage,
        page: () => MatchListPage(),
      ),
      GetPage(
        preventDuplicates: true, // 页面级别防重复
        name: Routes.optionGoalPage,
        page: () => OptionGoalPage(),
        binding: OptionGoalBinding(),
      ),
      GetPage(
        name: Routes.optionPlayerGoalPage,
        page: () => OptionPlayerGoalPage(),
        binding: OptionPlayerGoalBinding(),
      ),
      GetPage(
        name: Routes.optionGoalHelpPage,
        page: () => OptionGoalHelpPage(),
        binding: OptionGoalHelpBinding(),
      ),
      GetPage(
        name: Routes.materialLibraryPage,
        page: () => MaterialLibraryPage(),
        binding: MaterialLibraryBinding(),
        transition: Transition.downToUp,
      ),
      GetPage(
        name: Routes.compositeVideoPage,
        page: () => CompositeVideoPage(),
        binding: CompositeVideoBinding(),
      ),
      GetPage(
        name: Routes.compositePlayerVideoPage,
        page: () => CompositePlayerVideoPage(),
        binding: CompositePlayerVideoBinding(),
      ),
      GetPage(
        name: Routes.optionSitePage,
        page: () => OptionSitePage(),
        binding: OptionSiteBinding(),
      ),
      GetPage(
        preventDuplicates: true, // 页面级别防重复
        name: Routes.siteReportPage,
        page: () => const SiteReportPage(),
        binding: SiteReportBinding(),
      ),
      GetPage(
        name: Routes.pointsPage,
        page: () => PointsPage(),
        binding: PointsBinding(),
      ),
      GetPage(
        name: Routes.pointsMallPage,
        page: () => PointsMallPage(
          key: Key("222"),
          type: 1,
        ),
      ),
      GetPage(
        name: Routes.pointsHelpPage,
        page: () => PointsHelpPage(),
        binding: PointsHelpBinding(),
      ),
      GetPage(
        name: Routes.pointsDetailsPage,
        page: () => const PointsDetailsPage(),
        binding: PointsDetailsBinding(),
      ),
      GetPage(
        name: Routes.comparisonPage,
        page: () => ComparisonPage(),
        binding: ComparisonBinding(),
      ),
      GetPage(
        name: Routes.shootCompositeVideoPage,
        page: () => ShootCompositeVideoPage(),
        binding: ShootCompositeVideoBinding(),
      ),
      GetPage(
        name: Routes.siteGoalPage,
        page: () => SiteGoalPage(),
        binding: SiteGoalBinding(),
      ),
      GetPage(
        name: Routes.siteCompositeVideoPage,
        page: () => SiteCompositeVideoPage(),
        binding: SiteCompositeVideoBinding(),
      ),
      GetPage(
        name: Routes.shootGoalPage,
        page: () => ShootGoalPage(),
        binding: ShootGoalBinding(),
      ),
      GetPage(
        name: Routes.pointsExchangePage,
        page: () => PointsExchangePage(),
        binding: PointsExchangeBinding(),
      ),
      GetPage(
        name: Routes.teamListPage,
        page: () => TeamListPage(),
        binding: TeamListBinding(),
      ),
      GetPage(
        name: Routes.auditAddTeamPage,
        page: () => AuditAddTeamPage(),
        binding: AuditAddTeamBinding(),
      ),
      GetPage(
        name: Routes.playersPage,
        page: () => PlayersPage(),
        binding: PlayersBinding(),
      ),
      GetPage(
        name: Routes.teamPhotosPage,
        page: () => TeamPhotosPage(),
        binding: PlayersBinding(),
      ),
      GetPage(
        name: Routes.updateTeamMemberPage,
        page: () => UpdateTeamMemberPage(),
        binding: UpdateTeamMemberBinding(),
      ),
      GetPage(
        name: Routes.teamInfoPage,
        page: () => TeamInfoPage(),
        binding: TeamInfoBinding(),
      ),
      GetPage(
        name: Routes.messageTypePage,
        page: () => MessageTypePage(),
      ),
      GetPage(
        name: Routes.goodsInfoPage,
        page: () => GoodsInfoPage(),
        binding: TeamInfoBinding(),
      ),
      GetPage(
        name: Routes.addressPage,
        page: () => AddressPage(),
        binding: TeamInfoBinding(),
      ),
      GetPage(
        name: Routes.messageListPage,
        page: () => MessageListPage(),
      ),
      GetPage(
        name: Routes.messageInfoPage,
        page: () => MessageInfoPage(),
      ),
      GetPage(
        name: Routes.addTeamPage,
        page: () => AddTeamPage(),
        binding: AddTeamBinding(),
      ),
      GetPage(
        name: Routes.playerReportPage,
        page: () => PlayerReportPage(),
        binding: PlayerReportBinding(),
      ),
      GetPage(
        name: Routes.unlockDataPage,
        page: () => UnlockDataPage(),
        binding: UnlockDataBinding(),
      ),
      GetPage(
        name: Routes.competitionCouponsPage,
        page: () => CompetitionCouponsPage(),
        binding: CompetitionCouponsBinding(),
      ),
      GetPage(
        name: Routes.ordersPage,
        page: () => OrdersPage(),
        binding: OrdersBinding(),
      ),
      GetPage(
        name: Routes.couponsPage,
        page: () => CouponsPage(),
        binding: CouponsBinding(),
      ),
      GetPage(
        name: Routes.placeSearch,
        page: () => const SearchPage(),
      ),
      GetPage(
        name: Routes.sssy,
        page: () => const SssyPage(),
      ),
      GetPage(
        name: Routes.vipPage,
        page: () => const VipPage(),
      ),
      GetPage(
        name: Routes.inputInviteCodePage,
        page: () => const InputInviteCodePage(),
      ),
      GetPage(
        name: Routes.inviteCodePage,
        page: () => const InviteCodePage(),
        binding: BindingsBuilder(() {
          Get.lazyPut<InviteCodeLogic>(() => InviteCodeLogic());
        }),
      ),
      GetPage(
        name: Routes.kfPage,
        page: () => const KfPage(),
      ),
      GetPage(
        name: Routes.settingsPage,
        page: () => SettingsPage(),
      ),
      GetPage(
        name: Routes.aboutPage,
        page: () => const AboutPage(),
      ),
      GetPage(
        name: Routes.gameDetailsPage,
        page: () => GameDetailsPage(),
      ),
      GetPage(
        name: Routes.teamReportPage,
        page: () => TeamReportPage(),
      ),
      GetPage(
        name: Routes.rankingsPage,
        page: () => const RankingsPage(),
      ),
      GetPage(
        name: Routes.pointsGoodsTypePage,
        page: () => const PointsGoodsTypePage(),
      ),
      GetPage(
        name: Routes.rankingRulesPage,
        page: () => const RankingRules(),
      ),
      GetPage(
        name: Routes.battleDetailPage,
        page: () => BattleDetailPage(),
      ),
      GetPage(
        name: Routes.battleDetailTeamPage,
        page: () => BattleDetailTeamPage(),
      ),
      GetPage(
        name: Routes.createBattlePage,
        page: () => CreateBattlePage(),
      ),
      GetPage(
        name: Routes.purposeHistoryPage,
        page: () => PurposeHistoryPage(),
      ),
      GetPage(
        name: Routes.myBattlePage,
        page: () => MyBattlePage(),
      ),
      GetPage(
        name: Routes.scheduleHomePage,
        page: () => ScheduleHomePage(),
      ),
      GetPage(
        name: Routes.competitionDetailPage,
        page: () => CompetitionDetailPage(),
      ),
      GetPage(
        name: Routes.competitionSignUpPage,
        page: () => CompetitionSignUpPage(),
      ),
      GetPage(
        name: Routes.myRegistrationPage,
        page: () => MyRegistrationPage(),
      ),
      GetPage(
        name: Routes.competitionTeamsPage,
        page: () => CompetitionTeamsPage(),
      ),
      GetPage(
        name: Routes.careerHighlightsHomePage,
        page: () => CareerHighlightsHomePage(),
      ),
      GetPage(
        name: Routes.aiBattleReportpage,
        page: () => AiBattleReportPage(),
      )
    ];
    return pages;
  }

  static GetPage notfound = GetPage(
    name: Routes.notfound,
    page: () => const AppPageError(),
  );
}
