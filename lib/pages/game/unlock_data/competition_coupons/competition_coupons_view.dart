import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/game_coupons_model.dart';
import 'package:shoot_z/pages/game/unlock_data/competition_coupons/competition_coupons_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///解锁比赛数据   赛事优惠券列表
class CompetitionCouponsPage extends StatelessWidget {
  CompetitionCouponsPage({super.key});

  final logic = Get.put(CompetitionCouponsLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.option_coupon + "${logic.money.value}"),
      ),
      body: Obx(() {
        return SmartRefresher(
          controller: logic.refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () {
            logic.getdataList(controller: logic.refreshController);
          },

          physics: const AlwaysScrollableScrollPhysics(),
          //  physics: const NeverScrollableScrollPhysics(),
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty && logic.expiredDataList.isEmpty
                  ? SizedBox(
                      height: 480.w,
                      child: myNoDataView(
                        context,
                        msg: S.current.no_coupons,
                        imagewidget: WxAssets.images.couponsNodata
                            .image(width: 105.w, height: 89.w),
                      ))
                  : ListView(
                      children: [
                        ListView.separated(
                          scrollDirection: Axis.vertical,
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: logic.dataList.length,
                          itemBuilder: (context, position) {
                            return _listItemWidget(
                                logic.dataList[position], position);
                          },
                          separatorBuilder: (BuildContext context, int index) {
                            return SizedBox(
                              height: 15.w,
                            );
                          },
                        ),
                        if (logic.expiredDataList.isNotEmpty)
                          GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              logic.isOpenExpired.value =
                                  !logic.isOpenExpired.value;
                            },
                            child: Center(
                              child: Container(
                                height: 52.w,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      S.current.coupon_expired(
                                          logic.expiredDataList.length),
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 12.sp,
                                          color: Colours.color5C5C6E),
                                    ),
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                    logic.isOpenExpired.value
                                        ? const Icon(Icons.arrow_drop_down,
                                            color: Colours.white)
                                        : const Icon(
                                            Icons.arrow_drop_up,
                                            color: Colours.white,
                                          )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        if (logic.expiredDataList.isNotEmpty &&
                            logic.isOpenExpired.value)
                          ListView.separated(
                            scrollDirection: Axis.vertical,
                            // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                            shrinkWrap: true,
                            padding: EdgeInsets.only(bottom: 40.w),
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: logic.expiredDataList.length,
                            itemBuilder: (context, position) {
                              return _expiredListItemWidget(
                                  logic.expiredDataList[position], position);
                            },
                            separatorBuilder:
                                (BuildContext context, int index) {
                              return SizedBox(
                                height: 15.w,
                              );
                            },
                          ),
                      ],
                    ),
        );
      }),
      bottomNavigationBar: Obx(() {
        return logic.checkIndex.value >= logic.dataList.length
            ? const SizedBox()
            : Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                    color: Colours.color0F0F16,
                    border: Border(
                        top: BorderSide(
                      width: 1,
                      color: Colours.color282735,
                    ))),
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () async {},
                  child: Container(
                    height: 50.w,
                    width: double.infinity,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(
                        left: 20.w, right: 20.w, bottom: 20.w, top: 10.w),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                              text: S.current.coupon_tips,
                              style: TextStyle(
                                  color: Colours.color5C5C6E,
                                  fontSize: 12.sp,
                                  height: 1,
                                  fontWeight: FontWeight.w400),
                              children: <InlineSpan>[
                                TextSpan(
                                    text: "\t￥",
                                    style: TextStyle(
                                        color: Colours.white,
                                        fontSize: 12.sp,
                                        height: 1,
                                        fontWeight: FontWeight.w600)),
                                TextSpan(
                                    text: "${logic.decreaseMoney}",
                                    style: TextStyle(
                                        color: Colours.white,
                                        fontSize: 22.sp,
                                        height: 1,
                                        fontWeight: FontWeight.w600)),
                              ]),
                        ),
                        const Spacer(),
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            logic.sureCoupon();
                          },
                          child: Container(
                            height: 42.w,
                            width: 150.w,
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(right: 4.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(21.r),
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF
                                ],
                                begin: Alignment.bottomLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: Text(
                              S.current.sure,
                              style: TextStyles.display16.copyWith(
                                  fontSize: 14.sp,
                                  color: Colours.white,
                                  fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
      }),
    );
  }

  /// 构建列表项
  Widget _listItemWidget(GameCouponsModel item, int index) {
    return Obx(() {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () => logic.checkCoupon(index),
        child: Container(
          margin: EdgeInsets.only(left: 15.w, right: 15.w),
          height: 120.w,
          padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 15.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.r),
              color: Colours.color191921,
              image: const DecorationImage(
                  image: AssetImage("assets/images/coupons_bg.png"),
                  fit: BoxFit.fill)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.name ?? "",
                          style: TextStyles.regular.copyWith(
                              fontSize: 16.sp,
                              color: Colours.color191921,
                              fontWeight: FontWeight.w600),
                        ),
                        SizedBox(
                          height: 10.w,
                        ),
                        Text(
                          item.description ?? "",
                          style: TextStyles.regular.copyWith(
                              fontSize: 12.sp,
                              color: Colours.colorA8A8BC,
                              fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                            text: item.type == 3
                                ? (double.parse(item.discount ?? "0") * 10)
                                    .toStringAsFixed(1)
                                : item.type == 2
                                    ? "￥"
                                    : item.type == 1
                                        ? S.current.Free_admission
                                        : "", //type	integer1 次数券；2 金额抵扣券；3 折扣券
                            style: TextStyle(
                                color: Colours.colorA44EFF,
                                fontSize: item.type == 2 ? 12.sp : 30.sp,
                                height: 2,
                                fontWeight: FontWeight.normal),
                            children: <InlineSpan>[
                              TextSpan(
                                  text: item.type == 3
                                      ? S.current.fold
                                      : item.type == 2
                                          ? item.price
                                          : "",
                                  style: TextStyle(
                                      color: Colours.colorA44EFF,
                                      fontSize: item.type == 2 ? 22.sp : 12.sp,
                                      height: 2,
                                      fontWeight: FontWeight.normal)),
                            ]),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 8.w),
                        child: logic.checkIndex.value == index
                            ? MyImage(
                                "check_on_3.png",
                                width: 16.w,
                                height: 16.w,
                                isAssetImage: true,
                              )
                            : MyImage(
                                "check_on_3_wihte.png",
                                width: 16.w,
                                height: 16.w,
                                isAssetImage: true,
                                imageColor: Colours.color5C5C6E,
                                radius: 8.w,
                              ),
                      ),
                    ],
                  ),
                ],
              ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                height: 40.w,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    WxAssets.images.couponsTime
                        .image(width: 14.w, height: 14.w),
                    SizedBox(
                      width: 5.w,
                    ),
                    Expanded(
                      child: Text(
                        ((item.rangeType ?? 0) == 1)
                            ? S.current.long_effective
                            : (item.startTime ?? "") == ""
                                ? "${(item.endTime ?? "").length > 16 ? (item.endTime ?? "").substring(0, 16) : (item.endTime ?? "")}到期"
                                : "${(item.startTime ?? "").length > 16 ? (item.startTime ?? "").substring(0, 16) : (item.startTime ?? "")}\t至\t${(item.endTime ?? "").length > 16 ? (item.endTime ?? "").substring(0, 16) : (item.endTime ?? "")}",
                        style: TextStyles.regular.copyWith(
                          fontSize: 10.sp,
                          color: Colours.color5C5C6E,
                        ),
                        maxLines: 1,
                      ),
                    ),
                    if ((item.remainingDays ?? 0) != 0)
                      Text(
                        S.current.coupons_have_days(item.remainingDays ?? 0),
                        style: TextStyles.regular.copyWith(
                          fontSize: 10.sp,
                          color: Colours.color5C5C6E,
                        ),
                        maxLines: 1,
                      ),
                    SizedBox(
                      width: 15.w,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 过期的优惠券
  Widget _expiredListItemWidget(GameCouponsModel item, int index) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      height: 144.w,
      padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colours.color191921,
          image: const DecorationImage(
              image: AssetImage("assets/images/coupons_bg2.png"),
              fit: BoxFit.fill)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 16.sp,
                          color: Colours.color5C5C6E,
                          fontWeight: FontWeight.w600),
                    ),
                    SizedBox(
                      height: 10.w,
                    ),
                    Text(
                      item.description ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          color: Colours.color5C5C6E,
                          fontWeight: FontWeight.w400),
                    ),
                  ],
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                        text: item.type == 3
                            ? (double.parse(item.discount ?? "0") * 10)
                                .toStringAsFixed(1)
                            : item.type == 2
                                ? "￥"
                                : item.type == 1
                                    ? S.current.Free_admission
                                    : "", //type	integer1 次数券；2 金额抵扣券；3 折扣券
                        style: TextStyle(
                            color: Colours.color5C5C6E,
                            fontSize: item.type == 2 ? 12.sp : 30.sp,
                            height: 2,
                            fontWeight: FontWeight.normal),
                        children: <InlineSpan>[
                          TextSpan(
                              text: item.type == 3
                                  ? S.current.fold
                                  : item.type == 2
                                      ? item.price
                                      : "",
                              style: TextStyle(
                                  color: Colours.color5C5C6E,
                                  fontSize: item.type == 2 ? 30.sp : 12.sp,
                                  height: 2,
                                  fontWeight: FontWeight.normal)),
                        ]),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 8.w),
                    child: MyImage(
                      "check_on_3_wihte.png",
                      width: 16.w,
                      height: 16.w,
                      isAssetImage: true,
                      imageColor: Colours.color5C5C6E,
                      radius: 8.w,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const Spacer(),
          SizedBox(
            width: double.infinity,
            height: 61.w,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    MyImage(
                      "coupons_time.png",
                      width: 14.w,
                      height: 14.w,
                      isAssetImage: true,
                      imageColor: Colours.color5C5C6E,
                      radius: 8.w,
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Expanded(
                      child: Text(
                        ((item.rangeType ?? 0) == 1)
                            ? S.current.long_effective
                            : (item.startTime ?? "") == ""
                                ? "${(item.endTime ?? "").length > 16 ? (item.endTime ?? "").substring(0, 16) : (item.endTime ?? "")}到期"
                                : "${(item.startTime ?? "").length > 16 ? (item.startTime ?? "").substring(0, 16) : (item.startTime ?? "")}\t至\t${(item.endTime ?? "").length > 16 ? (item.endTime ?? "").substring(0, 16) : (item.endTime ?? "")}",
                        style: TextStyles.regular.copyWith(
                          fontSize: 10.sp,
                          color: Colours.color5C5C6E,
                        ),
                        maxLines: 1,
                      ),
                    ),
                    if ((item.remainingDays ?? 0) != 0)
                      Text(
                        S.current.coupons_have_days(item.remainingDays ?? 0),
                        style: TextStyles.regular.copyWith(
                          fontSize: 10.sp,
                          color: Colours.colorFFF280,
                        ),
                        maxLines: 1,
                      ),
                    SizedBox(
                      width: 15.w,
                    ),
                  ],
                ),
                SizedBox(
                  height: 10.w,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 2.w,
                    ),
                    MyImage(
                      "tips.png",
                      width: 12.w,
                      height: 12.w,
                      isAssetImage: true,
                      imageColor: Colours.color5C5C6E,
                      radius: 8.w,
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Expanded(
                      child: Text(
                        S.current
                            .coupon_expired_reason(item.disabledReason ?? ""),
                        style: TextStyles.regular.copyWith(
                          fontSize: 10.sp,
                          color: Colours.white,
                        ),
                        maxLines: 1,
                      ),
                    ),
                    if ((item.remainingDays ?? 0) != 0)
                      Text(
                        S.current.coupons_have_days(item.remainingDays ?? 0),
                        style: TextStyles.regular.copyWith(
                          fontSize: 10.sp,
                          color: Colours.colorFFF280,
                        ),
                        maxLines: 1,
                      ),
                    SizedBox(
                      width: 15.w,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
