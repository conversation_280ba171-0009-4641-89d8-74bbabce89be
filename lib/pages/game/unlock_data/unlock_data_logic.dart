import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/game/details/models/match_pay_info_model.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item1/unlock_data_item_logic1.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item2/unlock_data_item_logic2.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item3/unlock_data_item_logic3.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/event_bus.dart';

class UnlockDataLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  TabController? tabController;
  var tabbarIndex = 0.obs;
  var type = 0.obs;
  var matchId = "".obs;
  var playerId = "".obs;
  var teamId = "".obs;
  var showType = "".obs; //1不展示个人解锁  0展示
  final logic1 = Get.put(UnlockDataItemLogic1());
  final logic2 = Get.put(UnlockDataItemLogic2());
  final logic3 = Get.put(UnlockDataItemLogic3());
  StreamSubscription? paySubscription;

  @override
  void onInit() {
    super.onInit();

    type.value = Get.arguments["type"];
    matchId.value = Get.arguments["matchId"];
    showType.value = Get.arguments["showType"] ?? "";
    log(" showTypevalue=${showType.value}");
    if (showType.value != "1") {
      tabController = TabController(length: 3, vsync: this);
    } else {
      tabController = TabController(length: 2, vsync: this);
    }
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
      },
    );
    if (showType.value != "1") {
      switchTab(2);
    }
    if (type.value == 2) {
      playerId.value = Get.arguments["playerId"];
      teamId.value = Get.arguments["teamId"];
    }
    if (type.value == 1) {
      teamId.value = Get.arguments["teamId"];
    }

    paySubscription = BusUtils.instance.on((event) async {
      if (event.key == EventBusKey.unlockResult) {
        if (event.action == true) {
          WxLoading.showToast(S.current.unlock_successful);
          AppPage.back(result: "1");
          //getPayInfo();
        }
      }
    });
    getPayInfo();
  }

  @override
  void onClose() {
    super.onClose();
    paySubscription?.cancel();
  }

  void switchTab(index) {
    tabbarIndex.value = index;
    tabController?.animateTo(index);
  }

  getPayInfo() async {
    if (!UserManager.instance.isLogin) return true;
    final res = await Api().get(ApiUrl.getMatchIdPayInfo(matchId.value));
    if (res.isSuccessful()) {
      var payInfo = MatchPayInfoModel.fromJson(res.data);
      log("message=${jsonEncode(res.data)}");
      logic1.setMatchPayInfoModel(payInfo);
      logic2.setMatchPayInfoModel(payInfo, teamId.value);
      logic3.setMatchPayInfoModel(payInfo, playerId.value);
    }
  }
}
