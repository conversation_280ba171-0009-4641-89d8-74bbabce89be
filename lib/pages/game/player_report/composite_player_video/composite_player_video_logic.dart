import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/option_player_goal_model.dart';
import 'package:shoot_z/network/model/user_is_new_model.dart';
import 'package:shoot_z/network/model/videos_used_shots_model.dart';
import 'package:shoot_z/pages/game/player_report/composite_player_video/composite_player_video_state.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/dialo_view.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';

class CompositePlayerVideoLogic extends GetxController
    with WidgetsBindingObserver, GetSingleTickerProviderStateMixin {
  final CompositePlayerVideoLogicState state = CompositePlayerVideoLogicState();
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;

  VideoController videoController = VideoController();
  var arenaID = 0.obs;
  var isLoadVideo = false.obs;
  var isVip = false.obs;
  var isFrist = true.obs;
  var videosUsedShotsModel = VideosUsedShotsModel().obs; //判断是否有会员，优惠券，是否弹窗等
  // var videostate = 0.obs; //0初始 1播放  2暂停  3完成
//侧面 cameraIndex = 0  全景  cameraIndex = 126
  var dataList = <OptionPlayerGoalModelVideos>[].obs;
  @override
  Future<void> onInit() async {
    super.onInit();
    arenaID.value = Get.arguments["arenaID"] as int; //39 ??
    var list2 = Get.arguments["list"]; //39 ??
    dataList.assignAll(list2);

    WidgetsBinding.instance.addObserver(this);
    state.rememberOption.value =
        await WxStorage.instance.getString("rememberOption") ?? "0";
    if (state.rememberOption.value == "0") {
      state.compositeOption1.value =
          await WxStorage.instance.getInt("compositeOption1") ??
              0; //单片时长  0  5s    1  10s
      state.compositeOption2.value =
          await WxStorage.instance.getStringList("compositeOption2") ??
              ["1", "0", "1", "0"]; //视频效果与个性化 0选中  1选中 多选
      // state.compositeOption3.value =
      //     await WxStorage.instance.getInt("compositeOption3") ??
      //         1; //视角 0仅侧面  1全部视角
      // state.compositeOption4.value =
      //     await WxStorage.instance.getInt("compositeOption4") ??
      //         0; //移除所选的球 0是  1否); //移除所选的球 0是  1否
    }
    ever(isVip, (value) {
      videoController.showWatermark(!value);
    });
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    isVip.value = await getVideosUsedShots2(arenaID.value, type: 1);
    if (dataList.isNotEmpty) {
      changeVideoIndex(0);
      getDaoGoalList2();
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      dataFag.refresh();
    }
    if ((videosUsedShotsModel.value.couponExpireSoonCount ?? 0) > 0) {
      showDateDialog2(Get.context!);
    }
  }

  //查询用户已使用的片段以及是否vip
  Future<bool> getVideosUsedShots2(var arenaId, {int type = 0}) async {
    var param = {
      'arenaId': arenaId,
    };
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.show();
    }
    final res =
        await Api().get(ApiUrl.getVideosUsedShots, queryParameters: param);
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      log("getVideosUsedShots=${res.data["vip"]}");
      //{vip: false, shotCount: 0, remainingCount: 50, shots: null}
      //是否vip      shotCount已经使用的次数    remainingCount 剩余次数  Shots今日合成进球id
      videosUsedShotsModel.value = VideosUsedShotsModel.fromJson(res.data);
      if (!((videosUsedShotsModel.value.vip ?? false))) {
        return false;
      } else {
        return true;
      }
    } else {
      if (type == 0) {
        WxLoading.showToast(res.message);
      }
      return false;
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {}

  reloadVideo(String videoUrl) async {
    if (videoUrl.isEmpty) {
      log("betterPlayer-message11=$videoUrl");
      return;
    }
    videoController.setData(videoPath: videoUrl, showWatermark: !isVip.value);
  }

  @override
  void onClose() {
    //isVip.close();
    videoController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  //改变选择视频index
  void changeVideoIndex(int index) {
    log("message11=1");
    state.indexVideo.value = index;

    if (state.indexVideo.value != 9999) {
      reloadVideo(dataList[index].videoPath ?? "");
    }
  }

  //查询用户已使用的片段以及是否vip
  getVideosUsedShots(var arenaId) async {
    var param = {
      'arenaId': arenaId,
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res = await Api().get(ApiUrl.getVideosUsedShots,
        queryParameters: param, showError: false);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      log("getVideosUsedShots=${res.data}");
      //{vip: false, shotCount: 0, remainingCount: 50, shots: null}
      //是否vip      shotCount已经使用的次数    remainingCount 剩余次数  Shots今日合成进球id
      videosUsedShotsModel.value = VideosUsedShotsModel.fromJson(res.data);
      if ((videosUsedShotsModel.value.couponCount ?? 0) > 0) {
        getMergeVideos();
      } else if (!((videosUsedShotsModel.value.vip ?? false))) {
        if (!(dataFag["isFrist"] as bool)) {
          WxLoading.show();
        }
        final res2 = await Api()
            .get(ApiUrl.userIsNew, queryParameters: param, showError: false);
        if (!(dataFag["isFrist"] as bool)) {
          WxLoading.dismiss();
        }
        if (res2.isSuccessful()) {
          var userIsNewModel = UserIsNewModel.fromJson(res2.data);
          log("getCanDownLoadVideoUrl=${res2.data}-${userIsNewModel.isFirstRegister?.receivedVip == true}${userIsNewModel.isFirstRegister?.receivableActivityId}");
          if (!(userIsNewModel.isFirstRegister?.receivedVip == true)) {
            //领取vip
            getReciverVipDialog(S.current.Receive_now, S.current.Receive_no,
                () async {
              AppPage.back();
              if (!(dataFag["isFrist"] as bool)) {
                WxLoading.show();
              }
              var param22 = {
                'activityId':
                    userIsNewModel.isFirstRegister?.receivableActivityId,
              };
              var url = await ApiUrl.getReceiveVip(
                  userIsNewModel.isFirstRegister?.receivableActivityId ?? "");
              await Api().get(url, queryParameters: param22, showError: false);
              if (!(dataFag["isFrist"] as bool)) {
                WxLoading.dismiss();
              }
              await UserManager.instance.pullUserInfo().then((v) async {
                await Future.delayed(const Duration(milliseconds: 200));
                if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) == 0) {
                  WxLoading.showToast(S.current.Please_open_vip_first);
                } else {
                  getMergeVideos();
                }
              });
              BusUtils.instance.fire(EventAction(key: EventBusKey.receiveVip));
            }, () {
              AppPage.back();
            });
          } else {
            //开通vip
            getNeedVipDialog(
                S.current.Open_immediately, S.current.Talk_to_you_next_time,
                () {
              AppPage.back();
              AppPage.to(Routes.vipPage).then((v) async {
                await Future.delayed(const Duration(milliseconds: 1000));
                if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) == 0) {
                  WxLoading.showToast(S.current.Please_open_vip_first);
                } else {
                  getMergeVideos();
                }
              });
            }, () {
              AppPage.back();
            });
          }
        } else {
          WxLoading.showToast(res.message);
          return false;
        }
      } else {
        getMergeVideos();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //合成视频
  getMergeVideos() async {
    // compositeOption4 = 0.obs; //移除所选的球 0是  1否
    bool isCheckAll = true;
    var flist = dataList.where((value) {
      return value.checked == true;
    }).toList();

    if (flist.isEmpty) {
      WxLoading.showToast(S.current.merge_videos_tips1);
      return;
    }
    if (flist.length > 50) {
      WxLoading.showToast(S.current.merge_videos_tips3);
      return;
    }
    if (state.rememberOption.value == "0") {
      await WxStorage.instance.setString(
          "rememberOption", state.rememberOption.value); //记住我的选择 0记住  1不记住
      await WxStorage.instance.setInt("compositeOption1",
          state.compositeOption1.value); //单片时长  0  5s    1  10s
      await WxStorage.instance.setStringList(
          "compositeOption2", state.compositeOption2); //视频效果与个性化 0选中  1选中 多选
      // await WxStorage.instance.setInt(
      //     "compositeOption3", state.compositeOption3.value); //视角 0仅侧面  1全部视角
      // await WxStorage.instance.setInt(
      //     "compositeOption4", state.compositeOption4.value); //移除所选的球 0是  1否);
    }

    List<String?> ids = flist.map((item) => item.id).toList();
    List<int> effectList = [];
    for (int i = 0; i < 4; i++) {
      if (state.compositeOption2[i] == "1") {
        effectList.add(i + 1);
      }
    }

    var param = {
      'arenaId': arenaID.value.toString(), //球馆id
      //  'camera': state.compositeOption3.value == 0 ? 0 : -1, //0侧面 -1全部
      'duration': state.compositeOption1.value == 1 ? 10 : 5, //单个视频时长
      'effect': effectList, //特效 1 开启跟拍 2 慢放进球 3 保留原声 4 去除水印
      'fragments': ids, //片段id集合
      'name':
          "${UserManager.instance.userInfo.value?.userId}${DateTime.now().millisecondsSinceEpoch}", //合成名称
    };
    log("paramcompositeOption4=${jsonEncode(param)}");
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res = await Api().post(ApiUrl.getMergeVideos, data: param);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    log("message1");
    if (res.isSuccessful()) {
      //var compositeOption4 = 0.obs; //移除所选的球 0是  1否
      getMergeDialog(res.data["points"].toString(), isCheckAll);
    } else {
      WxLoading.showToast(res.message);
    }
  }

// 定义一个函数来提取日期部分（忽略时间）
  DateTime normalizeDate(DateTime dateTime) {
    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
    log("getDaoSql6catch3=${dateFormat.format(dateTime)}");
    return DateTime(dateTime.year, dateTime.month, dateTime.day);
  }

//全选和取消全选
  Future<void> checkAllVideo() async {
    state.allCheck.value = !state.allCheck.value;
    for (int i = 0; i < dataList.length; i++) {
      dataList[i].checked = (state.allCheck.value);
    }
    getDaoGoalList2();
  }

  //数据库取数据判断
  Future<void> getDaoGoalList2() async {
    var allCount = 0;
    for (int i = 0; i < dataList.length; i++) {
      if (dataList[i].checked ?? false) {
        allCount += 1;
      }
    }
    if (allCount >= (dataList.length)) {
      state.allCheck.value = true;
    } else {
      state.allCheck.value = false;
    }
    state.checkVideosCount.value = allCount;
    dataList.refresh();
  }

// 定义一个函数来解析时间字符串为 TimeOfDay 对象
  // 定义一个函数来解析时间字符串为 DateTime 对象
  DateTime parseDateTimeString(String dateTimeStr) {
    return DateFormat("yyyy-MM-dd'T'HH:mm:ssZ").parse(dateTimeStr);
  }

  //合成视频弹窗
  Future<void> getMergeDialog(var points, bool isCheckAll) async {
    getMergeDialog2(
        points == "0" || points == ""
            ? ""
            : S.current.merge_videos_dialog_tips8(points),
        points == "0" || points == "" ? "dialogvideo.png" : "daka3.png",
        S.current.sure, () {
      AppPage.back();
    });
  }

  void getMergeDialog2(
    String titltPoint,
    String imageAsset,
    String sureText,
    //String sureText2,
    void Function()? onPressed,
    // void Function()? onPressed2
  ) {
    Get.dialog(
      Padding(
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
        child: Material(
          type: MaterialType.transparency,
          color: Colors.transparent,
          child: Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    color: Colors.transparent,
                    child: Column(
                      children: <Widget>[
                        //upload_top_img
                        SizedBox(
                          height: 60.w,
                        ),
                        SizedBox(
                          height: 100.w,
                          width: double.infinity,
                          child: Stack(
                            alignment: Alignment.bottomCenter,
                            children: [
                              Container(
                                height: 65.w,
                                width: double.infinity,
                                margin: EdgeInsets.only(top: 35.w),
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(25.r),
                                    topRight: Radius.circular(25.r),
                                  ),
                                ),
                              ),
                              Transform.translate(
                                offset: Offset(0, -30.w),
                                child: MyImage(
                                  imageAsset,
                                  width: 85.w,
                                  height: 85.w,
                                  isAssetImage: true,
                                  fit: BoxFit.fitWidth,
                                  bgColor: Colors.transparent,
                                ),
                              ),
                            ],
                          ),
                        ),

                        Transform.translate(
                          offset: const Offset(0, -10),
                          child: Container(
                            alignment: Alignment.topLeft,
                            constraints: BoxConstraints(
                              maxHeight: titltPoint != "" ? 245.w : 185.w,
                              minHeight: 145.w,
                            ),
                            decoration: BoxDecoration(
                              color: Colours.color191921,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(25.r),
                                bottomRight: Radius.circular(25.r),
                              ),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            width: double.infinity,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: <Widget>[
                                if (titltPoint != "")
                                  Text(titltPoint,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 18.sp,
                                        color: Colours.colorA44EFF,
                                        fontWeight: AppFontWeight.regular(),
                                        height: 1,
                                      )),
                                if (titltPoint != "")
                                  SizedBox(
                                    height: 25.w,
                                  ),
                                Text(S.current.merge_videos_dialog_tips9,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: Colours.color9393A5,
                                      fontWeight: AppFontWeight.regular(),
                                      height: 1,
                                    )),
                                RichText(
                                  text: TextSpan(
                                      text: S.current.merge_videos_dialog_tips1,
                                      style: TextStyle(
                                          color: Colours.color9393A5,
                                          fontSize: 14.sp,
                                          height: 2,
                                          fontWeight: FontWeight.normal),
                                      children: <InlineSpan>[
                                        TextSpan(
                                            text:
                                                " ${S.current.merge_videos_dialog_tips2} ",
                                            style: TextStyle(
                                                color: Colours.white,
                                                fontSize: 14.sp,
                                                height: 2,
                                                fontWeight: FontWeight.normal)),
                                        TextSpan(
                                            text: S.current
                                                .merge_videos_dialog_tips31,
                                            style: TextStyle(
                                                color: Colours.color9393A5,
                                                height: 2,
                                                fontSize: 14.sp,
                                                fontWeight: FontWeight.normal)),
                                      ]),
                                ),
                                // Text(S.current.merge_videos_dialog_tips4,
                                //     textAlign: TextAlign.center,
                                //     style: TextStyle(
                                //       fontSize: 14.sp,
                                //       color: Colours.color9393A5,
                                //       height: 2,
                                //       fontWeight: AppFontWeight.regular(),
                                //     )),
                                SizedBox(
                                  height: 35.w,
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      top: 15.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                        begin: Alignment.bottomLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Text(
                                      sureText,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 15.sp),
                                    ),
                                  ),
                                ),
                                // GestureDetector(
                                //   behavior: HitTestBehavior.translucent,
                                //   onTap: onPressed2,
                                //   child: Container(
                                //     height: 46.w,
                                //     width: double.infinity,
                                //     alignment: Alignment.center,
                                //     margin: EdgeInsets.only(
                                //       right: 25.w,
                                //       top: 15.w,
                                //       left: 25.w,
                                //     ),
                                //     padding: EdgeInsets.only(
                                //         left: 5.w,
                                //         right: 5.w,
                                //         top: 3.w,
                                //         bottom: 3.w),
                                //     decoration: BoxDecoration(
                                //       color: Colours.color22222D,
                                //       borderRadius: BorderRadius.all(
                                //           Radius.circular(28.r)),
                                //     ),
                                //     child: Text(
                                //       sureText2,
                                //       style: TextStyles.regular.copyWith(
                                //           fontSize: 15.sp,
                                //           color: Colours.color9393A5),
                                //     ),
                                //   ),
                                // ),
                                const SizedBox(
                                  height: 10,
                                ),
                              ],
                            ),
                          ),
                        ),

                        // SizedBox(
                        //   height: 25.w,
                        // ),
                        // GestureDetector(
                        //   behavior: HitTestBehavior.translucent,
                        //   onTap: () {
                        //     AppPage.back();
                        //   },
                        //   child: WxAssets.images.icCloseDialog
                        //       .image(width: 30.w, height: 30.w),
                        // ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierColor: Colors.black.withOpacity(0.85),
    );
  }

//提示优惠券到期弹窗
  void showDateDialog2(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x50000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.back();
            },
            child: Container(
              width: 165.w,
              height: 94.w,
              margin: EdgeInsets.only(left: 150.w, right: 20.w, bottom: 71.w),
              padding: EdgeInsets.only(left: 10.w, right: 10.w, top: 12.w),
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: WxAssets.images.dialogTalk1.provider(),
                      fit: BoxFit.fill)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    textAlign: TextAlign.left,
                    text: TextSpan(
                        text: S.current.invitation_dialog_text8,
                        style: TextStyle(
                            color: Colours.color333333,
                            fontSize: 12.sp,
                            height: 1,
                            fontWeight: FontWeight.w400),
                        children: <InlineSpan>[
                          TextSpan(
                              text: S.current.invitation_dialog_text10(
                                  videosUsedShotsModel
                                          .value.couponExpireSoonCount ??
                                      0),
                              style: TextStyle(
                                  color: Colours.colorA44EFF,
                                  fontSize: 12.sp,
                                  height: 1,
                                  fontWeight: FontWeight.w400)),
                          TextSpan(
                              text: S.current.invitation_dialog_text11,
                              style: TextStyle(
                                  color: Colours.color333333,
                                  fontSize: 12.sp,
                                  height: 1,
                                  fontWeight: FontWeight.w400)),
                        ]),
                  ),
                  SizedBox(
                    height: 6.w,
                  ),
                  Text(
                      S.current.invitation_dialog_text4((videosUsedShotsModel
                                          .value.couponExpireTime ??
                                      "")
                                  .length >
                              10
                          ? (videosUsedShotsModel.value.couponExpireTime ?? "")
                              .substring(0, 10)
                          : videosUsedShotsModel.value.couponExpireTime ?? ""),
                      style: TextStyle(
                          color: Colours.color999999,
                          fontSize: 10.sp,
                          //  height: 1.2,
                          fontWeight: FontWeight.w400)),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  void checkOnlyVideo(int index) {
    dataList[index].checked = !(dataList[index].checked ?? false);
    dataList.refresh();
    getDaoGoalList2();
  }
}
