import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/login/bindPhone/bind_phone_logic.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:shoot_z/gen/assets.gen.dart';

class BindPhonePage extends StatelessWidget {
  BindPhonePage({super.key});
  final logic = Get.put(BindPhoneLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        top: false,
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => onKeyDismiss(),
          child: Column(
            children: [
              SizedBox(
                width: Get.width,
                height: 400.w,
                child: Stack(
                  children: [
                    Positioned(
                      right: 0,
                      top: 0,
                      left: 0,
                      child: WxAssets.images.loginTopBg.image(
                          height: 400.w,
                          width: ScreenUtil().screenWidth,
                          fit: BoxFit.fill),
                    ),
                    Positioned(
                        left: 20.w,
                        top: 120.w + ScreenUtil().statusBarHeight + 16.w,
                        child: Opacity(
                            opacity: 0.5, // 50% 不透明度
                            child: Container(
                              height: 4.w,
                              width: 120.w,
                              decoration: const BoxDecoration(
                                  gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF,
                                ],
                              )),
                            ))),
                    Positioned(
                        left: 20.w,
                        top: 120.w + ScreenUtil().statusBarHeight,
                        child: ShaderMask(
                            shaderCallback: (bounds) => const LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [
                                    Colours.colorFFECC1,
                                    Colours.colorE7CEFF,
                                    Colours.colorD1EAFF
                                  ],
                                ).createShader(bounds),
                            child: Text(
                              S.current.welcome_to + S.current.shoot_z,
                              style: TextStyles.semiBold14
                                  .copyWith(fontSize: 20.sp),
                            ))),
                    Positioned(
                        left: 0,
                        bottom: 0,
                        right: 0,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ShaderMask(
                                shaderCallback: (bounds) =>
                                    const LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: [
                                        Colours.colorFFECC1,
                                        Colours.colorE7CEFF,
                                        Colours.colorD1EAFF
                                      ],
                                    ).createShader(bounds),
                                child: Text(
                                  S.current.slogan,
                                  style: TextStyles.regular
                                      .copyWith(fontSize: 12.sp),
                                )).marginOnly(left: 20.w),
                            SizedBox(
                              height: 70.w,
                            ),
                            _tel(context),
                            SizedBox(
                              height: 20.w,
                            ),
                            _inputCode(context),
                          ],
                        )),
                  ],
                ),
              ),
              SizedBox(
                height: 40.w,
              ),
              _loginButton(context)
            ],
          ),
        ),
      ),
    );
  }

  Widget _tel(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      height: 50.w,
      decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(25.r)),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
                height: 50.w,
                child: Obx(
                  () => UserManager.instance.showPrivacyDialog.value
                      ? const SizedBox.shrink()
                      : TextField(
                          controller: logic.phoneController,
                          style: TextStyles.din
                              .copyWith(fontWeight: FontWeight.bold),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly, // 只允许输入数字
                            LengthLimitingTextInputFormatter(11), // 限制输入长度为11
                          ],
                          decoration: InputDecoration(
                            hintText: S.current.please_enter_your_mobile_number,
                            hintStyle: TextStyles.display14
                                .copyWith(color: Colours.color5C5C6E),
                            contentPadding:
                                const EdgeInsets.only(top: 0, bottom: 0),
                            //让文字垂直居中,
                            border: InputBorder.none,
                          ),
                          keyboardType: TextInputType.phone,
                        ),
                )),
          ),
        ],
      ),
    );
  }

  Widget _loginButton(BuildContext context) {
    return WxButton(
      height: 50.w,
      text: S.current.login_register,
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      borderRadius: BorderRadius.circular(25.r),
      linearGradient: GradientUtils.mainGradient,
      onPressed: () {
        logic.wxLogin();
      },
    );
  }

  Widget _inputCode(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        padding: EdgeInsets.only(left: 20.w, right: 6.w),
        height: 50.w,
        decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(25.r)),
        child: Row(children: [
          Expanded(
              child: SizedBox(
            height: 50.w,
            child: TextField(
              controller: logic.codeController,
              style: TextStyles.din.copyWith(fontWeight: FontWeight.bold),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly, // 只允许输入数字
                LengthLimitingTextInputFormatter(4), // 限制输入长度为4
              ],
              decoration: InputDecoration(
                hintText: S.current.enter_code,
                hintStyle:
                    TextStyles.display14.copyWith(color: Colours.color5C5C6E),
                contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                //让文字垂直居中,
                border: InputBorder.none,
              ),
              keyboardType: TextInputType.phone,
            ),
          )),
          Obx(() => WxButton(
                height: 38.w,
                width: 100.w,
                text: logic.isCountdownActive.value
                    ? "${logic.countdown.value}"
                    : S.current.send_code,
                textStyle: TextStyles.bold.copyWith(
                    fontSize: 12.sp,
                    color: logic.isCountdownActive.value
                        ? Colours.colorA8A8BC
                        : Colours.color191921),
                borderRadius: BorderRadius.circular(20.r),
                linearGradient: logic.isCountdownActive.value
                    ? const LinearGradient(
                        colors: [
                          Colours.color5C5C6E,
                          Colours.color5C5C6E,
                        ],
                      )
                    : const LinearGradient(
                        colors: [
                          Colours.colorFFECC1,
                          Colours.colorE7CEFF,
                          Colours.colorD1EAFF
                        ],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                onPressed: logic.isCountdownActive.value
                    ? null
                    : () {
                        logic.getCode(context);
                      },
              ))
        ]));
  }
}
