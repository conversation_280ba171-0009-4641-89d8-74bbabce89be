import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:get/get.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shoot_z/network/model/message_has_unread_model.dart';
import 'package:shoot_z/pages/login/models/my_summary_model.dart';
import 'package:shoot_z/pages/tab2Venue/tab_venue_logic.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../generated/l10n.dart';
import '../../network/api_url.dart';
import '../../routes/app.dart';
import '../../routes/route.dart';
import '../../utils/event_bus.dart';
import 'models/user_info_model.dart';
import 'models/user_model.dart';

class UserManager {
  UserManager._privateConstructor();
  static final UserManager _instance = UserManager._privateConstructor();
  static UserManager get instance {
    return _instance;
  }

  /// 响应式 - 是否登录
  var isLoginObs = false.obs;

  /// 普通 - 是否登录
  bool get isLogin => isLoginObs.value;

  /// 本地缓存用户信息key
  final String _key = "userModel";

  /// 本地缓存用户信息key
  final String _keyUserInfo = "userInfoModelKey";

  final String _keySummaryInfo = "SummaryInfo";
  final String _keyMessageHasUnreadModel = "messageHasUnreadModel";

  /// 本地缓存用户信息key
  var _traceId = "";

  UserModel? user;
  var userInfo = Rx<UserInfoModel?>(null);
  var summaryModel = Rx<MySummaryModel?>(null);
  var messageHasUnreadModel = Rx<MessageHasUnreadModel?>(null);

  var isFirstLogin = false;
  var showPrivacyDialog = false.obs;
  var channel = "".obs; // 渠道
  var source = "".obs; // 来源标识 applinks
  var campaign = "".obs; // 活动标识
  var medium = "".obs; // 媒介类型
  var term = "".obs; // 关键词
  var content = "".obs; // 内容标识
  var versionCode = "".obs; // 内容标识

  /// 初始化本地用户信息
  Future<void> setup() async {
    String? str = await WxStorage.instance.getString(_key);

    if (str != null) {
      user = UserModel.fromJson(json.decode(str));
      isLoginObs.value = true;
      WxStorage.instance.exchangeUser(user!.userId);
      String? userInfoStr = await WxStorage.instance.getString(_keyUserInfo);
      if (userInfoStr != null) {
        userInfo.value = UserInfoModel.fromJson(json.decode(userInfoStr));
        userInfo.refresh();
      }
      if (_traceId == "" && ((userInfo.value?.userId ?? "") != "")) {
        _traceId =
            "${userInfo.value?.userId}${userInfo.value?.userId}${DateTime.now().millisecondsSinceEpoch}";
      }
      String? summaryInfoStr =
          await WxStorage.instance.getString(_keySummaryInfo);
      if (summaryInfoStr != null) {
        summaryModel.value =
            MySummaryModel.fromJson(json.decode(summaryInfoStr));
        summaryModel.refresh();
      }

      String? messageHasUnreadModelStr =
          await WxStorage.instance.getString(_keyMessageHasUnreadModel);
      if (messageHasUnreadModelStr != null) {
        messageHasUnreadModel.value = MessageHasUnreadModel.fromJson(
            json.decode(messageHasUnreadModelStr));
        messageHasUnreadModel.refresh();
      }
    }
  }

  /// 获取我的信息
  Future<bool> pullUserInfo() async {
    var res = await Api().get(ApiUrl.userInfo);
    log("userInfo=${res.data}");
    if (res.isSuccessful()) {
      setUserInfo(UserInfoModel.fromJson(res.data));
      return true;
    } else {
      // WxLoading.showToast(res.message);
      return false;
    }
  }

  Future postChannelSubmit(int channelType,
      {String channelParam = 'launch'}) async {
    if (versionCode.value == "") {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      versionCode.value = packageInfo.version;
    }
    var params = {
      "versionCode": versionCode.value, // 版本号
      "channelCode": channel.value, // 渠道码
      "channelParam":
          channelParam, // 渠道参数 如二维码上的场地参数 启动 launch   半场单人shot  半场多人 shots
      "userId": user?.userId, // 必填
      "userFor": "", // 备用字段
      "channelType": channelType, //'0 通用， 1 注册，2 登陆，3 跳转'
      "source": source.value, // h5 小程序之类的
      "campaign": campaign.value, // 活动id
      "medium": medium.value, // 媒介类型
      "term": term.value, // 关键词
      "content": content.value // 内容标识
    };
    log("postChannelSubmit1=$params");
    log("postChannelSubmit1=${WxEnv.instance.apiUrl}");
    var res =
        await Api().post(ApiUrl.channelSubmit, showError: false, data: params);
    log("postChannelSubmit=${res.data}");
  }

  Future<bool> getMySummary() async {
    var res = await Api().get(ApiUrl.mySummary, showError: false);
    log("mySummary333=${res.data}");
    if (res.isSuccessful()) {
      summaryModel.value = MySummaryModel.fromJson(res.data);
      summaryModel.refresh();
      WxStorage.instance.setString(
          _keySummaryInfo, json.encode(summaryModel.value!.toJson()));
      return true;
    } else {
      // WxLoading.showToast(res.message);
      return false;
    }
  }

  Future<bool> getMessageHasUnread() async {
    var res = await Api().get(ApiUrl.getMessagehasUnread, showError: false);
    log("messageHasUnreadModel=${res.data}");
    if (res.isSuccessful()) {
      messageHasUnreadModel.value = MessageHasUnreadModel.fromJson(res.data);
      messageHasUnreadModel.refresh();
      WxStorage.instance.setString(_keyMessageHasUnreadModel,
          json.encode(messageHasUnreadModel.value!.toJson()));
      return true;
    } else {
      // WxLoading.showToast(res.message);
      return false;
    }
  }

  setUserInfo([UserInfoModel? model]) {
    userInfo.value = model ?? userInfo.value;
    userInfo.refresh();
    if (_traceId == "" && ((userInfo.value?.userId ?? "") != "")) {
      _traceId =
          "${userInfo.value?.userId}${userInfo.value?.userId}${DateTime.now().millisecondsSinceEpoch}";
    }
    WxStorage.instance
        .setString(_keyUserInfo, json.encode(userInfo.value!.toJson()));
  }

  Future<bool> refreshToken() async {
    var res = await Api.refreshToken().post(ApiUrl.refreshToken,
        data: {"refreshToken": user?.refreshToken},
        headers: {"Authorization": null},
        showError: false);
    if (res.isSuccessful()) {
      UserModel userModel = UserManager.instance.user!;
      userModel.refreshToken = res.data["refreshToken"];
      userModel.token = res.data["token"];
      WxEnv.instance.setToken(userModel.token, userModel.refreshToken);
      UserManager.instance.login(userModel);
      return true;
    } else if (res.code == 401) {
      // WxLoading.showToast(res.message);
      // WidgetsBinding.instance.addPostFrameCallback((_) {
      //   Get.dialog(CustomAlertDialog(title: S.current.you_are_offline,content: S.current.offline_hint,hideCancel: true,));
      // });
      Future.delayed(const Duration(milliseconds: 500)).then((_) {
        Get.dialog(CustomAlertDialog(
          title: S.current.you_are_offline,
          content: S.current.offline_hint,
          hideCancel: true,
        ));
      });
      return false;
    }
    return false;
  }

  void login(UserModel user, {bool isSave = true}) {
    this.user = user;
    if (isSave) {
      BusUtils.instance.fire(EventAction(key: EventBusKey.loginSuccessful));
      isLoginObs.value = true;
      WxStorage.instance.setString(_key, json.encode(user.toJson()));
      WxStorage.instance.exchangeUser(user.userId);
    }
  }

  void logout({bool isKickOut = true}) {
    WxStorage.instance.remove(_key);
    WxStorage.instance.remove(_keyUserInfo);
    WxEnv.instance.clearToken();
    WxStorage.instance.exchangeUser('');
    isLoginObs.value = false;
    user = null;
    userInfo.value = null;
    userInfo.refresh();
    if (isKickOut) {
      Get.delete<TabVenueLogic>();
      AppPage.resetRootPageName(Routes.login);
    }
  }

  void returnTabVenue() {
    AppPage.resetRootPageName(Routes.tab);
  }

  getPageName(String pageName) {
    var page = 0;
    switch (pageName) {
      case "main":
        page = 0;
        break;
      case "HomePage": //mainTab首页
        page = 1;
        break;
      case "CenuePage": //mainTab场馆
        page = 2;
        break;
      case "CreatPage": //mainTab创作
        page = 4;
        break;
      case "PointsMall": //mainTab积分商城
        page = 5;
        break;
      case "Mine": ////mainTab我的
        page = 6;
        break;
      case "main":
        page = 0;
        break;
      case "main":
        page = 0;
        break;
    }
    return page;
  }

  //埋点相关接口
  Future postApmTracking(int action,
      {String nowPage = '',
      String remark = '',
      String toPage = '',
      String nextPage = '',
      String arenaId = '0'}) async {
    if (_traceId == "") {
      if (_traceId == "" && ((userInfo.value?.userId ?? "") != "")) {
        _traceId =
            "${userInfo.value?.userId}${userInfo.value?.userId}${DateTime.now().millisecondsSinceEpoch}";
      }
      return;
    }
    var nowPage2 = getPageName(nowPage); //现在页面
    var toPage2 = getPageName(toPage); //下个页面
    var nextPage2 = getPageName(nextPage); //上个页面

    if (versionCode.value == "") {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      versionCode.value = packageInfo.version;
    }
    var vername = Platform.isAndroid ? "android" : "ios";
    var params = {
      "traceId": _traceId, //string 档次会话跟踪ID，打开小程序时前端生成，后续每次请求都带上同一个traceId
      "action": action, //int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
      "arenaId": arenaId, //string 场馆ID 默认 0
      "content": content
          .value, //string 内容 业务上的操作内容，如：输入了什么内容，分享了哪个内容，下载了哪个内容, 二维码参数， 分享用户id 等, 前端自定义
      "elem": remark, //string 页面元素（可空） 按钮名称，输入框名称，滑动元素，banner名称，广告名称 等, 前端自定义
      "page": nowPage2, //int 页面id 如：10000 自己定义写在文档
      "referer":
          nextPage2, //int 来源页面 action 0 且路由跳转进入时，referer 为上一页面id；如使用是其他方式进入页面，为微信小程序场景值
      "subPage": "0", //int 子页面 如：10001
      "to": toPage2, //页面id 如：11000
      "timestamp": DateTime.now().millisecondsSinceEpoch, //时间戳
      "userId": user?.userId ?? "0", // 必填 //string 用户ID Example : 0
      "ver": "$vername-$versionCode.value", // 版本号, //string 小程序版本号
      "vip": isLoginObs.value
          ? -1
          : (userInfo.value?.isVip ?? false)
              ? 1
              : 0, //int  是否是vip -1:未知 0:不是 1:是
    };
    log("postApmTracking=$params");
    var res = await Api()
        .get(ApiUrl.getApmTracking, showError: false, queryParameters: params);
    log("postApmTracking =${res.data}");
  }
}
