import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/login/proxy/view.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab2Venue/tab_venue_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/pay/fluwx_utils.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import '../../generated/l10n.dart';
import '../../utils/event_bus.dart';
import 'logic.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final logic = Get.put(LoginLogic());
  final state = Get.find<LoginLogic>().state;

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tabVenue = Get.isRegistered<TabVenueLogic>();
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          BusUtils.instance.fire(EventAction(key: EventBusKey.loginCanceled));
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        //默认情况下，Scaffold 的 resizeToAvoidBottomInset 属性为 true，会自动调整内容以避免被键盘遮挡。
        // 在 Flutter 中，当键盘弹出时，如果内容区域没有足够的空间显示所有组件，常常会出现 “Bottom overflowed by … pixels” 错误。
        // 这通常是因为键盘的高度占用了屏幕空间，导致某些组件无法完全显示。
        //把resizeToAvoidBottomInset设置为false，键盘弹出的时候布局就不会调整，就不需要用SingleChildScrollView嵌套
        // appBar: MyAppBar(
        //   // title: Text(
        //   //   S.current.login,
        //   // ),
        //   actions: [
        //     if (!TabVenue)
        //       GestureDetector(
        //         onTap: () => AppPage.resetRootPageName(Routes.tab),
        //         child: Padding(
        //             padding: EdgeInsets.only(right: 20.w),
        //             child: Text(
        //               S.current.just_stroll_around,
        //               style: TextStyles.display14
        //                   .copyWith(color: Colours.color5C5C6E),
        //             )),
        //       )
        //   ],
        // ),
        body: SafeArea(
          top: false,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => onKeyDismiss(),
            child: Column(
              children: [
                SizedBox(
                  width: Get.width,
                  height: 400.w,
                  child: Stack(
                    children: [
                      Positioned(
                        right: 0,
                        top: 0,
                        left: 0,
                        child: WxAssets.images.loginTopBg.image(
                            height: 400.w,
                            width: ScreenUtil().screenWidth,
                            fit: BoxFit.fill),
                      ),
                      Positioned(
                          left: 20.w,
                          top: 120.w + ScreenUtil().statusBarHeight + 16.w,
                          child: Opacity(
                              opacity: 0.5, // 50% 不透明度
                              child: Container(
                                height: 4.w,
                                width: 120.w,
                                decoration: const BoxDecoration(
                                    gradient: LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF,
                                  ],
                                )),
                              ))),
                      Positioned(
                          left: 20.w,
                          top: 120.w + ScreenUtil().statusBarHeight,
                          child: ShaderMask(
                              shaderCallback: (bounds) => const LinearGradient(
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                    colors: [
                                      Colours.colorFFECC1,
                                      Colours.colorE7CEFF,
                                      Colours.colorD1EAFF
                                    ],
                                  ).createShader(bounds),
                              child: Text(
                                S.current.welcome_to + S.current.shoot_z,
                                style: TextStyles.semiBold14
                                    .copyWith(fontSize: 20.sp),
                              ))),
                      Positioned(
                          left: 0,
                          bottom: 0,
                          right: 0,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ShaderMask(
                                  shaderCallback: (bounds) =>
                                      const LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: [
                                          Colours.colorFFECC1,
                                          Colours.colorE7CEFF,
                                          Colours.colorD1EAFF
                                        ],
                                      ).createShader(bounds),
                                  child: Text(
                                    S.current.slogan,
                                    style: TextStyles.regular
                                        .copyWith(fontSize: 12.sp),
                                  )).marginOnly(left: 20.w),
                              SizedBox(
                                height: 70.w,
                              ),
                              _tel(context),
                              SizedBox(
                                height: 20.w,
                              ),
                              _inputCode(context),
                            ],
                          )),
                    ],
                  ),
                ),
                SizedBox(
                  height: 40.w,
                ),
                _loginButton(context),
                if (!tabVenue)
                  Column(
                    children: [
                      SizedBox(
                        height: 40.w,
                      ),
                      GestureDetector(
                        onTap: () => AppPage.resetRootPageName(Routes.tab),
                        child: Text(
                          S.current.just_stroll_around,
                          style: TextStyles.bold.copyWith(
                              color: Colours.colorA8A8BC, fontSize: 12.sp),
                        ),
                      ),
                      Container(
                        color: Colours.colorA8A8BC,
                        height: 1.w,
                        width: 48.w,
                      ),
                    ],
                  ),
                if (const String.fromEnvironment('env', defaultValue: 'dev') !=
                    'pro')
                  SizedBox(
                    height: 15.w,
                  ),
                if (const String.fromEnvironment('env', defaultValue: 'dev') !=
                    'pro')
                  WxButton(
                    // height: 54,
                    text: S.current.set_proxy,
                    textStyle: TextStyles.regular,
                    margin: EdgeInsets.symmetric(horizontal: 30.w),
                    backgroundColor: Colors.transparent,
                    onPressed: () {
                      AppPage.toPage(ProxyPage());
                    },
                  ),
                const Spacer(),
                _thirdPartyLogin(context),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () => logic.didAgree(),
                      child: Obx(() => logic.state.isAgree.value
                          ? WxAssets.images.selectIcon.image()
                          : WxAssets.images.unselectIcon.image()),
                    ),
                    SizedBox(
                      width: 6.w,
                    ),
                    Text.rich(TextSpan(children: [
                      TextSpan(
                          text: S.current.read_and_agree,
                          style: TextStyles.display12),
                      TextSpan(
                        text: '《${S.current.user_policy}》',
                        style: TextStyles.display12.copyWith(
                          color: Colors.white,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () => logic.didUserPolicy(),
                      ),
                      TextSpan(
                          text: S.current.and, style: TextStyles.display12),
                      TextSpan(
                        text: '《${S.current.privacy_policy}》',
                        style: TextStyles.display12.copyWith(
                          color: Colors.white,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () => logic.didPrivacyPolicy(),
                      ),
                    ])),
                    SizedBox(
                      height: 31.w + MediaQuery.of(context).padding.bottom,
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _tel(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      height: 50.w,
      decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(25.r)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Stack(
          //   children: [
          //     Obx(
          //       () => DropdownButton<String>(
          //         underline: Container(height: 0),
          //         dropdownColor: Colours.color5C5C6E,
          //         style: TextStyles.titleMedium18,
          //         isDense: true,
          //         iconSize: 10.w,
          //         icon: Padding(
          //             padding: const EdgeInsets.only(left: 5),
          //             child: WxAssets.images.arrowDown.image()),
          //         value: state.areaCode.value,
          //         onChanged: (newValue) => state.areaCode.value = newValue!,
          //         items: LoginLogic.areaCodes
          //             .map<DropdownMenuItem<String>>((String value) {
          //           return DropdownMenuItem<String>(
          //             value: value,
          //             child: Text(value),
          //           );
          //         }).toList(),
          //       ),
          //     ),
          //     Positioned.fill(
          //       child: AbsorbPointer(
          //         //阻止DropdownButton点击
          //         absorbing: true,
          //         //当 absorbing 属性设置为 true 时，AbsorbPointer 会拦截触摸事件，使得事件不会传递给子组件
          //         child: Container(color: Colors.transparent),
          //       ),
          //     ),
          //   ],
          // ),
          // SizedBox(
          //   width: 10.w,
          // ),
          Expanded(
            child: SizedBox(
                height: 50.w,
                child: Obx(
                  () => UserManager.instance.showPrivacyDialog.value
                      ? const SizedBox.shrink()
                      : TextField(
                          controller: state.phoneController,
                          style: TextStyles.din
                              .copyWith(fontWeight: FontWeight.bold),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly, // 只允许输入数字
                            LengthLimitingTextInputFormatter(11), // 限制输入长度为11
                          ],
                          decoration: InputDecoration(
                            hintText: S.current.please_enter_your_mobile_number,
                            hintStyle: TextStyles.display14
                                .copyWith(color: Colours.color5C5C6E),
                            contentPadding:
                                EdgeInsets.only(top: 3.w, bottom: 0),
                            //让文字垂直居中,
                            border: InputBorder.none,
                          ),
                          keyboardType: TextInputType.phone,
                        ),
                )),
          ),
        ],
      ),
    );
  }

  Widget _getCode(BuildContext context) {
    return WxButton(
      height: 56,
      text: S.current.get_code,
      margin: EdgeInsets.symmetric(horizontal: 30.w),
      borderRadius: BorderRadius.circular(28),
      linearGradient: GradientUtils.mainGradient,
      onPressed: () {
        logic.getCode(context);
      },
    );
  }

  Widget _loginButton(BuildContext context) {
    return WxButton(
      height: 50.w,
      text: S.current.login_register,
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      borderRadius: BorderRadius.circular(25.r),
      linearGradient: GradientUtils.mainGradient,
      onPressed: () {
        logic.login(context);
      },
    );
  }

  Widget _inputCode(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        padding: EdgeInsets.only(left: 20.w, right: 6.w),
        height: 50.w,
        decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(25.r)),
        child: Row(children: [
          Expanded(
              child: SizedBox(
            height: 50.w,
            child: TextField(
              controller: state.codeController,
              style: TextStyles.din.copyWith(fontWeight: FontWeight.bold),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly, // 只允许输入数字
                LengthLimitingTextInputFormatter(4), // 限制输入长度为4
              ],
              decoration: InputDecoration(
                hintText: S.current.enter_code,
                hintStyle:
                    TextStyles.display14.copyWith(color: Colours.color5C5C6E),
                contentPadding: EdgeInsets.only(top: 3.w, bottom: 0),
                //让文字垂直居中,
                border: InputBorder.none,
              ),
              keyboardType: TextInputType.phone,
            ),
          )),
          Obx(() => WxButton(
                height: 38.w,
                width: 100.w,
                text: state.isCountdownActive.value
                    ? "${state.countdown.value}"
                    : S.current.send_code,
                textStyle: TextStyles.bold.copyWith(
                    fontSize: 12.sp,
                    color: state.isCountdownActive.value
                        ? Colours.colorA8A8BC
                        : Colours.color191921),
                borderRadius: BorderRadius.circular(20.r),
                linearGradient: state.isCountdownActive.value
                    ? const LinearGradient(
                        colors: [
                          Colours.color5C5C6E,
                          Colours.color5C5C6E,
                        ],
                      )
                    : const LinearGradient(
                        colors: [
                          Colours.colorFFECC1,
                          Colours.colorE7CEFF,
                          Colours.colorD1EAFF
                        ],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                onPressed: state.isCountdownActive.value
                    ? null
                    : () {
                        logic.getCode(context);
                      },
              ))
        ]));
  }

  Widget _thirdPartyLogin(BuildContext context) {
    return Wrap(
      spacing: 48.w,
      children: [
        // WxAssets.images.appleLogo.image(width: 50.w, height: 50.w),
        InkWell(
            onTap: () {
              // FluwxUtils.instance.openMiniProgram(pathType: LoginPath());
              if (!state.isAgree.value) {
                logic.showAgreeSheet(context, isWxLogin: true);
                return;
              }
              FluwxUtils.instance.wxLogin();
            },
            child: WxAssets.images.wxLogo.image(width: 40.w, height: 40.w)),
        // WxAssets.images.googleLogo.image(width: 50.w, height: 50.w),
      ],
    );
  }
}
