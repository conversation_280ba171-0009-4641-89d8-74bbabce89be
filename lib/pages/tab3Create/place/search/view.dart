import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab2Venue/place_list_item.dart';
import 'package:shoot_z/pages/tab3Create/place/search/logic.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:ui_packages/generated/l10n.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../../generated/l10n.dart';
import '../../../../widgets/view.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final logic = Get.put(SearchLogic());
  final state = Get.find<SearchLogic>().state;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        print('onPopInvoked$didPop');
        if (!didPop || Platform.isIOS) {
          logic.back(isSlide: true);
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          bottom: false,
          child: Column(
            children: [
              _search(context),
              Expanded(
                  child: Obx(
                () =>
                    state.type.value == 0 ? _history(context) : _list(context),
              )),
            ],
          ),
        ),
      ),
    );
  }

  Widget _search(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 12, left: 15, right: 15),
      child: Row(
        children: [
          Expanded(
              child: Container(
            height: 42,
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(21),
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 14,
                ),
                WxAssets.images.icSearch.image(),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                    child: TextField(
                  autocorrect: false,
                  controller: state.controller,
                  keyboardType: TextInputType.text,
                  style: TextStyles.display14,
                  maxLines: 1,
                  // onChanged: logic.onTextChanged,
                  decoration: InputDecoration(
                    // isDense: true,//isDense 为 true 会让 TextField 的高度变紧凑，同时调整光标和文本的位置。
                    contentPadding: const EdgeInsets.only(top: 0, bottom: 5),
                    border: InputBorder.none,
                    hintText: S.current.enter_stadium_name_to_search,
                    hintStyle: TextStyles.display14
                        .copyWith(color: Colours.color5C5C6E),
                  ),
                )),
                GestureDetector(
                    onTap: () => state.controller.text = '',
                    child: WxAssets.images.icSearchDelete.image()),
                const SizedBox(
                  width: 12,
                ),
              ],
            ),
          )),
          GestureDetector(
              onTap: () => logic.back(),
              child: Padding(
                padding: const EdgeInsets.only(left: 7),
                child: Text(
                  UiS.current.cancel,
                  style: TextStyles.display14,
                ),
              )),
        ],
      ),
    );
  }

  Widget _list(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: state.type.value == 1 ? 15 : 20,
      ),
      child: ListView.builder(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
          itemCount: state.type.value == 1
              ? state.texts.length
              : (state.models.isEmpty ? 1 : state.models.length),
          itemBuilder: (context, index) {
            if (state.type.value == 1) {
              return _textItem(context, index);
            } else if (state.models.isNotEmpty) {
              return _modelItem(context, index);
            }
            return _empty(context);
          }),
    );
  }

  Widget _modelItem(BuildContext context, int index) {
    return PlaceListItem(model: state.models[index]);
  }

  Widget _textItem(BuildContext context, int index) {
    final str = state.texts[index];
    return GestureDetector(
      onTap: () => logic.searchModel(index == 0 ? state.controller.text : str),
      child: Container(
        padding: const EdgeInsets.only(left: 15, right: 15),
        height: 50.w,
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colours.color191921,
              width: 1.0,
            ),
          ),
        ),
        child: Row(
          children: [
            WxAssets.images.icSearch
                .image(width: 18.w, height: 18.w, color: Colours.color5D5D6E),
            const SizedBox(
              width: 7,
            ),
            RichText(
              text: logic.highlightWord(
                  str,
                  state.controller.text,
                  Colours.color9D4FEF,
                  index == 0 ? Colours.color5C5C6E : Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _empty(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(top: 170.w),
        child: myNoDataView(context,
            msg: S.current.no_search_results,
            imagewidget: WxAssets.images.icSearchNo.image()));
  }

  Widget _history(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 30, left: 15, right: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              TextWithIcon(title: S.current.search_history),
              const Spacer(),
              GestureDetector(
                  onTap: () => logic.deleteHistory(),
                  child: Obx(() {
                    return WxAssets.images.icDelete.image(
                        width: 18.w,
                        height: 18.w,
                        color: state.historyList.isEmpty
                            ? Colours.color5D5D6E
                            : Colors.white);
                  })),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          Obx(() => state.historyList.isEmpty
              ? Container(
                  alignment: Alignment.center,
                  child: Text(
                    textAlign: TextAlign.center,
                    S.current.no_search_history_yet,
                    style: TextStyles.display14
                        .copyWith(color: Colours.color5C5C6E),
                  ),
                )
              : Wrap(
                  spacing: 10,
                  runSpacing: 15,
                  children: state.historyList.map((str) {
                    return GestureDetector(
                      onTap: () => logic.searchModel(str),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 15.w, vertical: 7.w),
                        decoration: BoxDecoration(
                          color: Colours.color191921,
                          borderRadius: BorderRadius.circular(16.w),
                        ),
                        child: Text(
                          str,
                          style: TextStyles.display12,
                        ),
                      ),
                    );
                  }).toList(),
                )),
        ],
      ),
    );
  }
}
