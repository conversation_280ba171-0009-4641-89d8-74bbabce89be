// ignore_for_file: unused_field

import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/player/view.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/selectCity/select_city_dialog.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/selectCity/select_city_logic.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/team/view.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'dart:ui' as ui;
import '../../../routes/app.dart';
import 'logic.dart';

class RankingsPage extends StatefulWidget {
  const RankingsPage({super.key});

  @override
  State<RankingsPage> createState() => _RankingsPageState();
}

class _RankingsPageState extends State<RankingsPage> {
  final logic = Get.put(RankingsLogic());
  final state = Get.find<RankingsLogic>().state;
  final _playerTabIndicatorKey = GlobalKey();
  final _teamTabIndicatorKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // 预加载指示器图片
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _CustomPainter.preloadImage(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: buildUI(context),
    );
  }

  Widget buildUI(BuildContext context) {
    return Stack(children: [
      // 渐变背景
      bgGradient(),
      // 图片布局
      bgImage(context),
      // monthBg(context),
      topTitleImage(context),
      titleBar(context),
      centerDesc(context),
      leftRules(context),
      rightCity(context),
      //切换选择
      changeRankType(context),
      // 排行榜
      rankings(context),
      //未开启定位
      noLocation(context),
      // 地区数据为空
      dataEmpty(context),
      // 选择地区
      // selectCity(context),
      // 个人排行
      myRanking(context),
    ]);
  }

  Widget selectCity(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: true,
        child: Positioned(
            top: 290.w,
            left: 274.w,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () => logic.showCityPicker(),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Obx(
                    () => Text(
                      state.cityName.value.length > 4
                          ? '${state.cityName.value.substring(0, 3)}...'
                          : state.cityName.value,
                      style: TextStyles.bold.copyWith(fontSize: 12.sp),
                    ),
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  WxAssets.images.icCityArrow
                      .image(width: 12.w, fit: BoxFit.fill),
                ],
              ),
            )),
      ),
    );
  }

  Widget myRanking(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: UserManager.instance.isLoginObs.value &&
            state.isPlayer.value &&
            !state.noData.value,
        child: Positioned(
          left: 20.w,
          right: 20.w,
          bottom: MediaQuery.of(context).padding.bottom > 0
              ? MediaQuery.of(context).padding.bottom
              : 20.w,
          child: InkWell(
              onTap: () {
                AppPage.to(Routes.careerHighlightsHomePage);
              },
              child: Container(
                height: 48.w,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: WxAssets.images.mineRankingBg.provider(),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Row(
                  children: [
                    SizedBox(
                      width: 24.w,
                    ),
                    SizedBox(
                      width: 37.w,
                      child: Obx(
                        () => Text(
                          '${(state.numbers[state.playerTabIndex.value] > 100 || state.numbers[state.playerTabIndex.value] == 0) ? '未上榜' : state.numbers[state.playerTabIndex.value]}',
                          textAlign: TextAlign.center,
                          style: TextStyles.bold.copyWith(fontSize: 12.sp),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 37.w,
                    ),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12.w),
                      child: CachedNetworkImage(
                        imageUrl:
                            UserManager.instance.userInfo.value?.avatar ?? '',
                        width: 24.w,
                        height: 24.w,
                        fit: BoxFit.fill,
                      ),
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                    Text(
                      UserManager.instance.userInfo.value?.userName ?? '',
                      style: TextStyles.bold.copyWith(fontSize: 12.sp),
                    ),
                    const Spacer(),
                    SizedBox(
                      width: 48.w,
                      child: Obx(
                        () => Text(
                          state.myRankData[state.playerTabIndex.value] == '0'
                              ? '-'
                              : state.myRankData[state.playerTabIndex.value],
                          textAlign: TextAlign.center,
                          style: TextStyles.regular.copyWith(fontSize: 20.sp),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 27.w,
                    ),
                  ],
                ),
              )),
        ),
      ),
    );
  }

  Widget dataEmpty(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: (state.noData.value && state.isPlayer.value) ||
            (state.teamNoData.value && !state.isPlayer.value),
        child: Positioned(
          top: 404.w,
          left: 0,
          right: 0,
          child: Center(
            child: myNoDataView(
              context,
              msg: '该地区暂无排名数据',
              imagewidget: WxAssets.images.teamEmptyIcon
                  .image(width: 180.w, height: 120.w),
            ),
          ),
        ),
      ),
    );
  }

  Widget noLocation(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: state.cityName.value == '请选择',
        child: Positioned(
          top: 430.w,
          left: 0,
          right: 0,
          child: Column(
            children: [
              Text('查看地区数据，请先开启定位授权', style: TextStyles.bold),
              SizedBox(
                height: 15.w,
              ),
              GestureDetector(
                onTap: () => LocationUtils.instance.openSettings(),
                child: WxAssets.images.icRankingLocation
                    .image(width: 192.w, fit: BoxFit.fill),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget rankings(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: !state.noData.value && state.cityName.value != '请选择',
        child: Positioned(
          top: 278.w,
          left: 0,
          right: 0,
          bottom: 0,
          child: Column(
            children: [
              Expanded(
                child: Stack(
                  children: [
                    Column(
                      children: [
                        // // 顶部背景图
                        // Obx(() => state.isPlayer.value
                        //     ? WxAssets.images.icRankingQy.image(
                        //         width: MediaQuery.of(context).size.width - 32.w,
                        //         fit: BoxFit.fitWidth,
                        //       )
                        //     : WxAssets.images.icRankingQd.image(
                        //         width: MediaQuery.of(context).size.width - 32.w,
                        //         fit: BoxFit.fitWidth,
                        //       )),
                        // 底部背景图
                        Expanded(
                          child: WxAssets.images.rankingsBigBg.image(
                            width: MediaQuery.of(context).size.width - 30.w,
                            fit: BoxFit.fill,
                          ),
                        ),
                      ],
                    ),
                    // Positioned(
                    //   top: 0,
                    //   left: 0,
                    //   right: 0,
                    //   child: SizedBox(
                    //     height: 36.w,
                    //     child: Row(
                    //       children: [
                    //         Expanded(
                    //           child: GestureDetector(
                    //             onTap: () => logic.changePlayerType(true),
                    //           ),
                    //         ),
                    //         Expanded(
                    //           child: GestureDetector(
                    //             onTap: () => logic.changePlayerType(false),
                    //           ),
                    //         ),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                    Positioned(
                      top: state.isPlayer.value ? 20.w : 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: initPageView(context),
                    ),
                  ],
                ),
              ),
            ],
          ).marginSymmetric(horizontal: 16.w),
        ),
      ),
    );
  }

  Widget initPageView(BuildContext context) {
    logic.pageController =
        PageController(initialPage: state.isPlayer.value ? 0 : 1);
    return PageView(
      controller: logic.pageController,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        // 玩家页面
        Column(
          children: [
            TabBar(
              controller: logic.playerTabController,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              labelPadding: EdgeInsets.symmetric(horizontal: 6.w),
              indicator: CustomTabIndicator(
                repaintKey: _playerTabIndicatorKey,
              ),
              indicatorColor: Colors.transparent,
              dividerColor: Colors.transparent,
              tabs: state.playerTabs
                  .map((e) => gradientTab(e, state.playerTabs.indexOf(e),
                      logic.playerTabController))
                  .toList(),
            ),
            Expanded(
              child: TabBarView(
                controller: logic.playerTabController,
                children: const [
                  PlayerView(index: 0),
                  PlayerView(index: 1),
                  PlayerView(index: 2),
                  PlayerView(index: 3),
                  PlayerView(index: 4),
                  PlayerView(index: 5),
                ],
              ),
            ),
          ],
        ),
        // 战队页面
        const TeamView(index: 0),
      ],
    );
  }

  Widget changeRankType(BuildContext context) {
    return Positioned(
      top: 210.w,
      left: 0,
      right: 0,
      child: Center(
        child: ClipRRect(
            borderRadius: BorderRadius.circular(20.w),
            child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                child: Container(
                  width: 220.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20.w),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                          child: InkWell(
                        onTap: () => logic.changePlayerType(true),
                        child: Obx(() => Container(
                              alignment: Alignment.center,
                              decoration: state.isPlayer.value
                                  ? BoxDecoration(
                                      image: DecorationImage(
                                      image: WxAssets.images.rankingSelectBg
                                          .provider(),
                                      fit: BoxFit.fill,
                                    ))
                                  : null,
                              child: Text(
                                '球员',
                                style:
                                    TextStyles.bold.copyWith(fontSize: 14.sp),
                              ),
                            )),
                      )),
                      Expanded(
                          child: InkWell(
                        onTap: () => logic.changePlayerType(false),
                        child: Obx(() => Container(
                              alignment: Alignment.center,
                              decoration: state.isPlayer.value
                                  ? null
                                  : BoxDecoration(
                                      image: DecorationImage(
                                      image: WxAssets.images.rankingSelectBg
                                          .provider(),
                                      fit: BoxFit.fill,
                                    )),
                              child: Text(
                                '球队',
                                style:
                                    TextStyles.bold.copyWith(fontSize: 14.sp),
                              ),
                            )),
                      )),
                    ],
                  ),
                ))),
      ),
    );
  }

  Widget monthBg(BuildContext context) {
    return Positioned(
      top: 103.w,
      left: 20.w,
      child: Obx(
        () {
          if (state.month.value == -1) {
            return const SizedBox.shrink();
          }
          return AssetGenImage(
                  'assets/images/ic_${state.months[state.month.value]}.png')
              .image();
        },
      ),
    );
  }

  Widget titleBar(BuildContext context) {
    return Positioned(
        top: MediaQuery.of(context).padding.top,
        left: 0,
        right: 0,
        child: SizedBox(
          height: kToolbarHeight,
          child: Stack(children: [
            IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                color: Colours.white,
                size: 20.w,
              ),
              onPressed: () {
                AppPage.back();
              },
            ),
            // Positioned(
            //   top: 0,
            //   bottom: 0,
            //   child: GestureDetector(
            //     onTap: () => AppPage.back(),
            //     child: WxAssets.images.arrowLeft.image(color: Colors.white),
            //   ),
            // ),
            // Center(
            //   child: Text(
            //     '排行榜',
            //     style: TextStyles.display16,
            //   ),
            // ),
          ]),
        ));
  }

  Widget topTitleImage(BuildContext context) {
    return Positioned(
      top: 30.w,
      left: 20.w,
      right: 20.w,
      child: WxAssets.images.topTitleImage.image(),
    );
  }

  Widget centerDesc(BuildContext context) {
    // String date = DateFormat('yyyy-MM-dd').format(DateTime.now());
    return Positioned(
      top: 160.w,
      left: 20.w,
      right: 20.w,
      child: Column(
        children: [
          // Text(
          //   'TOP100，每日更新',
          //   style: TextStyles.regular,
          // ),
          SizedBox(
            height: 25.w,
          ),
          Text(
            '截止时间:2025-09-30',
            style: TextStyle(color: const Color(0x33FFFFFF), fontSize: 12.sp),
          )
        ],
      ),
    );
  }

  Widget leftRules(BuildContext context) {
    return Positioned(
      top: 150.w,
      left: 0.w,
      child: GestureDetector(
        onTap: () => AppPage.to(Routes.rankingRulesPage),
        child: Container(
          width: 78.5,
          height: 36,
          padding: const EdgeInsets.all(10),
          decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Color(0xFFFFF9DC),
                  Color(0xFFE4C8FF),
                  Color(0xFFE5F3FF),
                ],
              ),
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(18),
                  bottomRight: Radius.circular(18))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              WxAssets.images.rulesIcon.image(
                width: 10,
                height: 12,
              ),
              Text(
                '规则',
                style: TextStyles.display12
                    .copyWith(color: const Color(0xFF0F0F16)),
              ),
              // const SizedBox(
              //   width: 8,
              // ),
              WxAssets.images.arrowRight.image(
                width: 5.5,
                height: 9,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget rightCity(BuildContext context) {
    return Positioned(
        top: 150.w,
        right: 0.w,
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            Get.bottomSheet(SelectCityDialog()).then((result) {
              if (result != null) {
                AreaModel model = result;
                if (model.id != state.cityId) {
                  if (model.name == '全国') {
                    state.cityName.value = '全国排行';
                  } else {
                    state.cityName.value = model.name ?? '';
                  }
                  state.cityId = model.id ?? 0;
                  BusUtils.instance
                      .fire(EventAction(key: EventBusKey.rankingsCitySwitch));
                }
              }
            });
          },
          child: Container(
            // width: double.infinity,
            height: 36.w,
            padding: EdgeInsets.only(left: 20.w, right: 10.w),
            decoration: const BoxDecoration(
                color: Color(0x339C4EEF),
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(18),
                    bottomLeft: Radius.circular(18))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(
                  () => Text(
                    state.cityName.value.length > 4
                        ? '${state.cityName.value.substring(0, 3)}...'
                        : state.cityName.value,
                    style: TextStyles.display12.copyWith(color: Colors.white),
                  ),
                ),
                SizedBox(
                  width: 8.w,
                ),
                Icon(
                  Icons.expand_more,
                  color: Colours.white,
                  size: 20.w,
                ),
              ],
            ),
          ),
        ));
  }

  Widget bgImage(BuildContext context) {
    return Positioned.fill(
        child: Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: WxAssets.images.rankingsBg.provider(),
          fit: BoxFit.cover,
          alignment: Alignment.topCenter,
        ),
      ),
    ));
  }

  Widget bgGradient() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF342DB0),
            Color(0xFF210085),
          ],
        ),
      ),
    );
  }

  /// 自定义 Tab，选中时渐变色，未选中时普通颜色
  Widget gradientTab(String text, int index, TabController controller) {
    return AnimatedBuilder(
      animation: controller.animation!,
      builder: (context, child) {
        bool isSelected = controller.index == index;
        return Container(
          padding: EdgeInsets.only(bottom: 4.w),
          child: Text(
            text,
            style: TextStyle(
              fontSize: isSelected ? 14.sp : 12.sp,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? Colors.white : const Color(0xCCFFFFFF),
            ),
          ),
        );
      },
    );
  }
}

class CustomTabIndicator extends Decoration {
  final GlobalKey? repaintKey;

  const CustomTabIndicator({this.repaintKey});

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CustomPainter(onChanged);
  }
}

class _CustomPainter extends BoxPainter {
  final ui.Image? _cachedImage;
  static ui.Image? _sharedCachedImage;
  static bool _isLoading = false;

  _CustomPainter(super.onChanged) : _cachedImage = _sharedCachedImage;

  static void preloadImage(BuildContext context) {
    if (_sharedCachedImage != null || _isLoading) return;

    _isLoading = true;
    // 替换为你的自定义图片路径，例如：
    // final provider = AssetImage('assets/images/your_custom_indicator.png').provider();
    final provider = WxAssets.images.imgCheckIn2.provider();

    provider.resolve(ImageConfiguration.empty).addListener(
      ImageStreamListener((ImageInfo info, bool synchronous) {
        _sharedCachedImage = info.image;
        _isLoading = false;
        // 使用回调通知重建，而不是直接调用setState
        // 这样可以避免在非StatefulWidget中调用setState的错误
      }),
    );
  }

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Rect rect = offset & configuration.size!;
    final indicatorWidth = 15.w;
    final indicatorHeight = 7.w;

    final double indicatorX = rect.left + (rect.width - indicatorWidth) / 2;
    final double indicatorY = rect.bottom - indicatorHeight + 5;

    if (_cachedImage != null) {
      canvas.drawImageRect(
        _cachedImage,
        Rect.fromLTWH(
          0,
          0,
          _cachedImage.width.toDouble(),
          _cachedImage.height.toDouble(),
        ),
        Rect.fromLTWH(indicatorX, indicatorY, indicatorWidth, indicatorHeight),
        Paint(),
      );
    }
  }
}
