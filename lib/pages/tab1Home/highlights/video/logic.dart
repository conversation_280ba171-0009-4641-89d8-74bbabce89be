import 'dart:developer';
import 'package:intl/intl.dart';
import 'package:flutter_common/api.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/more_highlights/record_model.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../../generated/l10n.dart';
import '../../../../routes/app.dart';
import '../models/highlights_model.dart';

class HighlightsVideoLogic extends GetxController {
  late VideoController videoController;
  late Videos video;
  late HighlightsModel group;
  late RecordModel record;
  var videoId = '';
  String videoPath = '';
  String arenaName = '';
  String arenaId = '';
  String videoCover = '';
  String videoName = '';
  String date = '';
  String week = '';
  int videoSize = 0;
  bool isArenaDetail = false;
  @override
  void onInit() async {
    final map = Get.arguments as Map;
    log("message7777$map");
    if (map.containsKey('group')) {
      group = map['group'];
      arenaName = group.arenaName;
      arenaId = group.arenaId;
      week = group.week;
      date = group.date;
    }
    if (map.containsKey('video')) {
      video = map['video'];
      videoId = video.id;
      videoPath = video.videoPath;
      videoCover = video.videoCover;
      videoSize = video.videoSize;
      videoName = video.name;
    }
    if (map.containsKey('record')) {
      record = map['record'];
      videoId = record.id!;
      videoPath = record.videoPath ?? '';
      videoCover = record.cover ?? '';
      videoName = record.name ?? '';
      arenaId = record.arenaId ?? '';
      DateTime parsedStartDate = DateTime.parse(record.createdTime ?? '');
      String formattedStartDate =
          DateFormat("yyyy.MM.dd").format(parsedStartDate);
      String chineseWeekday =
          DateFormat('EEEE', 'zh_CN').format(parsedStartDate);
      week = chineseWeekday;
      date = formattedStartDate;
      isArenaDetail = true;
    }
    if (map.containsKey('arenaName')) {
      arenaName = map['arenaName'];
    }
    Api().post(ApiUrl.highlightsPlay(videoId));
    videoController =
        VideoController(videoPath: videoPath, videoCover: videoCover);
    super.onInit();
  }

  @override
  void onClose() {
    videoController.dispose();
    super.onClose();
  }

  void share() async {
    MyShareH5.getShareH5(ShareHighlights(
        sharedFrom: UserManager.instance.userInfo.value?.userId ?? "",
        highlightId: videoId,
        type: "0"));
  }

  void downloadAndSaveVideo() {
    Utils.downloadAndSaveToPhotoAlbum(videoPath);
  }

  void showDeleteDialog() {
    Get.dialog(CustomAlertDialog(
      title: S.current.confirm_deletion,
      content: S.current.video_removal_tips,
      onPressed: () async {
        AppPage.back();
        final res = await Api().delete(ApiUrl.deleteHighlights + videoId);
        if (res.isSuccessful()) {
          AppPage.back(result: true);
        }
      },
    ));
  }
}
