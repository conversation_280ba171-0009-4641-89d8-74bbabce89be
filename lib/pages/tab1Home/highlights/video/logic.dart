import 'package:flutter_common/api.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../../generated/l10n.dart';
import '../../../../routes/app.dart';
import '../models/highlights_model.dart';

class HighlightsVideoLogic extends GetxController {
  late VideoController videoController;
  late Videos video;
  late HighlightsModel group;
  @override
  void onInit() async {
    final map = Get.arguments as Map;
    video = map['video'];
    group = map['group'];
    Api().post(ApiUrl.highlightsPlay(video.id));
    videoController = VideoController(
        videoPath: video.videoPath, videoCover: video.videoCover);
    super.onInit();
  }

  @override
  void onClose() {
    videoController.dispose();
    super.onClose();
  }

  void share() async {
    MyShareH5.getShareH5(ShareHighlights(
        sharedFrom: UserManager.instance.userInfo.value?.userId ?? "",
        highlightId: video.id,
        type: "0"));
  }

  void downloadAndSaveVideo() {
    Utils.downloadAndSaveToPhotoAlbum(video.videoPath);
  }

  void showDeleteDialog() {
    Get.dialog(CustomAlertDialog(
      title: S.current.confirm_deletion,
      content: S.current.video_removal_tips,
      onPressed: () async {
        AppPage.back();
        final res = await Api().delete(ApiUrl.deleteHighlights + video.id);
        if (res.isSuccessful()) {
          AppPage.back(result: true);
        }
      },
    ));
  }
}
