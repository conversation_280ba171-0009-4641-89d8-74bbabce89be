import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/inappwebview/router.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item1/tab_home_item_view1.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item2/tab_home_item_view2.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item3/tab_home_item_view3.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item4/tab_home_item_view4.dart';
import 'package:shoot_z/pages/tab1Home/tab_home_logic.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item5/tab_home_item_view5.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

///一级页面 首页
class TabHomePage extends StatelessWidget {
  TabHomePage({super.key});

  final logic = Get.put(TabHomeLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true, // 关键设置
      backgroundColor: Colors.transparent,
      body: Obx(() {
        return Center(
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : Stack(children: [
                  WxAssets.images.homeImgBg.image(),
                  Column(
                    children: [
                      SizedBox(
                        height: ScreenUtil().statusBarHeight,
                      ),
                      Expanded(
                        child: DefaultTabController(
                            length: 4,
                            child: NestedScrollView(
                              controller: logic.scrollController,
                              headerSliverBuilder:
                                  (context, innerBoxIsScrolled) {
                                return [
                                  SliverToBoxAdapter(
                                    child: teamAppBar(context),
                                  ),
                                  // 轮播图区域
                                  SliverToBoxAdapter(
                                    child: banner(),
                                  ),
                                  // SliverToBoxAdapter(
                                  //   child: tabBarWidget(),
                                  // ),
                                  SliverPersistentHeader(
                                    pinned: true,
                                    delegate: _MinimalTabBarDelegate(
                                      tabBar: TabBar(
                                          controller: logic.tabController,
                                          unselectedLabelColor:
                                              Colours.color5C5C6E,
                                          unselectedLabelStyle: TextStyle(
                                              fontSize: 18.sp,
                                              color: Colours.color5C5C6E,
                                              fontWeight: FontWeight.w600),
                                          labelColor: Colours.white,
                                          labelStyle: TextStyle(
                                              fontSize: 20.sp,
                                              color: Colours.white,
                                              fontWeight: FontWeight.w600),
                                          isScrollable: true,
                                          // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                                          indicatorPadding: EdgeInsets.zero,
                                          dividerColor: Colors.transparent,
                                          indicatorColor:
                                              Colors.transparent, // 设置指示器颜色为透明
                                          indicator: const BoxDecoration(
                                              color: Colors
                                                  .transparent), // 使用空装饰完全移除指示器
                                          dividerHeight: 0,
                                          labelPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 4.0), // 调整标签间的间距
                                          indicatorSize:
                                              TabBarIndicatorSize.label,
                                          padding: EdgeInsets.zero,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          tabAlignment: TabAlignment.start,
                                          tabs: List.generate(
                                              logic.tabNameList.length,
                                              (index) {
                                            return Obx(() {
                                              return SizedBox(
                                                width: 50.w,
                                                height: 66.w,
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    SizedBox(
                                                      height: 20.w,
                                                    ),
                                                    ShaderMask(
                                                      shaderCallback: (bounds) =>
                                                          const LinearGradient(
                                                        colors: [
                                                          Colours.colorFFF9DC,
                                                          Colours.colorE4C8FF,
                                                          Colours.colorE5F3FF,
                                                        ],
                                                        begin:
                                                            Alignment.topLeft,
                                                        end: Alignment
                                                            .bottomRight,
                                                      ).createShader(bounds),
                                                      child: Text(
                                                        logic
                                                            .tabNameList[index],
                                                        style: TextStyles
                                                            .regular
                                                            .copyWith(
                                                          fontWeight: logic
                                                                      .tabbarIndex
                                                                      .value ==
                                                                  index
                                                              ? FontWeight.w600
                                                              : FontWeight.w400,
                                                          fontSize: logic
                                                                      .tabbarIndex
                                                                      .value ==
                                                                  index
                                                              ? 16.sp
                                                              : 14.sp,
                                                          color: logic.tabbarIndex
                                                                      .value ==
                                                                  index
                                                              ? Colours.white
                                                              : Colours
                                                                  .color5C5C6E,
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 5.w,
                                                    ),
                                                    if (logic.tabbarIndex
                                                            .value ==
                                                        index)
                                                      WxAssets
                                                          .images.imgCheckIn2
                                                          .image(
                                                              width: 19.w,
                                                              height: 9.w),
                                                  ],
                                                ),
                                              );
                                            });
                                          })),
                                    ),
                                  ),
                                ];
                              },
                              body: TabBarView(
                                controller: logic.tabController,
                                children: [
                                  TabHomeItemPage1(
                                    key: const Key("1"),
                                  ),
                                  TabHomeItemPage2(
                                    key: const Key("2"),
                                  ),
                                  TabHomeItemPage3(
                                    key: const Key("3"),
                                  ),
                                  const TabHomeItemPage4(
                                    key: Key("4"),
                                  ),
                                  TabHomeItemPage5(
                                    key: const Key("5"),
                                  ),
                                ],
                              ),
                            )),
                      ),
                    ],
                  )
                ]),
        );
      }),
      floatingActionButton: Obx(() {
        return logic.tabbarIndex.value != 2
            ? SizedBox()
            : FloatingActionButton(
                onPressed: () {
                  AppPage.to(Routes.myBattlePage);
                },
                backgroundColor: Colors.transparent, // 提示信息（长按显示）
                child: SizedBox(
                  width: 50.w,
                  height: 50.w,
                  child: WxAssets.images.homeYueZhan
                      .image(width: 50.w, height: 50.w),
                ), // 按钮背景颜色
              );
      }),
    );
  }

  Widget banner() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: 10.w),
      child: Column(
        children: [
          Obx(
            () => CarouselSlider(
              carouselController: logic.carouselController,
              options: CarouselOptions(
                // 核心配置：设置视图比例（小于1让两侧可见）
                viewportFraction: 0.84, // 80%宽度用于当前项目
                // 高度比例（保持图片不变形）
                aspectRatio: 295 / 154,
                // 启用两侧预览（必须设置为true）
                enlargeCenterPage: true,
                // 控制两侧预览的显示范围
                enlargeStrategy: CenterPageEnlargeStrategy.zoom,
                enlargeFactor: 0.07, // 两侧展示20%的预览
                // 自动播放（可选）
                autoPlay: true,
                autoPlayInterval: const Duration(seconds: 3),
                onPageChanged: (index, reason) {
                  // index: 当前页面的索引
                  // reason: 切换的原因（用户滑动、自动播放、手动调用等）
                  //  print('当前页面索引: $index');
                  logic.currentIndex.value = index;
                },
              ),
              items: logic.imgBannerList.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isCenter = index == logic.currentIndex.value;
                final height = isCenter ? 154.w : 130.w;
                final opacity = isCenter ? 1.0 : 0.8;

                return Builder(
                  builder: (BuildContext context) {
                    return GestureDetector(
                      onTap: () async {
                        switch (item.action) {
                          //banner跳转
                          // Id      int    `json:"id,string"`
                          // Name    string `json:"name"`    // 资源名称
                          // Path    string `json:"path"`    // 图片地址
                          // Action  string `json:"action"`  // 点击操作类型
                          // SubPath string `json:"subPath"` // 点击操作用的路径 如页面地址、如网页、如参数等
                          //action:1 app页面；2 外部网页；3 内部网页地址
                          // action=1 & subPath== 1 排行榜 2.会员中心 3.我的球队  4.赛程列表 5.比赛日历 6.积分商城 7.生涯与集锦
                          case 1:
                            if (item.subPath == "1") {
                              AppPage.to(Routes.rankingsPage);
                            } else if (item.subPath == "2") {
                              AppPage.to(Routes.vipPage, needLogin: true);
                            } else if (item.subPath == "3") {
                              AppPage.to(Routes.teamListPage, needLogin: true);
                            } else if (item.subPath == "4") {
                              AppPage.to(Routes.scheduleHomePage, arguments: {
                                'type': 1,
                              });
                            } else if (item.subPath == "5") {
                              AppPage.to(Routes.scheduleHomePage, arguments: {
                                'type': 0,
                              });
                            } else if (item.subPath == "6") {
                              AppPage.to(Routes.pointsMallPage,
                                  arguments: {"type": "1"}, needLogin: true);
                            } else if (item.subPath == "7") {
                              AppPage.to(Routes.careerHighlightsHomePage,
                                  needLogin: true);
                            }
                            break;
                          case 2:
                            if (item.subPath != "") {
                              if (await canLaunchUrlString(
                                  item.subPath ?? "")) {
                                // ignore: deprecated_member_use
                                await launch(item.subPath ?? "",
                                    forceSafariVC: true);
                              }
                            }
                            break;
                          case 3:
                            if (item.subPath != "") {
                              WebviewRouter router = WebviewRouter(
                                  url: item.subPath,
                                  showNavigationBar: true,
                                  needBaseHttp: false,
                                  title: item.name ?? "");
                              AppPage.to(Routes.webview, arguments: router);
                            }

                            break;
                        }
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(18.r), // 设置圆角
                        child: Opacity(
                          opacity: opacity,
                          child: Container(
                            width: double.infinity,
                            margin: EdgeInsets.only(
                                left: 7.5.w,
                                right: 7.5.w,
                                top: !isCenter ? 24.w : 0.w),
                            child: MyImage(
                              item.path ?? "",
                              width: 295.w,
                              height: height,
                              radius: 12.r,
                              errorImage: "error_image_width.png",
                              placeholderImage: "error_image_width.png",
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              }).toList(),
            ),
          ),
          Obx(
            () => Visibility(
              visible: logic.imgBannerList.length > 1,
              child: Align(
                alignment: const Alignment(0.0, 0.9),
                child: AnimatedSmoothIndicator(
                  duration: const Duration(milliseconds: 500),
                  activeIndex: logic.currentIndex.value,
                  count: logic.imgBannerList.length,
                  effect: const ExpandingDotsEffect(
                    dotColor: Colours.color5C5C6E,
                    activeDotColor: Colours.colorA44EFF,
                    dotHeight: 4,
                    dotWidth: 8,
                    expansionFactor: 2.25,
                    spacing: 5,
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Container teamAppBar(context) {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      child: Row(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.to(Routes.highlightsPage);
            },
            child: Container(
              margin: EdgeInsets.only(left: 15.w),
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 7.w),
              decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(
                      "assets/images/home_img_bottom.png",
                    ),
                    fit: BoxFit.fill),
              ),
              child: Row(
                children: [
                  WxAssets.images.tabHomeImgPlay
                      .image(width: 14.w, height: 14.w),
                  SizedBox(
                    width: 8.w,
                  ),
                  Text(
                    S.current.highlights,
                    style: TextStyles.regular.copyWith(fontSize: 12.sp),
                  )
                  // WxAssets.images.homeImgBottom;
                ],
              ),
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.to(Routes.rankingsPage);
            },
            child: Container(
              margin: EdgeInsets.only(left: 15.w),
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 7.w),
              decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(
                      "assets/images/home_img_bottom_long.png",
                    ),
                    fit: BoxFit.fill),
              ),
              child: Row(
                children: [
                  WxAssets.images.fiveStarsIcon
                      .image(width: 14.w, height: 14.w),
                  SizedBox(
                    width: 8.w,
                  ),
                  Text(
                    S.current.tab_home_bottom,
                    style: TextStyles.regular.copyWith(fontSize: 12.sp),
                  )
                  // WxAssets.images.homeImgBottom;
                ],
              ),
            ),
          ),
          const Spacer(),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.to(Routes.messageTypePage, needLogin: true);
            },
            child: Container(
              width: 53.w,
              padding: EdgeInsets.only(right: 20.w),
              child: Stack(
                alignment: Alignment.centerRight,
                children: [
                  WxAssets.images.homeMessage.image(width: 20.w, height: 20.w),
                  Positioned(
                    top: 1,
                    child: Obx(() => UserManager.instance.messageHasUnreadModel
                                .value?.hasUnread ==
                            true
                        ? Container(
                            width: 6.w,
                            height: 6.w,
                            decoration: BoxDecoration(
                                color: Colours.red,
                                borderRadius: BorderRadius.circular(3.r)),
                          )
                        : const SizedBox()),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 自定义委托类
class _MinimalTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _MinimalTabBarDelegate({required this.tabBar});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colours.color0F0F16,
      child: tabBar,
    );
  }

  @override
  double get maxExtent => 66.w; // 最小高度

  @override
  double get minExtent => 66.w; // 最小高度

  @override
  bool shouldRebuild(_MinimalTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
}
