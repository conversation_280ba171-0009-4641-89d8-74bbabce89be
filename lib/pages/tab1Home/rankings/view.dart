// ignore_for_file: invalid_use_of_protected_member

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/rankings/player/view.dart';
import 'package:shoot_z/pages/tab1Home/rankings/team/view.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:ui_packages/ui_packages.dart';
import 'dart:ui' as ui;
import '../../../routes/app.dart';
import 'logic.dart';

class RankingsPage extends StatefulWidget {
  const RankingsPage({super.key});

  @override
  State<RankingsPage> createState() => _RankingsPageState();
}

class _RankingsPageState extends State<RankingsPage> {
  final logic = Get.put(RankingsLogic());
  final state = Get.find<RankingsLogic>().state;
  final _playerTabIndicatorKey = GlobalKey();
  final _teamTabIndicatorKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // 预加载指示器图片
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _CustomPainter.preloadImage(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: buildUI(context),
    );
  }

  Widget buildUI(BuildContext context) {
    return Stack(children: [
      // 渐变背景
      bgGradient(),
      // 图片布局
      bgImage(context),
      titleBar(context),
      monthBg(context),
      //切换选择
      changeRankType(context),
      // 排行榜
      rankings(context),
      //未开启定位
      noLocation(context),
      // 地区数据为空
      dataEmpty(context),
      // 选择地区
      selectCity(context),
      // 个人排行
      myRanking(context),
    ]);
  }

  Widget selectCity(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: !state.isAll.value,
        child: Positioned(
            top: 290.w,
            left: 274.w,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () => logic.showCityPicker(),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Obx(
                    () => Text(
                      state.cityName.value.length > 4
                          ? '${state.cityName.value.substring(0, 3)}...'
                          : state.cityName.value,
                      style: TextStyles.bold.copyWith(fontSize: 12.sp),
                    ),
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  WxAssets.images.icCityArrow
                      .image(width: 12.w, fit: BoxFit.fill),
                ],
              ),
            )),
      ),
    );
  }

  Widget myRanking(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: UserManager.instance.isLoginObs.value &&
            state.isPlayer.value &&
            !state.noData.value,
        child: Positioned(
          left: 20.w,
          right: 20.w,
          bottom: MediaQuery.of(context).padding.bottom > 0
              ? MediaQuery.of(context).padding.bottom
              : 20.w,
          child: Container(
            height: 48.w,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: WxAssets.images.icRankingSelf.provider(),
                fit: BoxFit.fill,
              ),
            ),
            child: Row(
              children: [
                SizedBox(
                  width: 24.w,
                ),
                SizedBox(
                  width: 37.w,
                  child: Obx(
                    () => Text(
                      '${(state.numbers[state.playerTabIndex.value] > 100 || state.numbers[state.playerTabIndex.value] == 0) ? '未上榜' : state.numbers[state.playerTabIndex.value]}',
                      textAlign: TextAlign.center,
                      style: TextStyles.bold.copyWith(fontSize: 12.sp),
                    ),
                  ),
                ),
                SizedBox(
                  width: 37.w,
                ),
                ClipRRect(
                  borderRadius: BorderRadius.circular(12.w),
                  child: CachedNetworkImage(
                    imageUrl: UserManager.instance.userInfo.value?.avatar ?? '',
                    width: 24.w,
                    height: 24.w,
                    fit: BoxFit.fill,
                  ),
                ),
                SizedBox(
                  width: 8.w,
                ),
                Text(
                  UserManager.instance.userInfo.value?.userName ?? '',
                  style: TextStyles.bold.copyWith(fontSize: 12.sp),
                ),
                const Spacer(),
                SizedBox(
                  width: 48.w,
                  child: Obx(
                    () => Text(
                      state.myRankData[state.playerTabIndex.value] == '0'
                          ? '---'
                          : state.myRankData[state.playerTabIndex.value],
                      textAlign: TextAlign.center,
                      style: TextStyles.regular.copyWith(fontSize: 20.sp),
                    ),
                  ),
                ),
                SizedBox(
                  width: 27.w,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget dataEmpty(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: !state.isAll.value &&
            state.noData.value &&
            state.cityName.value != '请选择',
        child: Positioned(
          top: 394.w,
          left: 0,
          right: 0,
          child: Center(
            child: WxAssets.images.icRankingEmpty
                .image(width: 116.w, fit: BoxFit.fill),
          ),
        ),
      ),
    );
  }

  Widget noLocation(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: !state.isAll.value && state.cityName.value == '请选择',
        child: Positioned(
          top: 430.w,
          left: 0,
          right: 0,
          child: Column(
            children: [
              Text('查看地区数据，请先开启定位授权', style: TextStyles.bold),
              SizedBox(
                height: 15.w,
              ),
              GestureDetector(
                onTap: () => LocationUtils.instance.openSettings(),
                child: WxAssets.images.icRankingLocation
                    .image(width: 192.w, fit: BoxFit.fill),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget rankings(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: state.isAll.value ||
            (!state.noData.value && state.cityName.value != '请选择'),
        child: Positioned(
          top: 327.w,
          left: 0,
          right: 0,
          bottom: 0,
          child: Column(
            children: [
              Expanded(
                child: Stack(
                  children: [
                    Column(
                      children: [
                        // 顶部背景图
                        Obx(() => state.isPlayer.value
                            ? WxAssets.images.icRankingQy.image(
                                width: MediaQuery.of(context).size.width - 32.w,
                                fit: BoxFit.fitWidth,
                              )
                            : WxAssets.images.icRankingQd.image(
                                width: MediaQuery.of(context).size.width - 32.w,
                                fit: BoxFit.fitWidth,
                              )),
                        // 底部背景图
                        Expanded(
                          child: WxAssets.images.icRankingListBg.image(
                            width: MediaQuery.of(context).size.width - 32.w,
                            fit: BoxFit.fill,
                          ),
                        ),
                      ],
                    ),
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: SizedBox(
                        height: 36.w,
                        child: Row(
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () => logic.changePlayerType(true),
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: () => logic.changePlayerType(false),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      top: 51.w,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: initPageView(context),
                    ),
                  ],
                ),
              ),
            ],
          ).marginSymmetric(horizontal: 16.w),
        ),
      ),
    );
  }

  Widget initPageView(BuildContext context) {
    logic.pageController =
        PageController(initialPage: state.isPlayer.value ? 0 : 1);
    return PageView(
      controller: logic.pageController,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        // 玩家页面
        Column(
          children: [
            TabBar(
              controller: logic.playerTabController,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              labelPadding: EdgeInsets.symmetric(horizontal: 6.w),
              indicator: CustomTabIndicator(
                repaintKey: _playerTabIndicatorKey,
              ),
              indicatorColor: Colors.transparent,
              dividerColor: Colors.transparent,
              tabs: state.playerTabs
                  .map((e) => gradientTab(e, state.playerTabs.indexOf(e),
                      logic.playerTabController))
                  .toList(),
            ),
            Expanded(
              child: TabBarView(
                controller: logic.playerTabController,
                children: const [
                  PlayerView(index: 0),
                  PlayerView(index: 1),
                  PlayerView(index: 2),
                  PlayerView(index: 3),
                  PlayerView(index: 4),
                  PlayerView(index: 5),
                ],
              ),
            ),
          ],
        ),
        // 战队页面
        Column(
          children: [
            TabBar(
              controller: logic.teamTabController,
              indicator: CustomTabIndicator(
                repaintKey: _teamTabIndicatorKey,
              ),
              indicatorColor: Colors.transparent,
              dividerColor: Colors.transparent,
              isScrollable: true,
              tabAlignment: TabAlignment.center,
              labelPadding: EdgeInsets.symmetric(horizontal: 20.w),
              tabs: state.teamTabs
                  .map((e) => gradientTab(
                      e, state.teamTabs.indexOf(e), logic.teamTabController))
                  .toList(),
            ),
            Expanded(
              child: TabBarView(
                controller: logic.teamTabController,
                children: const [
                  TeamView(index: 0),
                  TeamView(index: 1),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget changeRankType(BuildContext context) {
    return Positioned(
      top: 279.w,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          width: 160.w,
          height: 36.w,
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            image: DecorationImage(
              image: WxAssets.images.icRankingSwitchBg.provider(),
              fit: BoxFit.fill,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                  child: GestureDetector(
                onTap: () => logic.changeRankType(true),
                child: Obx(() => Container(
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.w),
                          color: state.isAll.value
                              ? Colours.color752A0073
                              : Colors.transparent),
                      child: Text(
                        '全国排行',
                        style: TextStyles.bold.copyWith(fontSize: 14.sp),
                      ),
                    )),
              )),
              Expanded(
                  child: GestureDetector(
                onTap: () => logic.changeRankType(false),
                child: Obx(() => Container(
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.w),
                          color: !state.isAll.value
                              ? Colours.color752A0073
                              : Colors.transparent),
                      child: Text(
                        '地区排行',
                        style: TextStyles.bold.copyWith(fontSize: 14.sp),
                      ),
                    )),
              )),
            ],
          ),
        ),
      ),
    );
  }

  Widget monthBg(BuildContext context) {
    return Positioned(
      top: 103.w,
      left: 20.w,
      child: Obx(
        () {
          if (state.month.value == -1) {
            return const SizedBox.shrink();
          }
          return AssetGenImage(
                  'assets/images/ic_${state.months[state.month.value]}.png')
              .image();
        },
      ),
    );
  }

  Widget titleBar(BuildContext context) {
    return Positioned(
        top: MediaQuery.of(context).padding.top,
        left: 0,
        right: 0,
        child: SizedBox(
          height: kToolbarHeight,
          child: Stack(children: [
            Positioned(
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onTap: () => AppPage.back(),
                child: WxAssets.images.arrowLeft.image(color: Colors.white),
              ),
            ),
            Center(
              child: Text(
                '排行榜',
                style: TextStyles.display16,
              ),
            ),
          ]),
        ));
  }

  Widget bgImage(BuildContext context) {
    return Positioned.fill(
        child: Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: WxAssets.images.icRankingBg.provider(),
          fit: BoxFit.cover,
          alignment: Alignment.topCenter,
        ),
      ),
    ));
  }

  Widget bgGradient() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF342DB0),
            Color(0xFF210085),
          ],
        ),
      ),
    );
  }

  /// 自定义 Tab，选中时渐变色，未选中时普通颜色
  Widget gradientTab(String text, int index, TabController controller) {
    return AnimatedBuilder(
      animation: controller.animation!,
      builder: (context, child) {
        bool isSelected = controller.index == index;
        return Container(
          padding: EdgeInsets.only(bottom: 4.w),
          child: ShaderMask(
            shaderCallback: (bounds) {
              return LinearGradient(
                colors: isSelected
                    ? [Colours.color9E6EFF, Colours.colorC3ABFF]
                    : [Colours.colorD6D6D6, Colours.colorD6D6D6],
              ).createShader(bounds);
            },
            child: Text(
              text,
              style: TextStyle(
                fontSize: isSelected ? 14.sp : 12.sp,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }
}

class CustomTabIndicator extends Decoration {
  final GlobalKey? repaintKey;

  const CustomTabIndicator({this.repaintKey});

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CustomPainter(onChanged);
  }
}

class _CustomPainter extends BoxPainter {
  final ui.Image? _cachedImage;
  static ui.Image? _sharedCachedImage;
  static bool _isLoading = false;

  _CustomPainter(VoidCallback? onChanged)
      : _cachedImage = _sharedCachedImage,
        super(onChanged);

  static void preloadImage(BuildContext context) {
    if (_sharedCachedImage != null || _isLoading) return;

    _isLoading = true;
    final provider = WxAssets.images.icRankingIndicator.provider();

    provider.resolve(ImageConfiguration.empty).addListener(
      ImageStreamListener((ImageInfo info, bool synchronous) {
        _sharedCachedImage = info.image;
        // 通知 StatefulWidget 进行重建
        if (context.findAncestorStateOfType<_RankingsPageState>() != null) {
          context
              .findAncestorStateOfType<_RankingsPageState>()
              ?.setState(() {});
        }
      }),
    );
  }

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Rect rect = offset & configuration.size!;
    final indicatorWidth = 16.w;
    final indicatorHeight = 3.w;

    final double indicatorX = rect.left + (rect.width - indicatorWidth) / 2;
    final double indicatorY = rect.bottom - indicatorHeight;

    if (_cachedImage != null) {
      canvas.drawImageRect(
        _cachedImage,
        Rect.fromLTWH(
          0,
          0,
          _cachedImage.width.toDouble(),
          _cachedImage.height.toDouble(),
        ),
        Rect.fromLTWH(indicatorX, indicatorY, indicatorWidth, indicatorHeight),
        Paint(),
      );
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
