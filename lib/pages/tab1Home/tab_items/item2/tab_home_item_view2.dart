import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/home_hot_record_model.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/widgets/match_item_widget.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item2/tab_home_item_logic2.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item4/competitions_list_item_widget.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';

///首页 一级页面->赛事
class TabHomeItemPage2 extends StatelessWidget {
  TabHomeItemPage2({super.key});
  final logic = Get.put(TabHomeItemLogic2());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return CustomScrollView(
        slivers: [
          _hotGamesWidget(context),
          _hotSchedule(context),
        ],
      );
    });
  }

  Widget _hotGamesWidget(context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(S.current.hot_games, rightOnTap: () {
            log('!!!!!!!!!');
            AppPage.to(
              Routes.scheduleHomePage,
            );
          }),
          logic.isFrist.value
              ? buildLoad(isShowGif: false)
              : (logic.homeHotRecordModel.value.matches?.isEmpty ?? true)
                  ? myNoDataView(context,
                      msg: S.current.No_data_available,
                      imagewidget: WxAssets.images.teamInfoNodata
                          .image(width: 107.w, height: 72.w),
                      height: 2.w)
                  : Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(
                            logic.homeHotRecordModel.value.matches?.length ?? 0,
                            (position) {
                          final match =
                              logic.homeHotRecordModel.value.matches![position];
                          if (match == null) {
                            return const SizedBox
                                .shrink(); // Return empty widget for null matches
                          }
                          return MatchItemWidget.fromMatchesModel(
                            model: match,
                            showStatus: true,
                          );
                        }),
                      ),
                    )
        ],
      ),
    );
  }

  Widget _hotSchedule(context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(S.current.hot_schedule,
              margin: EdgeInsets.only(top: 6.w), fontSize: 16, rightOnTap: () {
            AppPage.to(Routes.scheduleHomePage, arguments: {
              'type': 1,
            });
          }),
          SizedBox(
            height: 6.w,
          ),
          logic.isFrist.value
              ? buildLoad(isShowGif: false)
              : (logic.homeHotRecordModel.value.competitions?.isEmpty ?? true)
                  ? myNoDataView(context,
                      msg: S.current.No_data_available,
                      imagewidget: WxAssets.images.teamInfoNodata
                          .image(width: 107.w, height: 72.w),
                      height: 2.w)
                  : Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(
                            logic.homeHotRecordModel.value.competitions
                                    ?.length ??
                                0, (position) {
                          return CompetitionsListItemWidget(
                            model: logic.homeHotRecordModel.value
                                    .competitions![position] ??
                                HomeHotRecordModelCompetitions(),
                          );
                        }),
                      ),
                    )
        ],
      ),
    );
  }
}
