import 'dart:developer';

import 'package:better_player/better_player.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/model/featured_list_model.dart';
import 'package:shoot_z/utils/myShareH5.dart';

class VideoPlayerLogic extends GetxController {
  late BetterPlayerController betterPlayerController;
  BetterPlayerDataSource? _betterPlayerDataSource;
  var playbackRates = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]; //倍数
  var currentPlaybackRate = 1.0.obs; //选中的倍数
  var type = "0".obs; //0集锦  普通集锦  1半场集锦
  var featuredListModel = FeaturedListModel().obs; //标题1
  var isShare = "0".obs; //是否分享 1修改
  var isUpdate = "0".obs; //是否修改 0不修改
  var isHorizontal = true.obs;
  var aspectRatio = (9 / 16).obs;
  var isFrist = true.obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('type')) {
      type.value = Get.arguments['type'] ?? '';
    }
    if (Get.arguments != null &&
        Get.arguments.containsKey('featuredListModel')) {
      featuredListModel.value = Get.arguments['featuredListModel'] ?? '';
    }
    if (featuredListModel.value.videoUrl?.isNotEmpty ?? false) {
      _initializePlayer();
    }
  }

  Future<void> _initializePlayer() async {
    isFrist.value = true;
    // 竖屏视频示例URL
    var verticalVideoUrl = featuredListModel.value.videoUrl ?? "";
    // "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/shootz_sys/kol/20250519-1205351753695263976.mp4";
    //"https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/shootz_sys/kol/%E4%BA%8C%E4%BB%A3%E5%9C%BA%E6%99%AF%E4%B8%801753772645596.mp4";
    log("verticalVideoUrl=$verticalVideoUrl");
    _betterPlayerDataSource = BetterPlayerDataSource(
      BetterPlayerDataSourceType.network,
      bufferingConfiguration: const BetterPlayerBufferingConfiguration(
        minBufferMs: 3000, // 最小缓冲时间（毫秒）
        maxBufferMs: 15000, // 最大缓冲时间
        bufferForPlaybackMs: 500, // 开始播放前缓冲
        bufferForPlaybackAfterRebufferMs: 2000, // 重新缓冲后播放前的缓冲
      ),
      headers: {
        "Connection": "keep-alive",
        "Range": "bytes=0-", // 启用范围请求
        "Timeout": "30000"
      },

      cacheConfiguration: const BetterPlayerCacheConfiguration(
        useCache: true,
        maxCacheSize: 20 * 1024 * 1024, // 10MB缓存
        maxCacheFileSize: 5 * 1024 * 1024, // 单个文件最大5MB
        preCacheSize: 10 * 1024 * 1024,
      ),
      placeholder: const Center(child: CircularProgressIndicator()),
      // videoFormat: BetterPlayerVideoFormat.hls,
      verticalVideoUrl,
      //"https://vod.pule.com/6c992c3bvodcq1500003583/af54817b5285890813370412301/f0.mp4",
      // "https://vd4.bdstatic.com/mda-nf7hhj7wqge0a2s8/mda-nf7hhj7wqge0a2s8.mp4", //  videoUrl,
      liveStream: false,
    );
    betterPlayerController = BetterPlayerController(
      BetterPlayerConfiguration(
          autoPlay: true,
          looping: false,
          fit: BoxFit.fitWidth,
          translations: [BetterPlayerTranslations.chinese()],
          deviceOrientationsAfterFullScreen: [
            DeviceOrientation.portraitDown,
            DeviceOrientation.portraitUp
          ],
          controlsConfiguration: const BetterPlayerControlsConfiguration(
            playerTheme: BetterPlayerTheme.material,
            showControlsOnInitialize: false, // 初始化时显示控制栏
            enableProgressText: true, // 是否显示进度文本（如当前时间/总时间）
            enableFullscreen: true, // 是否显示全屏按钮
            enableSkips: false, // 是否显示跳过片头/片尾按钮（通常用于长视频）
            enableOverflowMenu: false, // 是否显示右上角的更多选项菜单（三点图标）
            enableMute: true, // 是否显示静音按钮
            enablePlayPause: true, // 是否显示播放/暂停按钮
            enableProgressBar: true, // 是否显示进度条
            enableProgressBarDrag: true, // 是否允许拖动进度条
            enableSubtitles: false, // 是否显示字幕切换按钮
            enableQualities: false, // 是否显示画质切换按钮
            enablePip: false, // 是否显示画中画（Picture-in-Picture）按钮
          ),
          aspectRatio: 9 / 16),
      betterPlayerDataSource: _betterPlayerDataSource,
    );
    // 监听视频初始化事件
    betterPlayerController.addEventsListener((event) {
      if (event.betterPlayerEventType == BetterPlayerEventType.initialized) {
        // 检测视频方向
        final videoWidth =
            betterPlayerController.videoPlayerController!.value.size?.width ??
                1;
        final videoHeight =
            betterPlayerController.videoPlayerController!.value.size?.height ??
                0;
        log("betterPlayerController2=$videoWidth $videoHeight");
        isHorizontal.value = videoHeight > videoWidth ? false : true;
        aspectRatio.value = videoHeight > videoWidth ? 9 / 16 : 16 / 9;
        refresh();
        if (isFrist.value) {
          isFrist.value = false;
        }
      }
    });
    //   betterPlayerController.setControlsEnabled(false);
  }

  @override
  void onClose() {
    super.onClose();
    betterPlayerController.dispose();
  }

  void share() async {
    ///sharePlay?id=6&type=1&header=1
    //type //0 精选视频  1 教学视频
    //header //0 显示顶部打开app 1 不显示

    switch (type.value) {
      case "0": //普通半场
      case "1": //半场集锦
        MyShareH5.getShareH5(
          ShareVideosId(
              videosId: (featuredListModel.value.id ?? "").toString(),
              header: "0",
              type: "0"),
        ); // //0普通集锦  1半场集锦
        break;
      case "2": //教学分享
        MyShareH5.getShareH5(
          ShareVideosId(
              videosId: (featuredListModel.value.id ?? "").toString(),
              header: "0",
              type: "1"),
        ); // //0普通集锦  1半场集锦
        break;
    }
  }

  void downloadAndSaveVideo() {
    // Utils.downloadAndSaveToPhotoAlbum(videoPath.value);
  }

  // void showDeleteDialog() {
  //   Get.dialog(CustomAlertDialog(
  //     title: S.current.confirm_deletion,
  //     content: S.current.video_removal_tips,
  //     onPressed: () async {
  //       AppPage.back();
  //       getDeleteVideo();
  //     },
  //   ));
  // }

  // Future<void> getDeleteVideo() async {
  //   Map<String, dynamic> param2 = {"id": videoId.value};
  //   var url = await ApiUrl.getDeleteVideo(videoId.value);
  //   var res = await Api().delete(url, data: param2);
  //   log("getDeleteVideo=${videoId.value}-${res.data}");
  //   if (res.isSuccessful()) {
  //     AppPage.back(result: true);
  //   } else {
  //     WxLoading.showToast(res.message);
  //   }
  // }
}
