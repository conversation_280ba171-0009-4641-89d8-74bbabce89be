// ignore_for_file: must_be_immutable

import 'dart:async';
import 'dart:developer';

import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/featured_list_model.dart';
import 'package:shoot_z/pages/tab1Home/short_videos2/short_videos2_logic.dart';
import 'package:shoot_z/pages/tab1Home/short_videos2/videoGesture.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class VideoFeed2Screen extends StatelessWidget {
  const VideoFeed2Screen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<ShortVideo2Controller>(
        init: ShortVideo2Controller(),
        builder: (controller) {
          return NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              if (notification is ScrollEndNotification &&
                  notification.metrics.extentAfter < 500 &&
                  controller.hasMore.value &&
                  !controller.isLoading.value) {
                controller.loadVideos(loadMore: true);
              }
              return false;
            },
            child: Obx(() {
              return (controller.dataFag["isFrist"] as bool)
                  ? buildLoad()
                  : (controller.dataList.isEmpty == true)
                      ? Expanded(
                          child: myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 105.w, height: 89.w),
                        ))
                      : PageView.builder(
                          scrollDirection: Axis.vertical,
                          itemCount: controller.dataList.length,
                          onPageChanged: (index) {
                            controller.changeVideo(index);
                            if (index >= controller.dataList.length - 5 &&
                                controller.hasMore.value &&
                                !controller.isLoading.value) {
                              controller.loadVideos(loadMore: true);
                            }
                          },
                          controller: PageController(
                            viewportFraction: 1.0,
                            initialPage: controller.currentIndex.value,
                          ),
                          itemBuilder: (context, index) {
                            if (index >= controller.dataList.length) {
                              return const SizedBox.shrink();
                            }

                            final video = controller.dataList[index];
                            final isLoading = controller.loadingControllers
                                .containsKey("${video.id ?? 0}");

                            // 预加载下一个视频
                            if (index < controller.dataList.length - 1) {
                              controller.initializeVideo(
                                  controller.dataList[index + 1], index + 1);
                            }
                            if (index < controller.dataList.length - 2) {
                              controller.initializeVideo(
                                  controller.dataList[index + 2], index + 2);
                            }
                            if (index > 0) {
                              controller.initializeVideo(
                                  controller.dataList[index - 1], index - 1);
                            }
                            // if (index > 1) {
                            //   controller.initializeVideo(
                            //       controller.dataList[index - 2], index - 2);
                            // }
                            return SizedBox.expand(
                              child: VideoItem(
                                video: video,
                                isLoading: isLoading,
                                isActive:
                                    controller.currentIndex.value == index,
                                isLast: index == controller.dataList.length - 1,
                                isLoadingMore: controller.isLoading.value,
                                index: index,
                              ),
                            );
                          },
                        );
            }),
          );
        },
      ),
    );
  }
}

class VideoItem extends StatefulWidget {
  final FeaturedListModel video;
  final bool isLoading;
  bool isActive;
  final bool isLast;
  final bool isLoadingMore;
  final int index;
  VideoItem({
    super.key,
    required this.video,
    required this.isLoading,
    required this.isActive,
    required this.isLast,
    required this.isLoadingMore,
    required this.index,
  });

  @override
  State<VideoItem> createState() => _WebviewPageState();
}

class _WebviewPageState extends State<VideoItem> {
  var eventTypeNames = {
    BetterPlayerEventType.initialized: 'Initialized',
    BetterPlayerEventType.play: 'Play',
    BetterPlayerEventType.pause: 'Pause',
    BetterPlayerEventType.seekTo: 'Seek To',
    BetterPlayerEventType.openFullscreen: 'Open Fullscreen',
    BetterPlayerEventType.hideFullscreen: 'Hide Fullscreen',
    BetterPlayerEventType.setVolume: 'Set Volume',
    BetterPlayerEventType.progress: 'Progress',
    BetterPlayerEventType.finished: 'Finished',
    BetterPlayerEventType.exception: 'Exception',
    BetterPlayerEventType.controlsVisible: 'Controls Visible',
    BetterPlayerEventType.controlsHiddenStart: 'Controls Hidden Start',
    BetterPlayerEventType.controlsHiddenEnd: 'Controls Hidden End',
    BetterPlayerEventType.setSpeed: 'Set Speed',
    BetterPlayerEventType.changedSubtitles: 'Changed Subtitles',
    BetterPlayerEventType.changedTrack: 'Changed Track',
    BetterPlayerEventType.changedPlayerVisibility: 'Changed Player Visibility',
    BetterPlayerEventType.changedResolution: 'Changed Resolution',
    BetterPlayerEventType.pipStart: 'Picture-in-Picture Start',
    BetterPlayerEventType.pipStop: 'Picture-in-Picture Stop',
    BetterPlayerEventType.setupDataSource: 'Setup Data Source',
    BetterPlayerEventType.bufferingStart: 'Buffering Start',
    BetterPlayerEventType.bufferingUpdate: 'Buffering Update',
    BetterPlayerEventType.bufferingEnd: 'Buffering End',
    BetterPlayerEventType.changedPlaylistItem: 'Changed Playlist Item',
  };
  final logic = Get.find<ShortVideo2Controller>();
  BetterPlayerController? playerController;
  var isCanplay = false.obs;
  var isPortraitVideo;
  var videoId;
  @override
  void initState() {
    videoId = "${widget.video.id}";
    playerController = logic.controllers[videoId];
    isPortraitVideo =
        widget.video.aspectRatio != null && widget.video.aspectRatio! < 1;

    getVideos();
    super.initState();
  }

  getVideos() async {
    playerController ??=
        await logic.initializeVideo2(widget.video, widget.index);
    log("playerControllerevent10='Event type name: ${playerController != null}");
    if (playerController != null && !(playerController?.isDisposed ?? true)) {
      playerController?.addEventsListener((event) {
        log("playerControllerevent='Event type name: ${getEventTypeName(event.betterPlayerEventType)}");
        if (event.betterPlayerEventType == BetterPlayerEventType.play) {
          // 检测视频方向
          isCanplay.value = true;
          if (mounted) setState(() {});
        }
      });
      if (playerController?.isPlaying() ??
          false || (playerController?.isBuffering() ?? false)) {
        isCanplay.value = true;
        if (mounted) setState(() {});
      }
      if (!(playerController?.isPlaying() ?? false)) {
        isCanplay.value = true;
        playerController?.play();
        if (mounted) setState(() {});
      }
      log("playerControllerevent11='Event type name: ${playerController != null}");
    } else {
      log("playerControllerevent120='Event type name: ${playerController != null}");

      // 创建控制器
      playerController = BetterPlayerController(
        BetterPlayerConfiguration(
          autoPlay: false,
          looping: true,
          fit: BoxFit.fitWidth, // 关键设置：保持原始比例
          translations: [BetterPlayerTranslations.chinese()],
          deviceOrientationsAfterFullScreen: [
            DeviceOrientation.portraitDown,
            DeviceOrientation.portraitUp
          ],
          aspectRatio: ScreenUtil().screenWidth / ScreenUtil().screenHeight,
          // autoDetectFullscreenDeviceOrientation: false, // 禁用自动检测
          //  autoDetectFullscreenAspectRatio: false, // 禁用自动检测
          controlsConfiguration: const BetterPlayerControlsConfiguration(
            enableSkips: false,
            enableFullscreen: true,
            enableMute: false,
            showControls: false,
            playerTheme: BetterPlayerTheme.material,
            showControlsOnInitialize: false, // 初始化时显示控制栏
          ),
        ),
      );
      log("playerControllerevent1211='Event type name: ${playerController != null}");
      try {
        // 设置数据源并等待初始化
        await playerController?.setupDataSource(
          BetterPlayerDataSource(
            BetterPlayerDataSourceType.network,
            widget.video.videoUrl!,
            bufferingConfiguration: const BetterPlayerBufferingConfiguration(
              minBufferMs: 5000,
              maxBufferMs: 10000,
              bufferForPlaybackMs: 1000,
              bufferForPlaybackAfterRebufferMs: 2000,
            ),
          ),
        );
        log("playerControllerevent121='Event type name: ${playerController != null}");
        // 等待视频准备就绪
        //  await controller.videoPlayerController!.initialized;
        await playerController?.isVideoInitialized();
        // 获取视频时长
        final duration =
            playerController?.videoPlayerController!.value.duration;
// 获取视频宽高比
        var aspectRatio2 =
            playerController?.videoPlayerController!.value.aspectRatio;
        // 更新视频模型中的时长
        widget.video.duration = duration ?? Duration.zero;
        widget.video.aspectRatio = aspectRatio2;
        // 添加到控制器列表
        logic.controllers[videoId] = playerController!;
        logic.loadingControllers.remove(videoId);
        log("playerControllerevent122='Event type name: ${playerController != null}");
        // 如果是当前视频，自动播放
        if (logic.currentIndex.value == widget.index) {
          isCanplay.value = true;
          playerController?.safePlay();
          if (mounted) setState(() {});
        }
        if (!(playerController?.isPlaying() ?? false)) {
          log("playerControllerevent13='Event type name: ${playerController != null}");
          isCanplay.value = true;
          playerController?.play();
          if (mounted) setState(() {});
        }
        playerController?.addEventsListener((event) {
          log("playerControllerevent124='Event type name: ${getEventTypeName(event.betterPlayerEventType)}");
          if (event.betterPlayerEventType == BetterPlayerEventType.play) {
            // 检测视频方向
            isCanplay.value = true;
            if (mounted) setState(() {});
          }
        });
        if (playerController?.isPlaying() ??
            false || (playerController?.isBuffering() ?? false)) {
          isCanplay.value = true;
          if (mounted) setState(() {});
        }
        if (!(playerController?.isPlaying() ?? false)) {
          isCanplay.value = true;
          playerController?.play();
          if (mounted) setState(() {});
        }
        if (mounted) setState(() {});
      } catch (e, stackTrace) {
        print('初始化视频失败: $e');
        print('堆栈跟踪: $stackTrace');
        log("playerControllerevent15=初始化视频失败: $e-堆栈跟踪: $stackTrace");
        // 处理错误状态
        logic.loadingControllers.remove(videoId);
        if (mounted) setState(() {});
      }
    }
  }

  String getEventTypeName(BetterPlayerEventType type) {
    return eventTypeNames[type] ?? type.toString().split('.').last;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return GestureDetector(
        onTap: () {
          if (!widget.isLoading) {
            logic.togglePlayPause(videoId);
          }
        },
        child: VideoGesture(
          onAddFavorite: () {},
          onLongPress: () {},
          onSingleTap: () {
            widget.isActive = !widget.isActive;
            isCanplay.value = true;
            if (playerController?.videoPlayerController?.value.isPlaying ??
                false) {
              playerController?.videoPlayerController?.pause();
            } else {
              playerController?.videoPlayerController?.play();
            }
            if (mounted) setState(() {});
          },
          child: Stack(
            fit: StackFit.expand,
            children: [
              if (playerController != null &&
                  (isCanplay.value || !widget.isLoading)) // 视频播放器或加载状态
                _buildVideoPlayer(playerController!, isPortraitVideo)
              else
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                          "${!widget.isLoading}-${playerController != null}-${isCanplay.value}",
                          style: const TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          )),
                      SpinKitChasingDots(
                        itemBuilder: (_, int index) {
                          return DecoratedBox(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5.r),
                              color: index.isEven
                                  ? Colours.colorFF3F3F
                                  : Colours.app_main,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),

              // 半透明遮罩层
              if (!widget.isLoading)
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withOpacity(0.3),
                        Colors.transparent,
                        Colors.transparent,
                        Colors.black.withOpacity(0.5),
                      ],
                      stops: const [0, 0.3, 0.7, 1],
                    ),
                  ),
                ),

              // 播放/暂停图标
              if (widget.isActive &&
                  !widget.isLoading &&
                  playerController != null)
                Center(
                  child: AnimatedOpacity(
                    opacity: logic.isVideoPlaying(videoId) ? 0 : 1,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.play_circle_fill_outlined,
                      size: 72,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ),

              // 顶部状态栏
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: SafeArea(
                  bottom: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          icon: Icon(
                            Icons.arrow_back_ios,
                            size: 20.w,
                            color: Colours.white,
                          ),
                          onPressed: () {
                            AppPage.back();
                          },
                        ),
                        Obx(() {
                          return Text(
                              "${logic.currentIndex}-${widget.video.aspectRatio?.toStringAsFixed(2)}",
                              style: const TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ));
                        }),
                        IconButton(
                          icon: const Icon(Icons.share_arrival_time,
                              size: 28, color: Colors.white),
                          onPressed: () {},
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // 全屏按钮
              if (!widget.isLoading &&
                  playerController != null &&
                  ((widget.video.aspectRatio ?? 0) > 1))
                Positioned(
                  bottom: 100,
                  right: 16,
                  child: IconButton(
                    icon: const Icon(Icons.fullscreen,
                        size: 32, color: Colours.white),
                    onPressed: () => _enterFullScreen(
                        context, widget.video, playerController!),
                  ),
                ),

              // 加载进度
              // if (widget.isLoading)
              //   Center(child: SpinKitChasingDots(
              //     itemBuilder: (_, int index) {
              //       return DecoratedBox(
              //         decoration: BoxDecoration(
              //           borderRadius: BorderRadius.circular(5.r),
              //           color: index.isEven
              //               ? Colours.colorFF3F3F
              //               : Colours.app_main,
              //         ),
              //       );
              //     },
              //   )

              //       // CircularProgressIndicator(
              //       //   strokeWidth: 2,
              //       //   valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              //       // ),
              //       ),

              // 底部加载更多指示器
              if (widget.isLast && widget.isLoadingMore)
                Positioned(
                  bottom: 20,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            "加载更多...",
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildVideoPlayer(
      BetterPlayerController controller, bool isPortraitVideo) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return BetterPlayer(controller: controller);
        // 竖屏视频全屏显示
        if (isPortraitVideo) {
          return BetterPlayer(controller: controller);
        }

        // 横屏视频以16:9比例居中显示
        return Center(
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: BetterPlayer(controller: controller),
          ),
        );
      },
    );
  }

  void _enterFullScreen(BuildContext context, FeaturedListModel video,
      BetterPlayerController controller) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FullScreenVideoPlayer(
          controller: controller,
          video: video,
        ),
      ),
    );
  }
}

class FullScreenVideoPlayer extends StatefulWidget {
  final BetterPlayerController controller;
  final FeaturedListModel video;

  const FullScreenVideoPlayer({
    super.key,
    required this.controller,
    required this.video,
  });

  @override
  State<FullScreenVideoPlayer> createState() => _FullScreenVideoPlayerState();
}

class _FullScreenVideoPlayerState extends State<FullScreenVideoPlayer> {
  bool _showControls = true;
  Timer? _hideControlsTimer;

  @override
  void initState() {
    super.initState();
    // 根据视频方向设置屏幕方向
    if (widget.video.aspectRatio != null && widget.video.aspectRatio! < 1) {
      // 竖屏视频
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
    } else {
      // 横屏视频
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }

    // 播放视频
    widget.controller.play();

    // 设置隐藏控制器的计时器
    _startHideControlsTimer();
  }

  @override
  void dispose() {
    // 恢复默认方向
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // 取消计时器
    _hideControlsTimer?.cancel();
    super.dispose();
  }

  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      setState(() {
        _showControls = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final isPortraitVideo =
        widget.video.aspectRatio != null && widget.video.aspectRatio! < 1;

    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: () {
          setState(() {
            _showControls = !_showControls;
          });
          if (_showControls) {
            _startHideControlsTimer();
          }
        },
        child: Stack(
          children: [
            // 全屏播放器
            if (isPortraitVideo)
              BetterPlayer(controller: widget.controller)
            else
              Center(
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child: BetterPlayer(controller: widget.controller),
                ),
              ),

            // 控制面板
            if (_showControls) ...[
              // 返回按钮
              Positioned(
                top: 16,
                left: 16,
                child: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ),

              // 播放/暂停按钮
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                child: Center(
                  child: IconButton(
                    icon: Icon(
                      widget.controller.isPlaying() == true
                          ? Icons.pause
                          : Icons.play_arrow,
                      size: 48,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      if (widget.controller.isPlaying() == true) {
                        widget.controller.pause();
                      } else {
                        widget.controller.play();
                      }
                    },
                  ),
                ),
              ),

              // 进度条
              // Positioned(
              //   bottom: 70,
              //   left: 16,
              //   right: 16,
              //   child: videopr(
              //     widget.controller.videoPlayerController!,
              //     allowScrubbing: true,
              //     colors: VideoProgressColors(
              //       playedColor: Colors.red,
              //       bufferedColor: Colors.grey[400]!,
              //       backgroundColor: Colors.grey[800]!,
              //     ),
              //     padding: EdgeInsets.zero,
              //   ),
              // ),

              // 视频信息
              Positioned(
                bottom: 20,
                left: 16,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "@${widget.video.title}",
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.video.content ?? "",
                      style: const TextStyle(color: Colors.white),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // 退出全屏按钮
              if (isLandscape)
                Positioned(
                  top: 16,
                  right: 16,
                  child: IconButton(
                    icon:
                        const Icon(Icons.fullscreen_exit, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }
}
