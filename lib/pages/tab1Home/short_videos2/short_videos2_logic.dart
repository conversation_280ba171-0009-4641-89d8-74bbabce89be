import 'dart:math';
import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../../network/model/featured_list_model.dart';

// 扩展方法
extension BetterPlayerControllerExt on BetterPlayerController {
  bool get isDisposed {
    try {
      // 尝试访问内部属性
      final player = videoPlayerController;
      return player == null || !player.hasListeners;
    } catch (e) {
      return true;
    }
  }

  void safePlay() {
    if (isDisposed) return;
    try {
      play();
    } catch (e) {
      // 忽略错误
    }
  }

  void safePause() {
    if (isDisposed) return;
    try {
      pause();
    } catch (e) {
      // 忽略错误
    }
  }
}

class ShortVideo2Controller extends GetxController {
  var dataList = <FeaturedListModel>[].obs;
  final RxInt currentIndex = 0.obs;
  final Map<String, BetterPlayerController> controllers = {};
  final RxMap<String, BetterPlayerController?> loadingControllers =
      <String, BetterPlayerController?>{}.obs;
  var dataFag = {
    "isFrist": true,
    "page": 1,
  }.obs;

  // 分页相关状态
  final RxBool isLoading = false.obs;
  final RxBool hasMore = true.obs;
  final RxInt currentPage = 1.obs;
  var isFrist = true.obs;
  // 预加载相关参数
  final int preloadThreshold = 5;
  final int preloadCount = 5;
  final int keepControllersCount = 12;

  BetterPlayerController? get currentController => dataList.isNotEmpty
      ? controllers["${dataList[currentIndex.value].id ?? 0}"]
      : null;

  FeaturedListModel? get currentVideo =>
      dataList.isNotEmpty ? dataList[currentIndex.value] : null;

  bool isVideoPlaying(String videoId) {
    final controller = controllers[videoId];
    return controller != null &&
        !controller.isDisposed &&
        (controller.isPlaying() ?? false);
  }

  @override
  void onInit() {
    super.onInit();
    WakelockPlus.enable();
    loadVideos();
  }

  // 加载视频数据
  Future<void> loadVideos({bool loadMore = false}) async {
    if (isLoading.value || (!loadMore && !hasMore.value)) return;
    isLoading.value = true;
    try {
      getFeaturedList(isLoad: loadMore);
    } catch (e) {
      Get.snackbar('加载失败', '无法加载视频数据: $e');
    } finally {
      isLoading.value = false;
    }
  }

  //查询热点资讯视频
  getFeaturedList({
    bool isLoad = false,
  }) async {
    var page = dataFag["page"] as int;
    if (!isLoad) {
      page = 1;
    }
    Map<String, dynamic> param = {
      'limit': 40,
      'page': page,
      'type': 2, //1-资讯 2-视频
    };

    try {
      var res = await Api().get(ApiUrl.getFeaturedList, queryParameters: param);
      // ccc.log("getFeaturedList=type:  ${res.data["list"]}");
      if (res.isSuccessful()) {
        List list = res.data["list"] ?? [];
        List<FeaturedListModel> modelList =
            list.map((e) => FeaturedListModel.fromJson(e)).toList();

        dataFag["page"] = page + 1;
        if (modelList.length < 10) {
          hasMore.value = false;
        }
        if (isLoad) {
          dataList.addAll(modelList);
          dataList.refresh();
        } else {
          dataList.assignAll(modelList);
          currentIndex.value = 0;
        }
        for (int i = 0; i < modelList.length; i++) {
          // 初始化新加载的视频
          initializeVideo(modelList[i], i);
        }

        if (dataFag["isFrist"] as bool) {
          dataFag["isFrist"] = false;
          dataFag.refresh();
        }

        // 预加载周围视频
        _preloadSurroundingVideos();
      } else {
        WxLoading.showToast(res.message);
      }
    } catch (e) {
      Get.snackbar('加载失败', '无法加载视频数据: $e');
    }
  }

  // 初始化单个视频
  Future<BetterPlayerController?> initializeVideo2(
      FeaturedListModel video, int index) async {
    final String videoId = "${video.id ?? 0}";
    // 如果已经在加载或已加载，跳过
    if (loadingControllers.containsKey(videoId) ||
        (controllers.containsKey(videoId) &&
            !controllers[videoId]!.isDisposed)) {
      return controllers[videoId];
    }

    // 标记为正在加载
    loadingControllers[videoId] = null;

    // 确保视频URL不为空
    if (video.videoUrl == null || video.videoUrl!.isEmpty) {
      print('视频URL为空: $videoId');
      loadingControllers.remove(videoId);
      return null;
    }

    // 创建控制器
    var controller = BetterPlayerController(
      BetterPlayerConfiguration(
        autoPlay: false,
        looping: true,
        fit: BoxFit.fitWidth, // 关键设置：保持原始比例
        translations: [BetterPlayerTranslations.chinese()],
        deviceOrientationsAfterFullScreen: [
          DeviceOrientation.portraitDown,
          DeviceOrientation.portraitUp
        ],
        aspectRatio: ScreenUtil().screenWidth / ScreenUtil().screenHeight,
        // autoDetectFullscreenDeviceOrientation: false, // 禁用自动检测
        //  autoDetectFullscreenAspectRatio: false, // 禁用自动检测
        controlsConfiguration: const BetterPlayerControlsConfiguration(
          enableSkips: false,
          enableFullscreen: true,
          enableMute: false,
          showControls: false,
          playerTheme: BetterPlayerTheme.material,
          showControlsOnInitialize: false, // 初始化时显示控制栏
        ),
      ),
    );

    try {
      // 设置数据源并等待初始化
      await controller.setupDataSource(
        BetterPlayerDataSource(
          BetterPlayerDataSourceType.network,
          video.videoUrl!,
          bufferingConfiguration: const BetterPlayerBufferingConfiguration(
            minBufferMs: 5000,
            maxBufferMs: 10000,
            bufferForPlaybackMs: 1000,
            bufferForPlaybackAfterRebufferMs: 2000,
          ),
        ),
      );

      // 等待视频准备就绪
      //  await controller.videoPlayerController!.initialized;
      await controller.isVideoInitialized();
      // 获取视频时长
      final duration = controller.videoPlayerController!.value.duration;
// 获取视频宽高比
      var aspectRatio2 = controller.videoPlayerController!.value.aspectRatio;
      // 更新视频模型中的时长
      final index = dataList.indexWhere((v) => "${v.id ?? 0}" == videoId);
      if (index != -1) {
        // 更新视频时长
        dataList[index].duration = duration ?? Duration.zero;
        dataList[index].aspectRatio = aspectRatio2;
      }
      // 添加到控制器列表
      controllers[videoId] = controller;
      loadingControllers.remove(videoId);

      // 如果是当前视频，自动播放
      if (currentIndex.value == index) {
        controller.safePlay();
      }
      return controller;
    } catch (e, stackTrace) {
      print('初始化视频失败: $e');
      print('堆栈跟踪: $stackTrace');
      // 处理错误状态
      loadingControllers.remove(videoId);
    }
  }

  // 初始化单个视频
  Future<void> initializeVideo(FeaturedListModel video, int index) async {
    final String videoId = "${video.id ?? 0}";

    // 如果已经在加载或已加载，跳过
    if (loadingControllers.containsKey(videoId) ||
        (controllers.containsKey(videoId) &&
            !controllers[videoId]!.isDisposed)) {
      return;
    }

    // 标记为正在加载
    loadingControllers[videoId] = null;

    // 确保视频URL不为空
    if (video.videoUrl == null || video.videoUrl!.isEmpty) {
      print('视频URL为空: $videoId');
      loadingControllers.remove(videoId);
      return;
    }

    // 创建控制器
    var controller = BetterPlayerController(
      BetterPlayerConfiguration(
        autoPlay: false,
        looping: true,
        fit: BoxFit.fitWidth, // 关键设置：保持原始比例
        translations: [BetterPlayerTranslations.chinese()],
        deviceOrientationsAfterFullScreen: [
          DeviceOrientation.portraitDown,
          DeviceOrientation.portraitUp
        ],
        aspectRatio: ScreenUtil().screenWidth / ScreenUtil().screenHeight,
        // autoDetectFullscreenDeviceOrientation: false, // 禁用自动检测
        //  autoDetectFullscreenAspectRatio: false, // 禁用自动检测
        controlsConfiguration: const BetterPlayerControlsConfiguration(
          enableSkips: false,
          enableFullscreen: true,
          enableMute: false,
          showControls: false,
          playerTheme: BetterPlayerTheme.material,
          showControlsOnInitialize: false, // 初始化时显示控制栏
        ),
      ),
    );

    try {
      // 设置数据源并等待初始化
      await controller.setupDataSource(
        BetterPlayerDataSource(
          BetterPlayerDataSourceType.network,
          video.videoUrl!,
          bufferingConfiguration: const BetterPlayerBufferingConfiguration(
            minBufferMs: 5000,
            maxBufferMs: 10000,
            bufferForPlaybackMs: 1000,
            bufferForPlaybackAfterRebufferMs: 2000,
          ),
        ),
      );

      // 等待视频准备就绪
      //  await controller.videoPlayerController!.initialized;
      await controller.isVideoInitialized();
      // 获取视频时长
      final duration = controller.videoPlayerController!.value.duration;
// 获取视频宽高比
      var aspectRatio2 = controller.videoPlayerController!.value.aspectRatio;
      // 更新视频模型中的时长
      final index = dataList.indexWhere((v) => "${v.id ?? 0}" == videoId);
      if (index != -1) {
        // 更新视频时长
        dataList[index].duration = duration ?? Duration.zero;
        dataList[index].aspectRatio = aspectRatio2;
      }
      // // 监听视频初始化事件
      // controller.addEventsListener((event) {
      //   if (event.betterPlayerEventType == BetterPlayerEventType.initialized) {
      //     // 检测视频方向
      //     final videoWidth =
      //         controller.videoPlayerController!.value.size?.width ?? 1;
      //     final videoHeight =
      //         controller.videoPlayerController!.value.size?.height ?? 0;
      //     // log("betterPlayerController2=$videoWidth $videoHeight");
      //     // isHorizontal.value = videoHeight > videoWidth ? false : true;
      //     dataList[index].aspectRatio =
      //         videoHeight > videoWidth ? 9 / 16 : 16 / 9;
      //     ccc.log("betterPlayerController2=$index--$videoWidth $videoHeight");
      //     refresh();
      //     if (isFrist.value) {
      //       isFrist.value = false;
      //     }
      //   }
      // });

      // 添加到控制器列表
      controllers[videoId] = controller;
      loadingControllers.remove(videoId);

      // 如果是当前视频，自动播放
      if (currentIndex.value == index) {
        controller.safePlay();
      }
    } catch (e, stackTrace) {
      print('初始化视频失败: $e');
      print('堆栈跟踪: $stackTrace');

      // 处理错误状态
      loadingControllers.remove(videoId);
    }
  }

  void changeVideo(int index) {
    if (index < 0 || index >= dataList.length) return;

    if (currentIndex.value != index) {
      // 暂停上一个视频
      final previousVideoId = "${dataList[currentIndex.value].id ?? 0}";
      safePause(previousVideoId);

      // 更新当前索引
      currentIndex.value = index;

      // 尝试播放新视频
      final newVideoId = "${dataList[index].id ?? 0}";
      if (controllers.containsKey(newVideoId)) {
        safePlay(newVideoId);
      } else {
        initializeVideo(dataList[index], index);
      }

      // 预加载周围视频
      _preloadSurroundingVideos();

      // 释放不需要的控制器
      _releaseUnusedControllers();

      // 检查是否需要加载更多
      _checkLoadMoreNeeded();
    }
  }

  // 安全播放
  void safePlay(String videoId) {
    final controller = controllers[videoId];
    if (controller != null && !controller.isDisposed) {
      controller.safePlay();
    }
  }

  // 安全暂停
  void safePause(String videoId) {
    final controller = controllers[videoId];
    if (controller != null && !controller.isDisposed) {
      controller.safePause();
    }
  }

  void togglePlayPause(String videoId) {
    final controller = controllers[videoId];
    if (controller != null && !controller.isDisposed) {
      if (controller.isPlaying() ?? false) {
        controller.safePause();
      } else {
        controller.safePlay();
      }
    }
  }

  // 预加载周围视频
  void _preloadSurroundingVideos() {
    final current = currentIndex.value;
    final total = dataList.length;

    // 计算预加载范围
    final start = max(0, current - preloadCount ~/ 2);
    final end = min(total, current + preloadCount ~/ 2 + 1);

    for (int i = start; i < end; i++) {
      if (i != current) {
        initializeVideo(dataList[i], i);
      }
    }
  }

  // 释放不需要的控制器
  void _releaseUnusedControllers() {
    final current = currentIndex.value;
    final total = dataList.length;

    // 计算需要保留的范围
    final retainStart = max(0, current - keepControllersCount);
    final retainEnd = min(total, current + keepControllersCount + 1);

    // 释放范围外的控制器
    controllers.keys.toList().forEach((videoId) {
      final index = dataList.indexWhere((v) => "${v.id ?? 0}" == videoId);
      if (index != -1 && (index < retainStart || index >= retainEnd)) {
        final controller = controllers[videoId]!;
        if (!controller.isDisposed) {
          controller.dispose();
        }
        controllers.remove(videoId);
      }
    });
  }

  // 检查是否需要加载更多
  void _checkLoadMoreNeeded() {
    final current = currentIndex.value;
    final total = dataList.length;

    // 如果接近末尾且有更多数据，加载更多
    if (total - current <= preloadThreshold &&
        hasMore.value &&
        !isLoading.value) {
      loadVideos(loadMore: true);
    }
  }

  @override
  void onClose() {
    // 释放所有控制器
    controllers.forEach((_, controller) {
      if (!controller.isDisposed) {
        controller.dispose();
      }
    });
    controllers.clear();
    loadingControllers.clear();
    WakelockPlus.disable();
    super.onClose();
  }
}
