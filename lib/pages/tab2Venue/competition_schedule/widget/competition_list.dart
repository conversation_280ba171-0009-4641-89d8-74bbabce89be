import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/competition_list_logic.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/widgets/match_item_widget.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class CompetitionList extends StatelessWidget {
  final int competitionId;
  CompetitionList({super.key, required this.competitionId});
  final logic = Get.put(CompetitionListLogic());
  @override
  Widget build(BuildContext context) {
    // 设置competitionId到logic中
    logic.setCompetitionId(competitionId);
    return Scaffold(
      body: Obx(() {
        return NotificationListener(
            onNotification: (ScrollNotification note) {
              if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                logic.loadMore();
              }
              return true;
            },
            child: RefreshIndicator(
              onRefresh: logic.onRefresh,
              child: Container(
                color: Colours.bg_color,
                child: logic.init.value
                    ? (logic.myBattleModelList.isEmpty
                        ? _emptyView(context)
                        : _listView(context))
                    : buildLoad(),
              ),
            ));
      }),
    );
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: constraints.maxHeight,
              child: myNoDataView(
                context,
                msg: '暂无赛事~',
                imagewidget: WxAssets.images.battleEmptyIcon.image(),
              ),
            ));
      },
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 15.w),
        itemCount: logic.myBattleModelList.length,
        itemBuilder: (context, index) {
          final item = logic.myBattleModelList[index];
          // 获取前一个项目的roundName用于比较
          final previousRoundName =
              index > 0 ? logic.myBattleModelList[index - 1].roundName : null;
          // 列表项
          return MatchItemWidget.fromCompetitionMatchModel(
            model: item,
            showStatus: true,
            showArenaName: true,
            previousRoundName: previousRoundName,
          );
        }).marginSymmetric(horizontal: 15.w);
  }
}
