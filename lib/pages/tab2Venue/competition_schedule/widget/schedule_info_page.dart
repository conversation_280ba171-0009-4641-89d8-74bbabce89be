import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/ranking_player_info_page.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/ranking_team_info_page.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/schedule_info_logic.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:ui_packages/ui_packages.dart';

class ScheduleInfoPage extends StatelessWidget {
  final int competitionId;
  ScheduleInfoPage({super.key, required this.competitionId});
  final logic = Get.put(ScheduleInfoLogic());
  @override
  Widget build(BuildContext context) {
    // 设置competitionId到logic中
    logic.setCompetitionId(competitionId);
    return Obx(() {
      return Column(
        children: [
          Container(
            alignment: Alignment.center,
            width: double.infinity,
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.all(Radius.circular(8.r))),
            padding: EdgeInsets.only(
                top: 20.w, bottom: 20.w, left: 15.w, right: 15.w),
            child: Wrap(
              spacing: (ScreenUtil().screenWidth - 60.w - 190.w) / 2.0,
              runSpacing: 20.w,
              children: [
                _teamData(
                    (logic.competitionDataModel.value.teamNum ?? 0).toString(),
                    S.current.the_participating_teams),
                _teamData(
                    (logic.competitionDataModel.value.playNum ?? 0).toString(),
                    '参赛人次'),
                _teamData(
                    (logic.competitionDataModel.value.matchNum ?? 0).toString(),
                    '比赛场次'),
                _teamData(
                    (logic.competitionDataModel.value.score ?? 0).toString(),
                    '总得分'),
                _teamData(
                    (logic.competitionDataModel.value.assistCount ?? 0)
                        .toString(),
                    '总助攻'),
                _teamData(
                    (logic.competitionDataModel.value.reboundCount ?? 0)
                        .toString(),
                    '总篮板')
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 20.w, bottom: 15.w),
            alignment: Alignment.centerLeft,
            child: const TextWithIcon(title: '排行榜'),
          ),
          changeRankType(context),
          Expanded(
              child: TabBarView(controller: logic.tabController, children: [
            RankingTeamInfoPage(competitionId: competitionId),
            RankingPlayerInfoPage(competitionId: competitionId),
          ]))
        ],
      ).marginSymmetric(horizontal: 15, vertical: 15);
    });
  }

  Widget _teamData(String dataStr, String bottomStr) {
    return SizedBox(
      width: 62.w,
      child: Column(
        children: [
          Text(
            dataStr,
            maxLines: 1,
            style: TextStyle(
                fontFamily: 'DIN',
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.white),
          ),
          SizedBox(
            height: 10.w,
          ),
          Text(
            bottomStr,
            style: TextStyles.display12,
            maxLines: 1,
          )
        ],
      ),
    );
  }

  Widget changeRankType(BuildContext context) {
    return Center(
      child: ClipRRect(
          borderRadius: BorderRadius.circular(20.w),
          child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
              child: Container(
                width: 232.w,
                height: 36.w,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20.w),
                ),
                child: Row(
                  children: [
                    Expanded(
                        child: InkWell(
                      onTap: () => logic.switchTab(0),
                      child: Obx(() => Container(
                            alignment: Alignment.center,
                            decoration: logic.tabbarIndex.value == 1
                                ? null
                                : BoxDecoration(
                                    image: DecorationImage(
                                    image: WxAssets.images.rankingSelectBg
                                        .provider(),
                                    fit: BoxFit.fill,
                                  )),
                            child: Text(
                              '球队',
                              style: TextStyles.bold.copyWith(fontSize: 14.sp),
                            ),
                          )),
                    )),
                    Expanded(
                        child: InkWell(
                      onTap: () => logic.switchTab(1),
                      child: Obx(() => Container(
                            alignment: Alignment.center,
                            decoration: logic.tabbarIndex.value == 1
                                ? BoxDecoration(
                                    image: DecorationImage(
                                    image: WxAssets.images.rankingSelectBg
                                        .provider(),
                                    fit: BoxFit.fill,
                                  ))
                                : null,
                            child: Text(
                              '球员',
                              style: TextStyles.bold.copyWith(fontSize: 14.sp),
                            ),
                          )),
                    )),
                  ],
                ),
              ))),
    );
  }
}
