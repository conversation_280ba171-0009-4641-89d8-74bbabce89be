import 'dart:async';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';

class CompetitionCalendarLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  var dateList = <Map<String, dynamic>>[].obs;
  var scrollController = ItemScrollController();
  var isLoading = false.obs;
  var init = false.obs;
  var currentSelectedIndex = 11.obs;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var contentImgPath = "".obs;
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <GameModel>[].obs;
  var currentDate = DateTime.now().obs;
  StreamSubscription? refreshSubscription;
  @override
  void onInit() {
    super.onInit();
    dateList.value = _generateDateList(currentDate.value);
    onRefresh();
    refreshSubscription = BusUtils.instance.on((event) async {
      if (event.key == EventBusKey.subcribeMatchesRefresh) {
        onRefresh();
      }
    });
    // getdataList(isLoad: false, controller: refreshController);
  }

  @override
  void onReady() {
    super.onReady();
  }

  void selectedDate(DateTime date) async {
    if (date == currentDate.value) {
      return;
    }
    currentDate.value = date;
    dateList.value = _generateDateList(currentDate.value);
    int index = dateList.indexWhere((element) => element['date'] == date);
    scrollController.scrollTo(
      index: index,
      duration: const Duration(milliseconds: 300),
      alignment: 0.5, // 居中对齐
    );
    WxLoading.show();
    onRefresh();
    WxLoading.dismiss();
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  List<Map<String, dynamic>> _generateDateList(DateTime centerDate) {
    final List<Map<String, dynamic>> dates = [];
    final DateFormat dateFormat = DateFormat('MM.dd');
    // 计算开始日期（centerDate往前15天）
    DateTime startDate = centerDate.subtract(const Duration(days: 30 ~/ 2));

    // 计算结束日期（centerDate往后15天）
    DateTime endDate = centerDate.add(const Duration(days: 30 ~/ 2));
    // 生成日期范围
    DateTime currentDate = startDate;
    final weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    while (currentDate.isBefore(endDate) ||
        currentDate.isAtSameMomentAs(endDate)) {
      dates.add({
        'dateFormatStr': dateFormat.format(currentDate),
        'date': currentDate,
        'weekday': weekdays[currentDate.weekday % 7],
        'isCenterDate': _isSameDay(currentDate, centerDate),
      });
      currentDate = currentDate.add(const Duration(days: 1));
    }
    return dates;
  }

  Future<void> onRefresh() async {
    isLoading.value = true;
    final DateFormat dateFormat = DateFormat('yyyy-MM-dd');
    Map<String, dynamic> param = {'date': dateFormat.format(currentDate.value)};
    final position = LocationUtils.instance.position;
    if (position != null) {
      param['latitude'] = '${position.latitude}';
      param['longitude'] = '${position.longitude}';
    }
    var res = await Api().get(ApiUrl.gameList, queryParameters: param);
    cc.log("gameListmessage${res.data}");
    init.value = true;
    isLoading.value = false;
    if (res.isSuccessful()) {
      dataList.value =
          (res.data as List).map((e) => GameModel.fromJson(e)).toList();
    }
  }

  //预约赛事报告
  getMatchesSubscribe(String matchId) async {
    Map<String, dynamic> param = {"matchId": matchId};
    var res = await Api()
        .post(ApiUrl.matchesSubscribe(matchId), queryParameters: param);
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.game_report4);
      onRefresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //取消预约
  matchesCancelSubscribe(String matchId) async {
    Map<String, dynamic> param = {"matchId": matchId};
    var res = await Api()
        .post(ApiUrl.matchesCancelSubscribe(matchId), queryParameters: param);
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.cancel_subcribe);
      onRefresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //获得最新列表
  // getdataList({
  //   isLoad = true,
  //   required RefreshController controller,
  // }) async {
  //   if (isLoad) {
  //     dataFag["page"] = (dataFag["page"] as int) + 1;
  //   } else {
  //     dataFag["page"] = 1;
  //   }

  //   Map<String, dynamic> param = {
  //     'pageIndex': dataFag["page"] ?? 1,
  //     'pageSize': 20,
  //     'teamId': teamId.value,
  //   };
  //   var url = await ApiUrl.getTeamSchedule(teamId.value);
  //   var res = await Api().get(url, queryParameters: param);
  //   if (res.isSuccessful()) {
  //     List list = res.data["result"] ?? [];
  //     List<CompetitionModel> modelList =
  //         list.map((e) => CompetitionModel.fromJson(e)).toList();
  //     cc.log("zzzzzz12removeAt-${res.data}");
  //     if (isLoad) {
  //       dataList.addAll(modelList);
  //       dataList.refresh();
  //       if (modelList.length < 20) {
  //         controller.loadNoData();
  //         //  controller.loadComplete();
  //       } else {
  //         controller.loadComplete();
  //       }
  //     } else {
  //       controller.resetNoData();
  //       dataList.assignAll(modelList);
  //       controller.refreshCompleted();
  //     }
  //   } else {
  //     controller.refreshCompleted();
  //     WxLoading.showToast(res.message);
  //   }
  //   if (dataFag["isFrist"] as bool) {
  //     dataFag["isFrist"] = false;
  //     refresh();
  //   }
  // }
}
