import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab1Home/highlights/models/highlights_model.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/more_highlights/more_highlights_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/more_widget.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

/// 赛程列表页面
class MoreHighlightsPage extends StatelessWidget {
  MoreHighlightsPage({super.key});
  final logic = Get.put(MoreHighlightsLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MyAppBar(
        title: Text('更多集锦'),
      ),
      body: Obx(() {
        return Column(
          children: [
            SizedBox(
              height: 15.w,
            ),
            Expanded(
                child: NotificationListener(
                    onNotification: (ScrollNotification note) {
                      if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                        logic.loadMore();
                      }
                      return true;
                    },
                    child: RefreshIndicator(
                      onRefresh: logic.onRefresh,
                      child: logic.init.value
                          ? (logic.dataList.isEmpty
                              ? _buildEmptyView(context)
                              : _list(context))
                          : buildLoad(),
                    ))),
          ],
        ).marginSymmetric(horizontal: 15.w);
      }),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: myNoDataView(
        context,
        msg: '暂无生涯视频，快去创作吧',
        imagewidget: WxAssets.images.battleEmptyIcon.image(),
      ),
    );
  }

  Widget _list(BuildContext context) {
    return Obx(
      () => // 分组内容
          GridView.builder(
        scrollDirection: Axis.vertical,
        // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
        shrinkWrap: true,
        // physics:
        //     const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 15.w,
          mainAxisSpacing: 15.w,
          childAspectRatio: 165 / 131,
        ),
        padding: EdgeInsets.only(bottom: 20.w),
        itemCount: logic.dataList.length,
        itemBuilder: (context, itemIndex) {
          final itemModel = logic.dataList[itemIndex];
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () async {
              // if (!video.isFinish) {
              //   return;
              // }
              // video.rxNew.value = false;
              // final result = await AppPage.to(Routes.highlightsVideo,
              //     arguments: {'video': video, 'group': group});
              // if (result == true) {
              //   // 如果结果为 true，表示需要刷新列表
              //   group.videos.removeAt(itemIndex);
              //   if (group.videos.isEmpty) {
              //     logic.dataList.removeAt(index);
              //   }
              //   logic.dataList.refresh(); // 刷新数据
              // }
              var group = HighlightsModel(logic.arenaId.toString(), logic.arenaName, '', '', 'week');
              AppPage.to(Routes.highlightsVideo,
                  arguments: {'video': itemModel,'group':group});
            },
            child: Column(
              children: [
                Expanded(
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8.w),
                        topRight: Radius.circular(8.w)),
                    child: Stack(
                      children: [
                        Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            top: 0,
                            child: CachedNetworkImage(
                              imageUrl: itemModel.cover ?? '',
                              fit: BoxFit.cover,
                              height: 93.w,
                            )),
                        Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              height: 24.w,
                              alignment: Alignment.centerLeft,
                              color: Colours.color0F0F16.withOpacity(0.6),
                              child: Text(
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                itemModel.name ?? '',
                                style: TextStyles.display12
                                    .copyWith(color: Colors.white),
                              ),
                            )),
                        Positioned(
                            left: 0,
                            right: 0,
                            top: 32.w,
                            child: WxAssets.images.selfieShotPlay
                                .image(width: 28.w, height: 28.w)),
                        // Visibility(
                        //   visible:
                        //       (video.isFinish && video.name.isNotEmpty) ||
                        //           (video.status == 0),
                        //   child: Positioned(
                        //     bottom: 0,
                        //     left: 0,
                        //     right: 0,
                        //     child: Container(
                        //       padding:EdgeInsets.only(top: 4.w),
                        //       height: 40.w,
                        //       decoration: BoxDecoration(
                        //         image: DecorationImage(
                        //             image: WxAssets.images.icHlItemBottom
                        //                 .provider(),
                        //             fit: BoxFit.fill),
                        //       ),
                        //       child:const SizedBox()

                        //     ),
                        //   ),
                        // ),
                      ],
                    ),
                  ),
                ),
                Container(
                  height: 38.w,
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(8.w),
                          bottomRight: Radius.circular(8.w))),
                  child: Row(
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(9.w),
                          child: CachedNetworkImage(
                            imageUrl: itemModel.userAvatar ?? '',
                            fit: BoxFit.cover,
                            height: 18.w,
                            width: 18.w,
                          )),
                      SizedBox(
                        width: 6.w,
                      ),
                      Expanded(
                          child: Text(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        itemModel.userName ?? '',
                        style: TextStyles.display12,
                      ))
                    ],
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _moreWidget() {
    return Padding(
        padding: const EdgeInsets.only(bottom: 30, top: 5),
        child:
            MoreWidget(logic.dataList.length, logic.hasMore(), logic.pageSize));
  }
}
