import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/competition_list/match_list_logic.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/widgets/match_item_widget.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

/// 比赛日历页面
class MatchListPage extends StatelessWidget {
  MatchListPage({super.key});

  final logic = Get.put(MatchListLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MyAppBar(
        title: Text('近期比赛'),
      ),
      body: Obx(() {
        return NotificationListener(
            onNotification: (ScrollNotification note) {
              if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                logic.loadMore();
              }
              return true;
            },
            child: RefreshIndicator(
              onRefresh: logic.onRefresh,
              child: Container(
                color: Colours.bg_color,
                child: logic.init.value
                    ? (logic.dataList.isEmpty
                        ? _buildEmptyView(context)
                        : _listView(context))
                    : buildLoad(),
              ),
            ));
      }),
    );
  }

  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: myNoDataView(context,
          msg: '暂无赛程', imagewidget: WxAssets.images.battleEmptyIcon.image()),
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 15.w),
        itemCount: logic.dataList.length,
        itemBuilder: (context, index) {
          final item = logic.dataList[index];
          // 列表项
          return MatchItemWidget.fromMatches(
            matchModel: item,
            showAppointmentReport: true,
            showStarIcon: true,
            showStatus: true,
            subcribClick: (result) {
              if (result['isSubCrib'] == true) {
                logic.getMatchesSubscribe(result['matchId']);
              }
              if (result['isSubCrib'] == false) {
                logic.matchesCancelSubscribe(result['matchId']);
              }
            },
          );
        }).marginSymmetric(horizontal: 15.w);
  }
}
