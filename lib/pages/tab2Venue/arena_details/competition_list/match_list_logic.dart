import 'dart:async';
import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/utils/event_bus.dart';

class MatchListLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  var init = false.obs;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var contentImgPath = "".obs;
  int _arenaID = 0;

  /// 是否正在加载数据
  bool _isLoading = false;
  var page = 1;
  var pageSize = 10;
  var totalRows = 0;
  //数据列表
  var dataList = <Matches>[].obs;
  var currentDate = DateTime.now().obs;
  StreamSubscription? refreshSubscription;
  @override
  void onInit() {
    super.onInit();
    _arenaID = Get.arguments;
    onRefresh();
    refreshSubscription = BusUtils.instance.on((event) async {
      if (event.key == EventBusKey.subcribeMatchesRefresh) {
        onRefresh();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await myMatches(false);
  }

  bool hasMore() {
    return dataList.length < totalRows;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    await myMatches(true);
    // init.value = true;
  }

  //我的约战
  Future<void> myMatches(bool isRefresh) async {
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> param = {
      "arenaID": _arenaID,
      'page': page,
      'limit': pageSize,
    };
    var res = await Api().get(ApiUrl.arenaGameList, queryParameters: param);
    _isLoading = false;
    init.value = true;
    WxLoading.dismiss();
    log("result${res.data}");
    if (res.isSuccessful()) {
      page += 1;
      final list = (res.data['arenaMatch'] as List)
          .map((e) => Matches.fromJson(e))
          .toList();
      totalRows = res.data["total"];
      if (isRefresh) {
        dataList.value = list;
      } else {
        dataList.addAll(list);
      }
    } else {
      if (isRefresh) {
        dataList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }

//预约赛事报告
  getMatchesSubscribe(String matchId) async {
    Map<String, dynamic> param = {"matchId": matchId};
    var res = await Api()
        .post(ApiUrl.matchesSubscribe(matchId), queryParameters: param);
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.game_report4);
      onRefresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //取消预约
  matchesCancelSubscribe(String matchId) async {
    Map<String, dynamic> param = {"matchId": matchId};
    var res = await Api()
        .post(ApiUrl.matchesCancelSubscribe(matchId), queryParameters: param);
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.cancel_subcribe);
      onRefresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
