import 'dart:developer';
import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/arena_details_logic.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/widgets/match_item_widget.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/RectIndicator.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:intl/intl.dart';

///球场主页详情 二级页面
class ArenaDetailsPage extends StatelessWidget {
  ArenaDetailsPage({super.key});

  final logic = Get.put(ArenaDetailsLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.arena_detail),
      ),
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? SafeArea(bottom: false, child: buildLoad())
            : KeepAliveWidget(
                child: SizedBox(
                  width: double.infinity,
                  child: SafeArea(
                    bottom: false,
                    child: Stack(
                      children: [
                        Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _videoWidget(),
                              Container(
                                margin: EdgeInsets.only(top: 10.w),
                                child: TabBar(
                                  controller: logic.tabController,
                                  indicator: const UnderlineTabIndicator(
                                    borderSide: BorderSide.none,
                                    insets: EdgeInsets.zero,
                                  ),
                                  labelColor: Colors.transparent,
                                  unselectedLabelColor: Colors.transparent,
                                  labelStyle: TextStyles.titleSemiBold16,
                                  dividerColor: Colors.transparent,
                                  dividerHeight: 0,
                                  unselectedLabelStyle: TextStyles.regular
                                      .copyWith(fontSize: 14.sp),
                                  overlayColor: WidgetStateProperty.all(
                                      Colors.transparent),
                                  isScrollable: true,
                                  tabAlignment: TabAlignment.center,
                                  tabs: List.generate(logic.tabNameList.length,
                                      (index) {
                                    return Tab(
                                      child: Container(
                                        constraints:
                                            BoxConstraints(maxWidth: 70.w),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Obx(() => logic.tabbarIndex.value ==
                                                    index
                                                ? ShaderMask(
                                                    shaderCallback: (bounds) =>
                                                        const LinearGradient(
                                                      colors: [
                                                        Colours.colorFFF9DC,
                                                        Colours.colorE4C8FF,
                                                        Colours.colorE5F3FF,
                                                      ],
                                                      begin: Alignment.topLeft,
                                                      end:
                                                          Alignment.bottomRight,
                                                    ).createShader(bounds),
                                                    child: Text(
                                                      logic.tabNameList[index],
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      textAlign:
                                                          TextAlign.center,
                                                      style: TextStyles
                                                          .titleSemiBold16
                                                          .copyWith(
                                                        color: Colors.white,
                                                      ),
                                                    ),
                                                  )
                                                : Text(
                                                    logic.tabNameList[index],
                                                    maxLines: 1,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    textAlign: TextAlign.center,
                                                    style: TextStyles.regular
                                                        .copyWith(
                                                      fontSize: 14.sp,
                                                      color:
                                                          Colours.color5C5C6E,
                                                    ),
                                                  )),
                                            SizedBox(height: 6.w),
                                            logic.tabbarIndex.value == index
                                                ? WxAssets.images.imgCheckIn2
                                                    .image()
                                                : SizedBox(height: 4.w),
                                          ],
                                        ),
                                      ),
                                    );
                                  }),
                                ),
                              ),
                              Expanded(
                                child: TabBarView(
                                  controller: logic.tabController,
                                  children: [
                                    _infoWidget(context),
                                    _listWidget(context),
                                    _competitionListWidget(context)
                                  ],
                                ),
                              ),
                              // Expanded(
                              //   child: Stack(
                              //     children: [
                              //       SingleChildScrollView(
                              //         child: SizedBox(
                              //           width: double.infinity,
                              //           child: Column(
                              //               mainAxisAlignment:
                              //                   MainAxisAlignment.start,
                              //               crossAxisAlignment:
                              //                   CrossAxisAlignment.start,
                              //               mainAxisSize: MainAxisSize.max,
                              //               children: [
                              //                 Expanded(
                              //                   child: TabBarView(
                              //                     controller: logic.tabController,
                              //                     children: [
                              //                       InfoWidget(context),
                              //                       _listWidget(context),
                              //                       _listWidget(context)
                              //                     ],
                              //                   ),
                              //                 ),
                              //                 // //球馆详情页面
                              //                 // InfoWidget(context),
                              //                 // //球馆比赛列表
                              //                 // _listWidget(context),
                              //                 // const SizedBox(
                              //                 //   height: 20,
                              //                 // ),
                              //               ]),
                              //         ),
                              //       ),

                              //     ],
                              //   ),
                              // )
                            ]),
                        Positioned(
                          top: (ScreenUtil().screenHeight - 260.w),
                          left: 2.w,
                          child: Visibility(
                            visible: !logic.isDaka.value,
                            child: GestureDetector(
                              onPanUpdate: (detail) {
                                logic.offset.value = logic.calOffset(
                                    MediaQuery.of(context).size,
                                    logic.offset.value,
                                    detail.delta);
                              },
                              onPanEnd: (detail) {},
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                if (UserManager.instance.isLogin) {
                                  if (logic.isDaka.value) {
                                    //已经打卡
                                    WxLoading.showToast(
                                        S.current.already_clocked);
                                  } else {
                                    //打卡弹窗
                                    logic.getDaka();
                                    //logic.getPointsIsClockIn();
                                  }
                                } else {
                                  // WxLoading.showToast("请先登录");
                                  AppPage.to(Routes.login)
                                      .then((onValue) async {
                                    await Future.delayed(
                                        const Duration(milliseconds: 500));
                                    log("getPointsIsClockIn=$onValue-${UserManager.instance.isLogin}");
                                    if (UserManager.instance.isLogin) {
                                      await logic.getPointsIsClockIn();
                                      if (logic.isDaka.value) {
                                        //已经打卡
                                        WxLoading.showToast(
                                            S.current.already_clocked);
                                      } else {
                                        //打卡弹窗
                                        logic.getDaka();
                                        //logic.getPointsIsClockIn();
                                      }
                                    } else {
                                      log("getPointsIsClockIn2=$onValue-${UserManager.instance.isLogin}");
                                      WxLoading.showToast(
                                          S.current.please_login);
                                    }
                                  });
                                }
                              },
                              child: WxAssets.images.daka1
                                  .image(width: 72.w, height: 58.w),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              );
      }),
      bottomNavigationBar: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? SizedBox()
            : Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                    bottom: 34.w, left: 15.w, right: 15.w, top: 1.w),
                child: Row(
                  children: [
                    if (logic.dataCheckList.isNotEmpty)
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () async {
                          if (Utils.isToLogin()) {
                            //素材库
                            AppPage.to(Routes.materialLibraryPage, arguments: {
                              "arenaID": logic.arenaID.value,
                            }).then((v) {
                              if (UserManager.instance.isLogin) {
                                logic.getDaoGoalList();
                              }
                            });
                          }
                        },
                        child: Stack(
                          children: [
                            Container(
                              width: 44.w,
                              height: 44.w,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16.r),
                                  color: Colours.color282735),
                              child: WxAssets.images.optionDocument
                                  .image(width: 24.w, height: 24.w),
                            ),
                            if (logic.dataCheckList.isNotEmpty)
                              Positioned(
                                right: 0,
                                child: Transform.translate(
                                  offset: const Offset(5, 0), // 移动50像素到右和下
                                  child: Container(
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 1.w,
                                        bottom: 1.w),
                                    decoration: BoxDecoration(
                                      color: Colours.red,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                    ),
                                    child: Text(
                                      "${logic.dataCheckList.length}",
                                      style: TextStyles.titleMedium18
                                          .copyWith(fontSize: 10.sp),
                                    ),
                                  ),
                                ),
                              )
                          ],
                        ),
                      ),
                    if (logic.dataCheckList.isNotEmpty)
                      SizedBox(
                        width: 10.w,
                      ),
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          logic.getDataInfo(true);
                          //isPremium	integer 是否为高级球馆
                          //AppPage.to(Routes.optionGoalPage);
                          if (logic.arenaDetailsModel.value.isPremium == 1) {
                            //选择场地
                            AppPage.to(Routes.optionSitePage, arguments: {
                              "arenaID": logic.arenaID.value,
                              "type": 1
                            }).then((v) {
                              if (UserManager.instance.isLogin) {
                                logic.getDaoGoalList();
                              }
                            });
                          } else {
                            //选择场地
                            AppPage.to(Routes.optionSitePage, arguments: {
                              "arenaID": logic.arenaID.value,
                              "type": 0
                            }).then((v) {
                              if (UserManager.instance.isLogin) {
                                logic.getDaoGoalList();
                              }
                            });
                          }
                        },
                        child: Container(
                          height: 50.w,
                          width: double.infinity,
                          alignment: Alignment.center,
                          padding: EdgeInsets.only(
                              left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                          decoration: BoxDecoration(
                            color: Colours.color282735,
                            borderRadius:
                                BorderRadius.all(Radius.circular(28.r)),
                            gradient: const LinearGradient(
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ],
                              begin: Alignment.bottomLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: logic.arenaDetailsModel.value.isPremium == 1
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    WxAssets.images.ai.image(
                                        width: 35.w,
                                        height: 35.w,
                                        color: Colours.white),
                                    SizedBox(
                                      width: 2.w,
                                    ),
                                    Text(
                                      S.current.get_my_highlights,
                                      style: TextStyles.titleMedium18
                                          .copyWith(fontSize: 16.sp),
                                    ),
                                  ],
                                )
                              : Text(
                                  S.current.get_my_video,
                                  style: TextStyles.titleMedium18
                                      .copyWith(fontSize: 16.sp),
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
      }),
    );
  }

  Widget _videoWidget() {
    return Obx(() {
      return SizedBox(
        width: double.infinity,
        height: ScreenUtil().screenWidth / 375 * 211,
        child: Stack(
          children: [
            Swiper(
              itemBuilder: (context, position) {
                return logic.arenaDetailsModel.value.path?[position]?.type == 0
                    ? MyImage(
                        logic.arenaDetailsModel.value.path?[position]?.path ??
                            '',
                        fit: BoxFit.fill,
                        width: double.infinity,
                        height: ScreenUtil().screenWidth / 375 * 211,
                        errorImage: "error_image_width.png")
                    : AspectRatio(
                        aspectRatio: 375 / 211, // 宽高比
                        child: VideoView(
                          controller: logic.videoController,
                        ),
                      );
              },
              itemCount: logic.arenaDetailsModel.value.path?.length ?? 0,
              autoplay: false,
              loop: false,
              controller: logic.swiperController,
              // pagination: SwiperPagination( alignment: Alignment.bottomRight),
              pagination: SwiperPagination(
                alignment: Alignment.bottomRight,
                builder: SwiperCustomPagination(
                    builder: (BuildContext context, SwiperPluginConfig config) {
                  return RectIndicator(
                      alignment: Alignment.bottomRight,
                      position: config.activeIndex,
                      count: logic.arenaDetailsModel.value.path?.length ?? 0,
                      color: Colors.white.withOpacity(1),
                      activeColor: const Color(0xFFD8D8D8),
                      //未选中 指示器颜色，选中的颜色key为Color
                      width: 7.0,
                      //指示器宽度
                      activeWidth: 6.0,
                      //选中的指示器宽度
                      radius: 2,
                      //指示器圆角角度
                      height: 4.0);
                }),
              ),
              onTap: (position) async {
                // if (await canLaunch(ads[position].url.toString())) {
                //   launch(ads[position].url.toString());
                // }
              },
              onIndexChanged: (value) {
                logic.changeSwiper(value);
              },
            ),
            Positioned(
                right: 10.w,
                top: 12.w,
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    AppPage.to(Routes.moreHighlightsPage, arguments: {
                      "arenaId": logic.arenaID.value,
                      "arenaName": logic.arenaDetailsModel.value.arenaName ?? ""
                    });
                  },
                  child: ClipRRect(
                      borderRadius: BorderRadius.circular(15.w),
                      child: BackdropFilter(
                          filter:
                              ImageFilter.blur(sigmaX: 10.0.w, sigmaY: 10.0.w),
                          child: Container(
                            alignment: Alignment.center,
                            width: 78.w,
                            height: 30.w,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                  image: WxAssets.images.moreHiglightsBg
                                      .provider()),
                              color: Colors.white.withOpacity(0.1),
                              // gradient: const LinearGradient(
                              //   colors: [
                              //     Color(0x288200FB),
                              //     Color(0x0color: Colors.white.withOpacity(0.1),3D8AFFF),
                              //   ],
                              //   begin: Alignment.bottomLeft,
                              //   end: Alignment.bottomRight,
                              // ),
                              borderRadius: BorderRadius.circular(15.w),
                            ),
                            child: Text(
                              '更多集锦',
                              style: TextStyles.display12
                                  .copyWith(color: Colors.white),
                            ),
                          ))),
                ))
          ],
        ),
      );
    });
  }

  SingleChildScrollView _infoWidget(BuildContext context) {
    final tagResultList =
        _getTagsList(logic.arenaDetailsModel.value.tags ?? '');
    return SingleChildScrollView(
        child: Container(
      width: double.infinity,
      padding: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  logic.arenaDetailsModel.value.arenaName ?? "",
                  style: TextStyles.titleSemiBold16,
                ),
              ),
              GestureDetector(
                onTap: () {
                  MyShareH5.getShareH5(ShareArenaDetails(
                      arenaId: logic.arenaID.value.toString()));
                },
                child: Row(
                  children: [
                    WxAssets.images.icShare.image(width: 14.w, height: 14.w),
                    SizedBox(
                      width: 5.w,
                    ),
                    Text(
                      '分享',
                      style: TextStyles.semiBold14.copyWith(fontSize: 12.sp),
                    )
                  ],
                ),
              )
            ],
          ),
          SizedBox(
            height: 12.w,
          ),
          Row(
            children: [
              Text(
                '当前场馆人流量：',
                style: TextStyles.display12,
              ),
              Container(
                width: 10.w,
                height: 10.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _getFlowColor(
                      logic.arenaDetailsModel.value.pedestrianFlowStatus ??
                          0)['color'],
                ),
              ),
              SizedBox(
                width: 5.w,
              ),
              Text(
                  _getFlowColor(
                      logic.arenaDetailsModel.value.pedestrianFlowStatus ??
                          0)['title'],
                  style: TextStyles.display12.copyWith(
                      color: _getFlowColor(
                          logic.arenaDetailsModel.value.pedestrianFlowStatus ??
                              0)['color'])),
            ],
          ),
          SizedBox(
            height: 15.w,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                height: 20.w,
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10.r),
                      bottomLeft: Radius.circular(10.r)),
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Text(
                  S.current.capture_time,
                  style: TextStyles.display12.copyWith(color: Colours.white),
                ),
              ),
              Container(
                height: 20.w,
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                  color: Colours.color922BFF.withOpacity(0.2),
                  borderRadius: BorderRadius.only(
                      topRight: Radius.circular(10.r),
                      bottomRight: Radius.circular(10.r)),
                ),
                child: Text(
                  "${(logic.arenaDetailsModel.value.beginTime ?? "").length >= 5 ? (logic.arenaDetailsModel.value.beginTime ?? "").substring(0, 5) : (logic.arenaDetailsModel.value.beginTime ?? "")}-${(logic.arenaDetailsModel.value.endTime ?? "").length >= 5 ? (logic.arenaDetailsModel.value.endTime ?? "").substring(0, 5) : (logic.arenaDetailsModel.value.endTime ?? "")}",
                  style: TextStyles.din.copyWith(fontSize: 12.sp),
                ),
              ),
              SizedBox(
                width: 15.w,
              ),
              Expanded(
                child: Text(
                  (logic.arenaDetailsModel.value.floorCondition ?? [])
                      .whereType<String>()
                      .join(' | '),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyles.display12.copyWith(
                    color: Colours.color5C5C6E,
                  ),
                ),
              ),
            ],
          ),
          Container(
            width: ScreenUtil().screenWidth - 30.w,
            height: 70.w,
            padding: EdgeInsets.all(15.w),
            margin: EdgeInsets.only(top: 15.w, bottom: 20.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(8.r)),
                color: Colours.color1F1A2F,
                image: DecorationImage(
                    image: WxAssets.images.arenaDetailAddressBg.provider(),
                    fit: BoxFit.fill)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                        width: 220.w,
                        child: Text(
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          logic.arenaDetailsModel.value.address ?? "",
                          style: TextStyles.semiBold14,
                        )),
                    const Spacer(),
                    Text(
                      S.current.distance_you(
                          logic.arenaDetailsModel.value.distance ?? ""),
                      style: TextStyles.display12,
                    ),
                  ],
                ),
                const Spacer(),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    //  log("openMapsSheet2");
                    if (logic.arenaDetailsModel.value.latitude! > 0 ||
                        logic.arenaDetailsModel.value.longitude! > 0) {
                      logic.openMapsSheet(
                          context,
                          //  "湖南省长沙市望城区春晖花园小区", //
                          logic.arenaDetailsModel.value.address ?? "",
                          logic.arenaDetailsModel.value.latitude ?? 0.0,
                          logic.arenaDetailsModel.value.longitude ?? 0.0);
                    } else {
                      WxLoading.showToast(S.current.no_location_club);
                    }
                  },
                  child: Column(
                    children: [
                      WxAssets.images.locationIcon.image(),
                      SizedBox(
                        height: 5.w,
                      ),
                      Text(
                        '导航',
                        style:
                            TextStyles.display12.copyWith(color: Colours.white),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 20.w,
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    log("openMapsSheet2-${logic.arenaDetailsModel.value.tel ?? ""}");
                    if (logic.arenaDetailsModel.value.tel!.isNotEmpty) {
                      log("openMapsSheet3-${logic.arenaDetailsModel.value.tel ?? ""}");
                      Get.dialog(
                        CustomAlertDialog(
                          title: S.current.call_phone(
                              logic.arenaDetailsModel.value.tel ?? ""),
                          onPressed: () {
                            AppPage.back();
                            Utils.phoneTelURL(
                                logic.arenaDetailsModel.value.tel ?? "");
                          },
                        ),
                      );
                    } else {
                      WxLoading.showToast(S.current.no_number_club);
                    }
                  },
                  child: Column(
                    children: [
                      WxAssets.images.phoneIcon.image(),
                      SizedBox(
                        height: 5.w,
                      ),
                      Text(
                        '电话',
                        style:
                            TextStyles.display12.copyWith(color: Colours.white),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(bottom: 15.w),
            child: const TextWithIcon(title: '智能服务'),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              ...tagResultList.map((e) {
                return _verticalIconWithText(
                    e['icon'] as Image, e['title'] as String,
                    isDisplay: e['isDisplay'] as bool);
              })
            ],
          ),
        ],
      ),
    ));
  }

  Map<String, dynamic> _getFlowColor(int status) {
    switch (status) {
      case 0:
        return {'title': '未知', 'color': Colours.color5C5C6E};
      case 1:
        return {'title': '空闲', 'color': Colours.color15B200};
      case 2:
        return {'title': '稀疏', 'color': Colours.color15B200};
      case 3:
        return {'title': '适中', 'color': Colours.color82B200};
      case 4:
        return {'title': '拥挤', 'color': Colours.colorFB8E00};
      case 5:
        return {'title': '爆满', 'color': Colours.colorFF3F3F};
      default:
        return {'title': '未知', 'color': Colours.color5C5C6E};
    }
  }

  Widget _verticalIconWithText(Image icon, String text,
      {bool isDisplay = true}) {
    return Column(
      children: [
        icon,
        SizedBox(
          height: 13.w,
        ),
        Text(
          text,
          style: isDisplay
              ? TextStyles.semiBold14.copyWith(fontSize: 12.sp)
              : TextStyles.display12,
        ),
      ],
    );
  }

  List<Map<String, Object>> _getTagsList(String tags) {
    var result = [
      {
        'icon': WxAssets.images.highlightingMoments.image(),
        'title': '高光时刻',
        'isDisplay': false
      },
      {
        'icon': WxAssets.images.dataReportIcon.image(),
        'title': '数据报告',
        'isDisplay': false
      },
      {
        'icon': WxAssets.images.matchLiveIcon.image(),
        'title': '赛事直播',
        'isDisplay': false
      }
    ];
    final tagList = tags.split(',');
    if (tagList.contains('1')) {
      result[0]['icon'] =
          WxAssets.images.highlightingMomentsHighlighting.image();
      result[0]['isDisplay'] = true;
    }
    if (tagList.contains('2')) {
      result[1]['icon'] = WxAssets.images.dataReportHighlighting.image();
      result[1]['isDisplay'] = true;
    }
    if (tagList.contains('3')) {
      result[2]['icon'] = WxAssets.images.matchLiveHighlighting.image();
      result[2]['isDisplay'] = true;
    }
    return result;
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? SizedBox(
                    height: 300.w,
                    child: myNoDataView(
                      context,
                      msg: '暂无赛事信息～',
                      imagewidget: WxAssets.images.purposeEmpty.image(),
                    ))
                : Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const TextWithIcon(title: '近期比赛'),
                          InkWell(
                              onTap: () => AppPage.to(Routes.matchListPage,
                                  arguments: logic.arenaID.value),
                              child: Container(
                                width: 78.w,
                                height: 30.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  image: DecorationImage(
                                      image: WxAssets.images.moreHiglightsBg
                                          .provider()),
                                ),
                                child: Text(
                                  '查看全部',
                                  style: TextStyles.display12
                                      .copyWith(color: Colors.white),
                                ),
                              ))
                        ],
                      ).marginOnly(left: 15.w, right: 15.w, bottom: 20.w),
                      ListView.builder(
                          scrollDirection: Axis.vertical,
                          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: logic.dataList.length,
                          itemBuilder: (context, position) {
                            return _listItemWidget(logic.dataList[position]);
                          }),
                    ],
                  ),
      );
      // );
    });
  }

  /// 赛程列表数据
  _competitionListWidget(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.competitionList.isEmpty
                ? SizedBox(
                    height: 300.w,
                    child: myNoDataView(
                      context,
                      msg: '暂无赛程信息～',
                      imagewidget: WxAssets.images.purposeEmpty.image(),
                    ))
                : Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const TextWithIcon(title: '赛程列表'),
                          InkWell(
                              onTap: () => AppPage.to(
                                  Routes.arenaCompetitionListPage,
                                  arguments: logic.arenaID.value),
                              child: Container(
                                width: 78.w,
                                height: 30.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  image: DecorationImage(
                                      image: WxAssets.images.moreHiglightsBg
                                          .provider()),
                                ),
                                child: Text(
                                  '查看全部',
                                  style: TextStyles.display12
                                      .copyWith(color: Colors.white),
                                ),
                              ))
                        ],
                      ).marginOnly(left: 15.w, right: 15.w, bottom: 20.w),
                      _listView(context),
                    ],
                  ),
      );
      // );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(Matches item) {
    return MatchItemWidget.fromMatches(
      matchModel: item,
      showStarIcon: true,
      showStatus: true,
      showAppointmentReport: true,
      subcribClick: (result) {
        if (result['isSubCrib'] == true) {
          logic.getMatchesSubscribe(result['matchId']);
        }
        if (result['isSubCrib'] == false) {
          logic.matchesCancelSubscribe(result['matchId']);
        }
      },
    ).marginOnly(left: 15.w, right: 15.w);
  }

  /// 构建列表项
  // Widget _competitionItemWidget(CompetitionModel item) {
  //   return ;
  // }
  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: logic.competitionList.length,
        itemBuilder: (context, index) {
          var model = logic.competitionList[index];
          DateTime parsedStartDate = DateTime.parse(model.startTime ?? '');
          DateTime parsedEndDate = DateTime.parse(model.endTime ?? '');
          String formattedStartDate =
              DateFormat("yyyy.MM.dd").format(parsedStartDate);
          String formattedEndDate =
              DateFormat("yyyy.MM.dd").format(parsedEndDate);
          return Column(
            children: [
              InkWell(
                onTap: () => AppPage.to(Routes.competitionDetailPage,
                    arguments: {'competitionId': model.competitionId}),
                child: Container(
                  height: 130.w,
                  margin: EdgeInsets.only(bottom: 15.w),
                  padding: EdgeInsets.all(15.r),
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.all(Radius.circular(8.r))),
                  child: Row(
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(8.w),
                          child: CachedNetworkImage(
                            imageUrl: model.arenaImageUrl ?? "",
                            width: 100.w,
                            height: 100.w,
                            fit: BoxFit.fill,
                          )),
                      SizedBox(
                        width: 15.w,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              model.competitionName ?? '',
                              style:
                                  TextStyles.semiBold14.copyWith(height: 1.5),
                              maxLines: 2,
                            ),
                            Text(
                              '报名截止：${model.registrationDeadline?.split(' ').first}',
                              style: TextStyles.display12,
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  height: 20,
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 9.w),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    gradient: model.status == 1
                                        ? const LinearGradient(colors: [
                                            Color(0xFF7732ED),
                                            Color(0xFFA555EF),
                                          ])
                                        : null,
                                    color: _getStatusColor(model.status ?? 0),
                                    borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(8.r),
                                        topRight: Radius.circular(8.r)),
                                  ),
                                  child: Text(
                                    _getStatusStr(model.status ?? 0),
                                    style: TextStyles.display10.copyWith(
                                        color: model.status == 4
                                            ? Colours.color5C5C6E
                                            : Colours.white,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                                const SizedBox(
                                  width: 6,
                                ),
                                Text(
                                  '$formattedStartDate-$formattedEndDate',
                                  style: TextStyles.display12
                                      .copyWith(color: Colours.colorA8A8BC),
                                )
                              ],
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              )
            ],
          );
        });
  }

  _getStatusStr(int status) {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '报名中';
      case 2:
        return '待开赛';
      case 3:
        return '进行中';
      case 4:
        return '已结束';
      default:
        return '未开始';
    }
  }

  _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colours.color6435E9;
      case 1:
        return Colours.color6435E9;
      case 2:
        return Colours.color6435E9;
      case 3:
        return Colours.colorFF661A;
      case 4:
        return Colours.color262626;
      default:
        return Colours.color262626;
    }
  }
}
