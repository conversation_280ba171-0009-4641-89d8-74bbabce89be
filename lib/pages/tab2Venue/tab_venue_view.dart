import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import '../../generated/l10n.dart';
import '../../utils/location_utils.dart';
import '../../widgets/view.dart';
import 'tab_venue_logic.dart';
import 'place_list_item.dart';

class TabVenuePage extends StatefulWidget {
  const TabVenuePage({super.key});

  @override
  State<TabVenuePage> createState() => _HomePageState();
}

class _HomePageState extends State<TabVenuePage>
    with AutomaticKeepAliveClientMixin {
  final logic = Get.put(TabVenueLogic());
  final state = Get.find<TabVenueLogic>().state;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: SafeArea(
        top: false,
        child: Obx(() => Stack(
              children: [
                WxAssets.images.arenaTopBg.image(
                    width: ScreenUtil().screenWidth, fit: BoxFit.fitWidth),
                Column(
                  children: [
                    SizedBox(
                      height: ScreenUtil().statusBarHeight + 10.w,
                    ),
                    // 搜索框
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 15.w),
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      height: 44.w,
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                        color: Colours.color191921,
                        borderRadius: BorderRadius.circular(22.r),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          WxAssets.images.icSearch.image(),
                          SizedBox(
                            width: 10.w,
                          ),
                          Expanded(
                              child: TextField(
                            onTap: () => AppPage.to(Routes.placeSearch),
                            controller: logic.searchController,
                            keyboardType: TextInputType.text,
                            readOnly: true,
                            // onChanged: logic.onTextChanged,
                            decoration: InputDecoration(
                              // isDense: true,//isDense 为 true 会让 TextField 的高度变紧凑，同时调整光标和文本的位置。
                              contentPadding:
                                  EdgeInsets.only(top: 0.w, bottom: 3.w),
                              border: InputBorder.none,
                              hintText: "请输入要查找的场地名称",
                              hintStyle: TextStyles.display14
                                  .copyWith(color: Colours.color5C5C6E),
                            ),
                          )),
                          GestureDetector(
                              onTap: () {
                                logic.searchController.text = '';
                                logic.searchText.value = '';
                              },
                              child: Obx(() => Visibility(
                                  visible: logic.searchText.value.isNotEmpty,
                                  child:
                                      WxAssets.images.icSearchDelete.image()))),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 20.w,
                    ),
                    state.init.value
                        ? Expanded(
                            child: NotificationListener(
                                onNotification: (ScrollNotification note) {
                                  if (note.metrics.pixels ==
                                      note.metrics.maxScrollExtent) {
                                    logic.loadMore();
                                  }
                                  return true;
                                },
                                child: RefreshIndicator(
                                  onRefresh: logic.onRefresh,
                                  child: Builder(builder: (context) {
                                    return Obx(
                                      () => CustomScrollView(
                                        slivers: [
                                          if (state.myArenaList.isNotEmpty)
                                            SliverToBoxAdapter(
                                              child: Container(
                                                alignment: Alignment.centerLeft,
                                                child: const TextWithIcon(
                                                    title: '我的主场'),
                                              ).marginOnly(
                                                  left: 15.w, bottom: 15.w),
                                            ),
                                          LocationUtils
                                                  .instance.havePermission.value
                                              ? _myArenalist()
                                              : _location(),
                                          if (state.allArenaList.isNotEmpty)
                                            SliverToBoxAdapter(
                                              child: Container(
                                                alignment: Alignment.centerLeft,
                                                child: const TextWithIcon(
                                                    title: '所有场馆'),
                                              ).marginOnly(
                                                  left: 15.w, bottom: 15.w),
                                            ),
                                          _allArenalist()
                                        ],
                                      ),
                                    );
                                  }),
                                )),
                          )
                        : buildLoad(),
                  ],
                ),
              ],
            )),
      ),
    );
  }

  SliverToBoxAdapter _location() {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          SizedBox(
            height: 71.w,
          ),
          WxAssets.images.icHomeLocationHint
              .image(width: 104.w, fit: BoxFit.fill),
          SizedBox(
            height: 30.w,
          ),
          Text(
            S.current.location_tips,
            style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
          ),
          SizedBox(
            height: 30.w,
          ),
          WxButton(
            width: 125.w,
            height: 40.w,
            borderRadius: BorderRadius.circular(20.w),
            backgroundColor: Colours.color22222D,
            text: S.current.open_now,
            textStyle: TextStyles.regular,
            onPressed: logic.openSettings,
          ),
        ],
      ),
    );
  }

  SliverList _myArenalist() {
    return SliverList(
        delegate: SliverChildBuilderDelegate(
      (BuildContext context, int index) {
        return PlaceListItem(
          model: state.myArenaList[index],
        );
      },
      childCount: state.myArenaList.isNotEmpty ? state.myArenaList.length : 0,
    ));
  }

  SliverList _allArenalist() {
    return SliverList(
        delegate: SliverChildBuilderDelegate(
      (BuildContext context, int index) {
        return PlaceListItem(
          model: state.allArenaList[index],
        );
      },
      childCount: state.allArenaList.isNotEmpty ? state.allArenaList.length : 0,
    ));
  }

  Widget allPlace() {
    return Padding(
        padding: const EdgeInsets.only(bottom: 30, top: 5),
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            height: 30,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: Colours.color2F2F3B, width: 1),
            ),
            child: GestureDetector(
              onTap: () => logic.toAll(),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  WxAssets.images.icMoreAll.image(),
                  const SizedBox(
                    width: 4,
                  ),
                  Text(
                    S.current.all_arenas,
                    style: TextStyles.display12.copyWith(color: Colors.white),
                  )
                ],
              ),
            ),
          ),
        ));
  }

  @override
  bool get wantKeepAlive => true;
}
