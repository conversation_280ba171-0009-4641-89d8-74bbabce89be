import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter_common/wx_storage.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_cupertino_datetime_picker/flutter_cupertino_datetime_picker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/entities/option_goal_model.dart';
import 'package:shoot_z/network/model/ai_option_model.dart';
import 'package:shoot_z/network/model/invite_mini_path_model.dart';
import 'package:shoot_z/network/model/user_is_new_model.dart';
import 'package:shoot_z/pages/tab2Venue/option_site/option_goal/option_goal_state.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/dialo_view.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:intl/intl.dart';

class OptionGoalLogic extends GetxController
    with WidgetsBindingObserver, GetSingleTickerProviderStateMixin {
  final OptionGoalLogicState state = OptionGoalLogicState();
  String _date = DateFormat('HH:mm').format(DateTime.now());
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  List<String> maintenanceArenaList = <String>[];
  final VideoController videoController =
      VideoController(pushDisposeOnAndroid: true);
  var courtName = "".obs;
  var arenaID = 0.obs;
  var courtId = "".obs;

  var placemodel3 = Rx<PlaceModel?>(null); //球馆列表实例化数据

  TabController? tabController;
  var tabbarIndex = 0.obs;

  void switchTab(index) {
    tabbarIndex.value = index;
  }

  var isFrist = true.obs;
  // var videostate = 0.obs; //0初始 1播放  2暂停  3完成
//侧面 cameraIndex = 0  全景  cameraIndex = 126
  var todayDate = "";
  var yesterdayDate = "";
  var isVip = false.obs;
  var isFullScreen = false.obs;
  var aIoptionCheckID = "".obs; //ai选球选中人id
  var aIoptionCheckDx = 0.0.obs; //动画
  Timer? _timer;
  StreamSubscription<EventAction>? eventbus;
  var type = 0.obs; //0普通球馆  1高级球馆 有ai身形
  @override
  Future<void> onInit() async {
    super.onInit();
    if (Get.arguments != null &&
        Get.arguments.containsKey('maintenanceArenaList')) {
      maintenanceArenaList = Get.arguments['maintenanceArenaList'];
    }
    WidgetsBinding.instance.addObserver(this);
    tabController = TabController(length: 2, vsync: this);
    courtName.value = Get.arguments["courtName"];
    arenaID.value = Get.arguments["arenaID"] as int; //39 ??
    courtId.value = Get.arguments["courtId"];
    type.value = Get.arguments["type"] as int;
    // 定义日期时间格式
    var latestTime = Get.arguments["LatestTime"]; //最后的生成时间
    var lateStartTime = Get.arguments["startTime"]; //开始时间
    var lateEndTime = Get.arguments["endTime"]; //结束时间时间
    if (lateEndTime != null && lateStartTime != null) {
      //传进来开始时间和结束时间
      state.videoDate.value = lateStartTime.toString().substring(0, 10);
      getDataTime2(lateStartTime, lateEndTime);
    } else {
      state.videoDate.value = Get.arguments["LatestDate"];
      getDataTime(latestTime);
    }

    log("videoDate11=${state.videoDate1.value} - ${state.videoDate.value} - ${latestTime}-${lateStartTime}-${lateEndTime}");

    // 当 isVip 变化时，执行回调
    ever(isVip, (value) {
      print("isVip 变了，新值: $value");
      videoController.showWatermark(!value);
    });
    if (type.value == 1) {
      if (UserManager.instance.isLogin) {
        //   //ai选球
        var isOk = await getAiOptionGoal(Get.context!);
        if (isOk) {
          getAiOptionGoalDialog(Get.context!);
        }
      }
    }
  }

  getDataTime(String latestTime) async {
    DateFormat dateFormat3 = DateFormat("yyyy-MM-dd");
    DateTime nowDateTime = DateTime.now();
    todayDate = dateFormat3.format(nowDateTime);
    DateTime nextEndTime = nowDateTime.subtract(const Duration(days: 1));
    yesterdayDate = dateFormat3.format(nextEndTime);

    state.videoEndTime.value = "${state.videoDate.value} $latestTime";
    try {
      // 解析日期时间字符串
      DateTime endtime = DateTime.parse(state.videoEndTime.value);
      // 减少一个小时
      DateTime starttime = endtime.subtract(const Duration(hours: 1));

      // 创建一个 DateTime 对象（可以是当前时间或特定时间）
      DateTime dateTime = DateTime(2024, 12, 17, 21, 53, 55);

      // 定义日期时间格式
      DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
      state.videoStartTime.value = dateFormat.format(starttime);
      DateTime nextEndTime = endtime.add(const Duration(hours: 1));
      state.dateFristList2.clear();
      DateTime dateTime1 = DateTime(starttime.year, starttime.month,
          starttime.day, starttime.hour, 00, 00);
      DateTime dateTime2 = DateTime(starttime.year, starttime.month,
          starttime.day, starttime.hour, 30, 00);
      DateTime dateTime3 = DateTime(
          endtime.year, endtime.month, endtime.day, endtime.hour, 00, 00);
      DateTime dateTime4 = DateTime(
          endtime.year, endtime.month, endtime.day, endtime.hour, 30, 00);
      DateTime dateTime5 = DateTime(
          nextEndTime.year,
          nextEndTime.month,
          //推荐时段的4个时间
          nextEndTime.day,
          nextEndTime.hour,
          00,
          00);
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime1),
        "endTime": dateFormat.format(dateTime3)
      });
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime1),
        "endTime": dateFormat.format(dateTime2)
      });
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime3),
        "endTime": dateFormat.format(dateTime5)
      });
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime3),
        "endTime": dateFormat.format(dateTime4)
      });
      state.datePrecisionList.clear();
      //精确查找最近8天

      DateTime nowDateTime = DateTime.now();
      DateFormat dateFormat2 = DateFormat("yyyy.MM.dd");
      final DateFormat formatter = DateFormat('EEEE', 'zh_CN'); // 根据需要更改语言代码
      // DateTime videoDate2 = DateTime.parse(state.videoDate.value);
      state.videoDate1.value = dateFormat2.format(nowDateTime);
      state.videoNextStartTime.value = state.videoEndTime.value;
      DateTime endtime3 = DateTime.parse(state.videoEndTime.value);
      // 增加一个小时
      DateTime nextStartTime = endtime3.add(const Duration(hours: 1));
      // 定义日期时间格式
      state.videoNextEndTime.value = dateFormat.format(nextStartTime);
      state.datePrecisionList.add({
        "week": formatter.format(nowDateTime),
        "date": dateFormat2.format(nowDateTime),
        "datestr": S.current.today
      });
      for (int i = 0; i < 7; i++) {
        var days = i + 1;
        DateTime nextEndTime = nowDateTime.subtract(Duration(days: days));
        state.datePrecisionList.add({
          "week": formatter.format(nextEndTime),
          "date": dateFormat2.format(nextEndTime),
          "datestr": i == 0 ? S.current.yesterday : ""
        });
      }
      print('Parsed DateTime: $dateTime');
    } catch (e) {
      print('Error parsing date string: $e');
    }
  }

  getDataTime2(String startTime, String endTime) async {
    DateFormat dateFormat3 = DateFormat("yyyy-MM-dd");
    DateTime nowDateTime = DateTime.now();
    todayDate = dateFormat3.format(nowDateTime);
    DateTime nextEndTime = nowDateTime.subtract(const Duration(days: 1));
    yesterdayDate = dateFormat3.format(nextEndTime);
    state.videoEndTime.value = endTime;
    state.videoStartTime.value = startTime;
    try {
      // 解析日期时间字符串
      DateTime endtime = DateTime.parse(state.videoEndTime.value);
      // 减少一个小时
      DateTime starttime = DateTime.parse(state.videoStartTime.value);

      // 创建一个 DateTime 对象（可以是当前时间或特定时间）
      // 定义日期时间格式
      DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
      // 定义日期时间格式
      state.dateFristList2.clear();
      DateTime dateTime1 = DateTime(starttime.year, starttime.month,
          starttime.day, starttime.hour, 00, 00);
      DateTime dateTime2 = DateTime(starttime.year, starttime.month,
          starttime.day, starttime.hour, 30, 00);
      DateTime dateTime3 = DateTime(
          endtime.year, endtime.month, endtime.day, endtime.hour, 00, 00);
      DateTime dateTime4 = DateTime(
          endtime.year, endtime.month, endtime.day, endtime.hour, 30, 00);
      DateTime dateTime5 = DateTime(
          nextEndTime.year,
          nextEndTime.month,
          //推荐时段的4个时间
          nextEndTime.day,
          nextEndTime.hour,
          00,
          00);
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime1),
        "endTime": dateFormat.format(dateTime3)
      });
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime1),
        "endTime": dateFormat.format(dateTime2)
      });
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime3),
        "endTime": dateFormat.format(dateTime5)
      });
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime3),
        "endTime": dateFormat.format(dateTime4)
      });
      state.datePrecisionList.clear();
      //精确查找最近8天

      DateTime nowDateTime = DateTime.now();
      DateFormat dateFormat2 = DateFormat("yyyy.MM.dd");
      final DateFormat formatter = DateFormat('EEEE', 'zh_CN'); // 根据需要更改语言代码
      // DateTime videoDate2 = DateTime.parse(state.videoDate.value);
      state.videoDate1.value = dateFormat2.format(nowDateTime);
      state.videoNextStartTime.value = state.videoEndTime.value;
      DateTime endtime3 = DateTime.parse(state.videoEndTime.value);
      // 增加一个小时
      DateTime nextStartTime = endtime3.add(const Duration(hours: 1));
      // 定义日期时间格式
      state.videoNextEndTime.value = dateFormat.format(nextStartTime);
      state.datePrecisionList.add({
        "week": formatter.format(nowDateTime),
        "date": dateFormat2.format(nowDateTime),
        "datestr": S.current.today
      });
      for (int i = 0; i < 7; i++) {
        var days = i + 1;
        DateTime nextEndTime = nowDateTime.subtract(Duration(days: days));
        state.datePrecisionList.add({
          "week": formatter.format(nextEndTime),
          "date": dateFormat2.format(nextEndTime),
          "datestr": i == 0 ? S.current.yesterday : ""
        });
      }
    } catch (e) {
      print('Error parsing date string: $e');
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    eventbus = BusUtils.instance.on((p0) async {
      if (p0.key == EventBusKey.loginSuccessful) {
        if (UserManager.instance.isLogin) {
          isVip.value = await getVideosUsedShots(arenaID.value, type: 1);
        }
      } else if (p0.key == EventBusKey.receiveVip) {
        if (UserManager.instance.isLogin) {
          isVip.value = await getVideosUsedShots(arenaID.value, type: 1);
        }
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 这里的代码将在当前帧结束后执行
      refresh();
    });
    if (UserManager.instance.isLogin) {
      isVip.value = await getVideosUsedShots(arenaID.value, type: 1);
    }
    await getDataList(isLoad: false);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {}

  reloadVideo(String videoUrl) async {
    if (videoUrl.isEmpty) {
      log("betterPlayer-message11=$videoUrl");
      return;
    }
    videoController.setData(videoPath: videoUrl, showWatermark: !isVip.value);
  }

  //获得最新列表
  getDataList({
    isLoad = true,
  }) async {
    var param = {
      // 'arenaId': 30 ?? arenaID.value,
      // 'courtId': 238 ?? int.tryParse(courtId.value),
      // //'videoDate': videoDate.value,
      // 'videoStartTime': "2024-12-18 18:45:19" ?? state.videoStartTime.value,
      // 'videoEndTime': "2024-12-18 20:45:19" ?? state.videoEndTime.value,
      'arenaId': arenaID.value,
      'courtId': int.tryParse(courtId.value),
      //'videoDate': videoDate.value,
      'videoStartTime': state.videoStartTime.value,
      'videoEndTime': state.videoEndTime.value,
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }

    if (aIoptionCheckID.value != "") {
      //去除ai选球标签
      aIoptionCheckID.value = "";
    }
    final res = await Api().get(ApiUrl.getCourtVideos, queryParameters: param);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      List list = res.data;
      List<OptionGoalModel> modelList =
          list.map((e) => OptionGoalModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${modelList.length}");
      if (isLoad) {
        state.dataList.addAll(modelList);
        state.dataList.refresh();
        // if (modelList.length < 20) {
        //   //  controller.loadComplete();
        // }
      } else {
        state.dataList.assignAll(modelList);
        if (state.dataList.isNotEmpty) {
          changeVideoIndex(0);
        } else {
          state.indexVideo.value = 9999;
          videoController.setData(videoPath: '');
        }
      }
      //读取数据库数据
      getDaoGoalList();
      if (dataFag["isFrist"] as bool) {
        dataFag["isFrist"] = false;
        refresh();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //查询用户已使用的片段以及是否vip
  Future<bool> getVideosUsedShots(var arenaId, {int type = 0}) async {
    var param = {
      'arenaId': arenaId,
    };
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.show();
    }
    final res =
        await Api().get(ApiUrl.getVideosUsedShots, queryParameters: param);
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      log("getVideosUsedShots=${res.data["vip"]}");
      //{vip: false, shotCount: 0, remainingCount: 50, shots: null}
      //是否vip      shotCount已经使用的次数    remainingCount 剩余次数  Shots今日合成进球id
      if (!((res.data["vip"] ?? false) as bool)) {
        if (type == 0) {
          if (!(dataFag["isFrist"] as bool)) {
            WxLoading.show();
          }
          final res2 = await Api()
              .get(ApiUrl.userIsNew, queryParameters: param, showError: false);
          if (!(dataFag["isFrist"] as bool)) {
            WxLoading.dismiss();
          }
          if (res2.isSuccessful()) {
            var userIsNewModel = UserIsNewModel.fromJson(res2.data);
            log("getCanDownLoadVideoUrl=${res2.data}-${userIsNewModel.isFirstRegister?.receivedVip == true}${userIsNewModel.isFirstRegister?.receivableActivityId}");
            if (!(userIsNewModel.isFirstRegister?.receivedVip == true) &&
                userIsNewModel.isFirstRegister?.isNew == true) {
              //领取vip
              getReciverVipDialog(S.current.Receive_now, S.current.Receive_no,
                  () async {
                AppPage.back();
                if (!(dataFag["isFrist"] as bool)) {
                  WxLoading.show();
                }
                var param22 = {
                  'activityId':
                      userIsNewModel.isFirstRegister?.receivableActivityId,
                };
                var url = await ApiUrl.getReceiveVip(
                    userIsNewModel.isFirstRegister?.receivableActivityId ?? "");
                await Api()
                    .get(url, queryParameters: param22, showError: false);
                if (!(dataFag["isFrist"] as bool)) {
                  WxLoading.dismiss();
                }
                await UserManager.instance.pullUserInfo().then((v) async {
                  await Future.delayed(const Duration(milliseconds: 200));
                  if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) ==
                      0) {
                    WxLoading.showToast(S.current.Please_open_vip_first);
                  } else {
                    //   //ai选球
                    return true;
                  }
                });
                BusUtils.instance
                    .fire(EventAction(key: EventBusKey.receiveVip));
              }, () {
                AppPage.back();
              });
            } else {
              //开通vip
              getNeedVipDialog(
                  S.current.Open_immediately, S.current.Talk_to_you_next_time,
                  () {
                AppPage.back();
                AppPage.to(Routes.vipPage).then((v) async {
                  await Future.delayed(const Duration(milliseconds: 1000));
                  if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) ==
                      0) {
                    WxLoading.showToast(S.current.Please_open_vip_first);
                  } else {
                    return true;
                  }
                });
              }, () {
                AppPage.back();
              });
            }
          } else {
            WxLoading.showToast(res.message);
            return false;
          }
        } else {
          return false;
        }
      } else {
        return true;
      }
    } else {
      if (type == 0) {
        WxLoading.showToast(res.message);
      }
      return false;
    }
    return false;
  }

  //查询用户已使用的片段以及是否vip 是否 分享获取权限 0 没有 1有 ；
  Future<bool> getVideosUsedShots2(var arenaId, {int type = 0}) async {
    var param = {
      'arenaId': arenaId,
    };
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.show();
    }
    final res =
        await Api().get(ApiUrl.getVideosUsedShots, queryParameters: param);

    if (res.isSuccessful()) {
      log("getVideosUsedShots2=${res.data}-user=${UserManager.instance.user?.userId}");
      // log("getVideosUsedShots2=${res.data["shareRights"]}");
      // log("getVideosUsedShots21=${(res.data["shareRights"] ?? 0) != 1}");
      //{vip: false, shotCount: 0, remainingCount: 50, shots: null}
      //是否vip      shotCount已经使用的次数    remainingCount 剩余次数  Shots今日合成进球id
      if ((res.data["shareRights"] ?? 0) != 1) {
        //是否 分享获取权限 0 没有 1有
        return false;
      } else {
        return true;
      }
    } else {
      return false;
    }
  }

  //判断视频是否能下载  然后返回最新下载地址
  getCanDownLoadVideoUrl(String fragmentId, String videoId) async {
    var param = {
      'fragmentId': fragmentId,
      'videoId': videoId,
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res = await Api().get(ApiUrl.getCanDownLoadVideoUrl,
        queryParameters: param, showError: false);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    log("getCanDownLoadVideoUrl=${res.data}-${res.code}");
    if (res.isSuccessful()) {
      downloadAndSaveVideo(res.data["videoPath"]);
    } else if (res.code == 400001) {
      if (!(dataFag["isFrist"] as bool)) {
        WxLoading.show();
      }
      final res2 = await Api()
          .get(ApiUrl.userIsNew, queryParameters: param, showError: false);
      if (!(dataFag["isFrist"] as bool)) {
        WxLoading.dismiss();
      }
      if (res2.isSuccessful()) {
        var userIsNewModel = UserIsNewModel.fromJson(res2.data);
        log("getCanDownLoadVideoUrl=${res2.data}-${userIsNewModel.isFirstRegister?.receivedVip == true}${userIsNewModel.isFirstRegister?.receivableActivityId}");
        if (!(userIsNewModel.isFirstRegister?.receivedVip == true)) {
          //领取vip
          getReciverVipDialog(S.current.Receive_now, S.current.Receive_no,
              () async {
            AppPage.back();
            if (!(dataFag["isFrist"] as bool)) {
              WxLoading.show();
            }
            var param22 = {
              'activityId':
                  userIsNewModel.isFirstRegister?.receivableActivityId,
            };
            var url = await ApiUrl.getReceiveVip(
                userIsNewModel.isFirstRegister?.receivableActivityId ?? "");
            await Api().get(url, queryParameters: param22, showError: false);
            if (!(dataFag["isFrist"] as bool)) {
              WxLoading.dismiss();
            }
            await UserManager.instance.pullUserInfo();
            await Future.delayed(const Duration(milliseconds: 200));
            isVip.value = await getVideosUsedShots(arenaID.value, type: 1);
            if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) == 0) {
              // WxLoading.showToast(S.current.Please_open_vip_first);
            } else {
              getDownLoad();
            }
          }, () {
            AppPage.back();
          });
        } else {
          //开通vip
          getNeedVipDialog(
              S.current.Open_immediately, S.current.Talk_to_you_next_time, () {
            AppPage.back();
            AppPage.to(Routes.vipPage).then((v) async {
              await Future.delayed(const Duration(milliseconds: 1000));
              if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) == 0) {
                WxLoading.showToast(S.current.Please_open_vip_first);
              } else {
                getDownLoad();
              }
            });
          }, () {
            AppPage.back();
          });
        }
      } else {
        WxLoading.showToast(res.message);
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  getDownLoad() {
    {
      var videoId = "";
      for (int i = 0;
          i < (state.dataList[state.indexVideo.value].otherVideos?.length ?? 0);
          i++) {
        if (state.dataList[state.indexVideo.value].otherVideos?[i]
                ?.cameraIndex ==
            state.indexVideoAngle.value) {
          videoId =
              state.dataList[state.indexVideo.value].otherVideos?[i]?.id ?? "";
        }
      }
      if ((state.dataList[state.indexVideo.value].videoPath ?? "").isEmpty ||
          state.indexVideo.value == 9999 ||
          videoId == "") {
        WxLoading.showToast(S.current.Please_select_video_click_Download);
        return;
      }
      if (UserManager.instance.isLogin) {
        getCanDownLoadVideoUrl(
            state.dataList[state.indexVideo.value].id ?? "", videoId);
      } else {
        AppPage.to(Routes.login).then((onValue) async {
          await Future.delayed(const Duration(milliseconds: 500));
          if (UserManager.instance.isLogin) {
            getCanDownLoadVideoUrl(
                state.dataList[state.indexVideo.value].id ?? "", videoId);
          } else {
            WxLoading.showToast(S.current.please_login);
          }
        });
      }
    }
  }

  @override
  void onClose() {
    //isVip.close();
    videoController.pause();
    videoController.videoPlayerController?.setVolume(0);
    videoController.videoPlayerController?.seekTo(const Duration(seconds: 0));
    // videoController.dispose();
    // eventbus?.cancel();
    // WidgetsBinding.instance.removeObserver(this);
    http.Client().close(); // 如果使用http包
    // VideoBufferManager.unregisterController(videoController);
    // // 当检测到内存不足时
    // if (MemoryUsage.current.percent > 0.8) {
    //   MemoryMonitor().triggerMemoryCleanup();
    // }
    eventbus?.cancel();
    rootBundle.evict('');
    WidgetsBinding.instance.removeObserver(this);
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    _timer?.cancel(); // 确保在组件销毁时取消计时器
    super.onClose();
  }

  @override
  void dispose() {
    // 停止所有动画
    videoController.dispose();
    // http.Client().close(); // 如果使用http包
    // // VideoBufferManager.unregisterController(videoController);
    // // // 当检测到内存不足时
    // // if (MemoryUsage.current.percent > 0.8) {
    // //   MemoryMonitor().triggerMemoryCleanup();
    // // }
    // eventbus?.cancel();
    // imageCache.clear(); //	图片内存过高时
    // rootBundle.evict('');
    // WidgetsBinding.instance.removeObserver(this);
    // _timer?.cancel(); // 确保在组件销毁时取消计时器

    // 释放视频编解码器
    SystemChannels.platform.invokeMethod('VideoPlayer.disposeAll');

    // 强制GC（针对视频编解码器泄漏）
    SystemChannels.platform.invokeMethod('Memory.forceGC');

    super.dispose();
  }

  //改变选择视频index
  void changeVideoIndex(
    int index,
  ) {
    state.indexVideo.value = index;
    state.indexVideoAngle.value = state.dataList[index].cameraIndex ?? 0;
    if (state.indexVideo.value != 9999) {
      log("betterPlayer-message11=3-${state.dataList[index].videoPath ?? ""}");

      reloadVideo(state.dataList[index].videoPath ?? "");
    }
  }

  //改变选择视频角度
  void changeVideoAngle(
    int index,
  ) {
    state.indexVideoAngle.value =
        state.dataList[state.indexVideo.value].cameras?[index]!.cameraIndex ??
            0;
    if (state.indexVideo.value != 9999) {
      var videoPath = '';
      for (int i = 0;
          i < (state.dataList[state.indexVideo.value].otherVideos?.length ?? 0);
          i++) {
        if (state.dataList[state.indexVideo.value].otherVideos?[i]
                ?.cameraIndex ==
            state.indexVideoAngle.value) {
          videoPath = state.dataList[state.indexVideo.value].otherVideos?[i]
                  ?.videoPath ??
              "";
        }
      }
      if (videoPath.isNotEmpty) {
        reloadVideo(videoPath);
      }
    }
  }

//打卡随着手势移动
  Offset calOffset(Size size, Offset offset, Offset nextOffset) {
    double dx = 0;
    //水平方向偏移量不能小于0不能大于屏幕最大宽度
    if (offset.dx + nextOffset.dx <= 0) {
      dx = 0;
    } else if (offset.dx + nextOffset.dx >= (size.width - 50)) {
      dx = size.width - 50;
    } else {
      dx = offset.dx + nextOffset.dx;
    }
    double dy = 0;
    //垂直方向偏移量不能小于0不能大于屏幕最大高度
    if (offset.dy + nextOffset.dy >= (size.height - 100)) {
      dy = size.height - 100;
    } else if (offset.dy + nextOffset.dy <= kToolbarHeight) {
      dy = kToolbarHeight;
    } else {
      dy = offset.dy + nextOffset.dy;
    }
    return Offset(
      dx,
      dy,
    );
  }

  //存入数据库
  Future<void> getDaoInsertGoalList(List<OptionGoalModel> dataList) async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final optionGoalDao = database.optionGoalDao;
    await optionGoalDao.deleteAll(arenaID.value.toString(),
        UserManager.instance.userInfo.value?.userId ?? "");
    await Future.delayed(const Duration(milliseconds: 200));
    await optionGoalDao.insertGoalList(dataList, arenaID.value.toString(),
        UserManager.instance.userInfo.value?.userId ?? "");
  }

  //数据库取数据
  Future<void> getDaoGoalList() async {
    if (!UserManager.instance.isLogin) {
      return;
    }
    // 获取当前本地时间
    DateTime now = DateTime.now();

    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final optionGoalDao = database.optionGoalDao;
    await optionGoalDao
        .findAllGoal(arenaID.value.toString(),
            UserManager.instance.userInfo.value?.userId ?? "")
        .then((v) {
      // // 使用 where 方法过滤列表
      List<OptionGoalModel> filteredNumbers = v.where((value) {
        bool result = false;
        String? timeString = value.videoDate;
        if (timeString != null) {
          try {
            // 解析日期时间字符串
            DateTime dateTime = DateTime.parse(timeString);

            // 判断两个日期是否相差8天以内
            result = Utils.areDatesWithinEightDays(now, dateTime);
            // 打印 UTC 时间和本地时间
            // log("getDaoSql5=${result}");
          } catch (e) {
            log("getDaoSql6catch=${e}");
          }
        }

        return result;
      }).toList();

      state.dataCheckList.assignAll(filteredNumbers);
    });
    await Future.delayed(const Duration(milliseconds: 100));
    for (int i = 0; i < state.dataList.length; i++) {
      bool containsBob =
          state.dataCheckList.any((item2) => item2.id == state.dataList[i].id);
      if (containsBob) {
        state.dataList[i].selected = true;
      } else {
        state.dataList[i].selected = false;
      }
    }
    state.dataList.refresh();
    await optionGoalDao.deleteAll(arenaID.value.toString(),
        UserManager.instance.userInfo.value?.userId ?? "");
    await Future.delayed(const Duration(milliseconds: 100));
    await optionGoalDao.insertGoalList(
        state.dataCheckList,
        arenaID.value.toString(),
        UserManager.instance.userInfo.value?.userId ?? "");
  }

  //新增数据和删除数据
  Future<void> checkVideo(int index) async {
    state.dataList[index].selected = !(state.dataList[index].selected ?? false);
    state.dataList.refresh();
    // // 使用 where 方法过滤列表
    // List<OptionGoalModel> filteredNumbers = state.dataList
    //     .where((value) => (value.selected ?? false) == true)
    //     .toList();
    // state.dataCheckList.assignAll(filteredNumbers);
    log("checkVideo2-1");
    //0新增 1删除
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final optionGoalDao = database.optionGoalDao;
    if (state.dataList[index].selected ?? false) {
      optionGoalDao.insertGoal(state.dataList[index], arenaID.value.toString(),
          UserManager.instance.userInfo.value?.userId ?? "");
      log("checkVideo2-2");
    } else {
      var optionGoalModel = state.dataList[index];
      await optionGoalDao.deleteGoal1(optionGoalModel.arenaID ?? "",
          optionGoalModel.userId ?? "", optionGoalModel.id ?? "");
      log("checkVideo2-3");
    }
    await optionGoalDao
        .findAllGoal(arenaID.value.toString(),
            UserManager.instance.userInfo.value?.userId ?? "")
        .then((v) {
      state.dataCheckList.assignAll(v);
      log("checkVideo2-4+${v.length}-${arenaID.value.toString()}-${UserManager.instance.userInfo.value?.userId ?? ""}");
    });
    log("checkVideo2-5");
    if (state.dataList[index].selected == true) {
      if ((index + 1) < state.dataList.length) changeVideoIndex(index + 1);
    }
  }

  //下载视频
  void downloadAndSaveVideo(String path) {
    Utils.downloadAndSaveToPhotoAlbum(path);
  }

  //弹窗选择时间
  Future<void> chooseTime(String startTime, String endTime, int type) async {
    state.videoStartTime.value = startTime;
    state.videoEndTime.value = endTime;
    state.videoNextStartTime.value = endTime;
    DateTime endtime = DateTime.parse(endTime);
    // 增加一个小时
    DateTime nextStartTime = endtime.add(const Duration(hours: 1));
    // 定义日期时间格式
    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
    state.videoNextEndTime.value = dateFormat.format(nextStartTime);
    getDataList(isLoad: false);
    var mykey =
        "userIdarenaId${UserManager.instance.userInfo.value?.userId}${arenaID.value}";
    await WxStorage.instance.setString(mykey,
        "${courtId.value}*${state.videoStartTime.value}*${state.videoEndTime.value}");
    // var mykey2 = await WxStorage.instance.getString(mykey);
    // log("userIdarenaId=${mykey2 ?? ""}");
  }

  void showDatePicker(BuildContext context, int type) {
    //0开始时间 1结束时间
    FocusManager.instance.primaryFocus?.unfocus();
    DatePicker.showDatePicker(
      context,
      locale: DateTimePickerLocale.zh_cn, // 设置为中文
      pickerTheme: DateTimePickerTheme(
        backgroundColor: Colours.color191921,
        itemTextStyle: TextStyles.display16,
        itemHeight: 60,
        showTitle: true,
        title: Container(
          padding: const EdgeInsets.only(left: 20, right: 20, top: 25),
          height: 50,
          decoration: const BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Text(
                  S.current.cancel,
                  style:
                      TextStyles.display14.copyWith(color: Colours.color9393A5),
                ),
              ),
              Text(
                type == 0
                    ? S.current.Please_select_start_time
                    : S.current.Please_select_end_time,
                style: TextStyles.titleSemiBold16,
              ),
              GestureDetector(
                onTap: () {
                  if (type == 0) {
                    state.videoStartTime1.value = _date;
                  } else {
                    state.videoEndTime1.value = _date;
                  }
                  Navigator.pop(context);
                },
                child: Text(
                  S.current.save,
                  style:
                      TextStyles.display14.copyWith(color: Colours.colorA44EFF),
                ),
              ),
            ],
          ),
        ),
        titleHeight: 50,
      ),
      initialDateTime: type == 0
          ? state.videoStartTime1.value.isEmpty
              ? DateTime.now()
              : DateFormat('HH:mm').parse(state.videoStartTime1.value, false)
          : state.videoEndTime1.value.isEmpty
              ? DateTime.now()
              : DateFormat('HH:mm').parse(state.videoEndTime1.value, false),
      dateFormat: S.current.hh_mm,
      // maxDateTime: DateTime.parse(MAX_DATETIME),
      // minDateTime: DateTime.parse(MIN_DATETIME),
      onConfirm: (dateTime, _) {},
      onChange: (dateTime, _) {
        _date = DateFormat('HH:mm').format(dateTime);
      },
    );
  }

//精准查找 确认按钮
  void sureTimeSearch(BuildContext context) {
    if (state.videoDate1.value.isEmpty) {
      WxLoading.showToast(S.current.time_choose_tips1);
      return;
    }

    if (state.videoStartTime1.value.isEmpty) {
      WxLoading.showToast(S.current.Please_select_start_time);
      return;
    }
    if (state.videoEndTime1.value.isEmpty) {
      WxLoading.showToast(S.current.Please_select_end_time);
      return;
    }
    var startTime =
        DateFormat('HH:mm').parse(state.videoStartTime1.value, false);
    var endTime = DateFormat('HH:mm').parse(state.videoEndTime1.value, false);
    if (startTime.isAfter(endTime)) {
      WxLoading.showToast(S.current.time_choose_tips2);
      return;
    }
    DateTime twoHoursEarlier = endTime.subtract(Duration(hours: 2));
    if (startTime.isBefore(twoHoursEarlier)) {
      WxLoading.showToast(S.current.time_choose_tips3);
      return;
    }
    if (startTime == endTime) {
      WxLoading.showToast(S.current.time_choose_tips4);
      return;
    }
    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");

    // 解析日期时间字符串
    DateTime originalTime =
        DateFormat("yyyy.MM.dd").parse(state.videoDate1.value);
    state.videoDate.value = dateFormat.format(originalTime);

    DateTime startDateTime1 = DateTime(originalTime.year, originalTime.month,
        originalTime.day, startTime.hour, startTime.minute, 00);
    DateTime endDdteTime2 = DateTime(originalTime.year, originalTime.month,
        originalTime.day, endTime.hour, endTime.minute, 00);

    Navigator.pop(context);
    chooseTime(
        dateFormat.format(startDateTime1), dateFormat.format(endDdteTime2), 3);
  }

  // //ai选球弹窗
  // Future<bool> getAiOptionGoal(BuildContext context) async {
  //   var param = {
  //     'arenaId': arenaID.value,
  //     'courtId': int.tryParse(courtId.value),
  //     //'videoDate': videoDate.value,
  //     'videoStartTime': state.videoStartTime.value,
  //     'videoEndTime': state.videoEndTime.value,
  //   };
  //   if (!(dataFag["isFrist"] as bool)) {
  //     WxLoading.show();
  //   }
  //   final res =
  //       await Api().get(ApiUrl.getClassifications, queryParameters: param);
  //   if (!(dataFag["isFrist"] as bool)) {
  //     WxLoading.dismiss();
  //   }
  //   if (res.isSuccessful()) {
  //     if (res.data == null) {
  //       WxLoading.showToast(S.current.no_ai_data);
  //       return false;
  //     }

  //     List list = res.data;
  //     List<AiOptionModel> modelList =
  //         list.map((e) => AiOptionModel.fromJson(e)).toList();
  //     log("getAiOptionGoal=${res.data}");
  //     state.aiDataList.assignAll(modelList);
  //     if (modelList.isEmpty) {
  //       WxLoading.showToast(S.current.no_ai_data);
  //     } else {
  //       return true;
  //     }
  //     return false;
  //   } else {
  //     WxLoading.showToast(res.message);
  //     return false;
  //   }
  // }
  //ai选球弹窗
  Future<bool> getAiOptionGoal(BuildContext context) async {
    var param = {
      'arenaId': arenaID.value,
      'courtId': int.tryParse(courtId.value),
      //'videoDate': videoDate.value,
      'videoStartTime': state.videoStartTime.value,
      'videoEndTime': state.videoEndTime.value,
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res =
        await Api().get(ApiUrl.getClassifications2, queryParameters: param);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      if (res.data == null) {
        WxLoading.showToast(S.current.no_ai_data);
        return false;
      }

      List list = res.data;
      List<AiOptionModel> modelList =
          list.map((e) => AiOptionModel.fromJson(e)).toList();
      log("getAiOptionGoal=${res.data}");
      state.aiDataList.assignAll(modelList);
      if (modelList.isEmpty) {
        WxLoading.showToast(S.current.no_ai_data);
      } else {
        return true;
      }
      return false;
    } else {
      WxLoading.showToast(res.message);
      return false;
    }
  }

  //获得最新列表
  getDataList2(
    var ids, {
    isLoad = true,
  }) async {
    var param = {
      'classifications': ids,
      'arenaId': arenaID.value,
      'courtId': int.tryParse(courtId.value),
      'videoStartTime': state.videoStartTime.value,
      'videoEndTime': state.videoEndTime.value,
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    // if (aIoptionCheckID.value == "") {
    //   //增加ai选球标签
    //   aIoptionCheckID.value = ids;
    // }
    final res = await Api()
        .get(ApiUrl.getClassificationsVideos, queryParameters: param);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      List list = res.data;
      List<OptionGoalModel> modelList =
          list.map((e) => OptionGoalModel.fromJson(e)).toList();
      if (isLoad) {
        state.dataList.addAll(modelList);
        state.dataList.refresh();
      } else {
        state.dataList.assignAll(modelList);
        if (state.dataList.isNotEmpty) {
          changeVideoIndex(0);
        } else {
          state.indexVideo.value = 9999;
          videoController.setData(videoPath: '');
        }
      }
      if (state.rememberOption.value == "0") {
        await Future.delayed(const Duration(milliseconds: 100));
        for (int i = 0; i < state.dataList.length; i++) {
          state.dataList[i].selected = true;
        }
        state.dataList.refresh();
        final database =
            await $FloorAppDatabase.databaseBuilder('app_database.db').build();
        final optionGoalDao = database.optionGoalDao;
        await Future.delayed(const Duration(milliseconds: 100));
        await optionGoalDao.insertGoalList(
            state.dataList,
            arenaID.value.toString(),
            UserManager.instance.userInfo.value?.userId ?? "");
      }
      //读取数据库数据
      getDaoGoalList();
      if (dataFag["isFrist"] as bool) {
        dataFag["isFrist"] = false;
        refresh();
      }
      if (aIoptionCheckID.value == "") {
        //增加ai选球标签
        aIoptionCheckID.value = ids;
        aIoptionCheckDx.value = 0.w;
        // 开始动画
        // 使用 Future.delayed 创建一个一次性计时器
        // Future.delayed(Duration(milliseconds: 2000), () {
        //   // 在这里执行你想要的操作
        //   _startTimer();
        // });
        _startTimer();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void _startTimer() {
    var a = 0;
    _timer = Timer.periodic(const Duration(milliseconds: 1), (Timer timer) {
      if (aIoptionCheckDx.value < 130.w) {
        if ((aIoptionCheckDx.value >= 64.7.w &&
                aIoptionCheckDx.value <= 65.3.w) &&
            a <= 1000) {
          a++;
          log("a=$a");
        } else {
          aIoptionCheckDx.value = aIoptionCheckDx.value + 0.2.w;
        }
      } else {
        _timer?.cancel(); // 取消计时器
      }
    });
  }

//分享去邀请新人   0微信  1朋友圈
  Future<void> getShareWx(int i) async {
    var param = {
      'shareType': "1",
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res = await Api().get(ApiUrl.inviteMiniPath, queryParameters: param);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      if (res.data == null) {
        WxLoading.showToast(S.current.No_data_available);
        return;
      }
      log("getVideosUsedShots42=${jsonEncode(res.data)}-user=${UserManager.instance.user?.userId}");
      InviteMiniPathModel inviteMiniPathModel =
          InviteMiniPathModel.fromJson(res.data);
      // 下载网络图片并转换为 Uint8List
      Uint8List? imageBytes =
          await downloadImageToUint8List(inviteMiniPathModel.image ?? "");
      if (imageBytes == null || inviteMiniPathModel.image == "") {
        ByteData data = await rootBundle
            .load("assets/images/dialog_invitation.png"); //dialog_invitation
        imageBytes = data.buffer.asUint8List();
      }
      MyShareH5.shareMiniProgram(inviteMiniPathModel, imageBytes);
      // List list = res.data;
      // List<AiOptionModel> modelList =
      //     list.map((e) => AiOptionModel.fromJson(e)).toList();
      // log("getAiOptionGoal=${res.data}");
      // state.aiDataList.assignAll(modelList);
    } else {
      WxLoading.showToast(res.message);
    }

    if (i == 0) {
    } else {}
  }

  Future<Uint8List?> downloadImageToUint8List(String imageUrl) async {
    try {
      // 发起 HTTP GET 请求获取图片数据
      final response = await http.get(Uri.parse(imageUrl));

      // 检查响应状态码是否成功
      if (response.statusCode == 200) {
        // 将响应体的字节数据转换为 Uint8List
        return Uint8List.fromList(response.bodyBytes);
      } else {
        print("Failed to load image: ${response.statusCode}");
        return null;
      }
    } catch (e) {
      print("Error downloading image: $e");
      return null;
    }
  }

  //ai选球弹窗
  void getAiOptionGoalDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 532,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 18.w, bottom: 20.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.Please_select_your_photo,
                      style: TextStyles.medium.copyWith(fontSize: 16.sp),
                    )),
                Expanded(
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      SizedBox(
                        height: 480.w,
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            GridView.builder(
                                scrollDirection: Axis.vertical,
                                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                shrinkWrap: true,
                                physics:
                                    const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 3,
                                  crossAxisSpacing: 15,
                                  mainAxisSpacing: 15,
                                  childAspectRatio: 102 / 179,
                                ),
                                padding:
                                    EdgeInsets.only(bottom: 60.w, top: 0.w),
                                itemCount: state.aiDataList.length,
                                itemBuilder: (context, index) {
                                  return Obx(() {
                                    return GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        state.aiDataList[index].isSelect =
                                            !(state.aiDataList[index]
                                                    .isSelect ??
                                                false);
                                        state.aiDataList.refresh();
                                      },
                                      child: Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          MyImage(
                                            state.aiDataList[index].picture ??
                                                '',
                                            //  holderImg: "home/index/df_banner_top",
                                            fit: BoxFit.fill,
                                            width: 102.w,
                                            height: 179.w,
                                            isAssetImage: false,
                                            // errorImg: "home/index/df_banner_top"
                                            radius: 8.r,
                                          ),
                                          Container(
                                            width: 102.w,
                                            height: 179.w,
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                                color: !(state.aiDataList[index]
                                                            .isSelect ??
                                                        false)
                                                    ? null
                                                    : Colours.color50000000),
                                            child: !(state.aiDataList[index]
                                                        .isSelect ??
                                                    false)
                                                ? null
                                                : Container(
                                                    width: 20.w,
                                                    height: 20.w,
                                                    margin: EdgeInsets.only(
                                                        right: 8.w,
                                                        bottom: 3.w,
                                                        top: 8.w),
                                                    child: const Icon(
                                                      Icons.check,
                                                      color: Colours.white,
                                                      size: 20,
                                                    ),
                                                  ),
                                          )
                                        ],
                                      ),
                                    );
                                  });
                                }),
                          ],
                        ),
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          List<AiOptionModel> filteredNumbers =
                              state.aiDataList.where((value) {
                            bool result = value.isSelect ?? false;
                            return result;
                          }).toList();
                          if (filteredNumbers.isEmpty) {
                            WxLoading.showToast(
                                S.current.select_your_photo_tips);
                            return;
                          }
                          AppPage.back();
                          List<int?> ids =
                              filteredNumbers.map((item) => item.id).toList();
                          // getDataList2(ids.join(","), isLoad: false);
                          //  log("classifications2=${ids.join(",")}");
                          // 在导航前重置状态
                          // SiteReportLogic.to.reset();
                          AppPage.to(Routes.siteReportPage, arguments: {
                            "arenaId": arenaID.value.toString(),
                            "classifications":
                                ids.join(","), //456013,456014,456015,456018
                            "courts": courtId.value,
                            "videoEndTime": state.videoEndTime.value,
                            "videoStartTime": state.videoStartTime.value
                          });
                        },
                        child: Container(
                          height: 46.w,
                          width: double.infinity,
                          alignment: Alignment.center,
                          margin: EdgeInsets.only(bottom: 7.w),
                          padding: EdgeInsets.only(
                              left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                          decoration: BoxDecoration(
                            color: Colours.color282735,
                            borderRadius:
                                BorderRadius.all(Radius.circular(28.r)),
                            gradient: const LinearGradient(
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ],
                              begin: Alignment.bottomLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Text(
                            S.current.select_your_photo_tips2,
                            style: TextStyles.titleMedium18
                                .copyWith(fontSize: 16.sp),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
              ],
            ),
          );
        });
      },
    );
  }
}
