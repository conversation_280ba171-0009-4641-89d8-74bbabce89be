// ignore_for_file: unrelated_type_equality_checks

import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/option_site_model.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:intl/src/intl/date_format.dart';

///球场主页->三级页面  选择场地
class OptionSiteLogic extends GetxController {
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var arenaID = 0.obs;
  var optionSiteModel = OptionSiteModel().obs;
  //数据列表
  RxList<Matches> dataList = <Matches>[].obs;
  var startTime = "".obs;
  var endTime = "".obs;
  var todayDate = "";
  var yesterdayDate = "";
  var optionSiteModelCourts = OptionSiteModelCourts().obs;
  var type = 0.obs; //0普通球馆  1高级球馆 有ai身形
  RxList<String> maintenanceArenaList = <String>[].obs;
  @override
  void onInit() {
    super.onInit();
    arenaID.value = Get.arguments["arenaID"] as int;
    type.value = Get.arguments["type"] as int;
    DateFormat dateFormat = DateFormat("yyyy-MM-dd");
    DateTime nowDateTime = DateTime.now();
    todayDate = dateFormat.format(nowDateTime);
    DateTime nextEndTime = nowDateTime.subtract(const Duration(days: 1));
    yesterdayDate = dateFormat.format(nextEndTime);
  }

  @override
  void onReady() {
    super.onReady();

    getDataInfo(true);
  }

  Future<void> getData2() async {
    var mykey =
        "userIdarenaId${UserManager.instance.userInfo.value?.userId}${arenaID.value}";
    var mykey2 = await WxStorage.instance.getString(mykey);
    if (mykey2 != null && mykey2 != "") {
      var myOptionList = mykey2.split("*");
      if (myOptionList.length == 3) {
        startTime.value = myOptionList[1];
        endTime.value = myOptionList[2];
        for (var item in optionSiteModel.value.courts!) {
          if (item!.courtId.toString().trim() ==
                  myOptionList[0].toString().trim() &&
              myOptionList[0].toString().trim() != "") {
            optionSiteModelCourts.value = item;
          }
        }
      }
    }
  }

//获得数据
  Future<void> getDataInfo(bool isRefresh) async {
    var mykey =
        "userIdarenaId${UserManager.instance.userInfo.value?.userId}${arenaID.value}";
    var mykey2 = await WxStorage.instance.getString(mykey);
    // WxLoading.show();
    var url = await ApiUrl.getCourtsUrl(arenaID.value);
    final res = await Api().get(url);
    //WxLoading.dismiss();
    log("getDataInfo=${res.data}");
    if (res.isSuccessful()) {
      optionSiteModel.value = OptionSiteModel.fromJson(res.data);
      optionSiteModel.refresh();
      // maintenanceArenaList.value = (optionSiteModel.value.courts ?? [])
      //     .where((item) => (item?.status ?? 0) == 1)
      //     .map((item) => (item?.courtName ?? ''))
      //     .toList();
      maintenanceArenaList.value = [
        '1-2',
        '1-1',
      ];
      log("message!!!!!!!!!${maintenanceArenaList.value}");
      if (mykey2 != null && mykey2 != "") {
        var myOptionList = mykey2.split("*");
        if (myOptionList.length == 3) {
          startTime.value = myOptionList[1];
          endTime.value = myOptionList[2];
          for (var item in optionSiteModel.value.courts!) {
            if (item!.courtId.toString().trim() ==
                    myOptionList[0].toString().trim() &&
                myOptionList[0].toString().trim() != "") {
              optionSiteModelCourts.value = item;
            }
          }
        }
      }

      if (dataFag["isFrist"] as bool) {
        dataFag["isFrist"] = false;
        dataFag.refresh();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
