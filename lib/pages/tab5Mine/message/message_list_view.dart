import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/message_list_model.dart';
import 'package:shoot_z/pages/tab5Mine/message/message_list_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的  单个分类的列表
class MessageListPage extends StatelessWidget {
  MessageListPage({super.key});

  final logic = Get.put(MessageListLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Obx(() {
          return Text(logic.messageName.value);
        }),
      ),
      body: _listWidget(context),
    );
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
          controller: logic.refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: true,
          enablePullUp: true,
          onRefresh: () {
            logic.getdataList(
                isLoad: false, controller: logic.refreshController);
          },
          onLoading: () {
            logic.getdataList(controller: logic.refreshController);
          },
          physics: const AlwaysScrollableScrollPhysics(),
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 150.w, height: 89.w),
                        ),
                      ],
                    )
                  : ListView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      padding: EdgeInsets.only(bottom: 40.w),
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: logic.dataList.length,
                      itemBuilder: (context, position) {
                        return //type  1 官方消息, 2 系统消息
                            logic.dataList[position].type == 2
                                ? _listItemWidget1(logic.dataList[position])
                                : _listItemWidget2(logic.dataList[position]);
                      }));
    });
  }

  /// 构建列表项
  Widget _listItemWidget1(MessageListModel item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // AppPage.to(Routes.teamInfoPage, arguments: {
        //   'teamId': item.id,
        // }).then((v) {
        //   logic.getdataList(controller: logic.refreshController, isLoad: false);
        // });
        switch (item.subType) {
          case 1: //赛事报告生成通知
            AppPage.to(Routes.gameDetailsPage,
                arguments: item.businessId.toString());
            break;
          case 2: //视频集锦生成
            AppPage.to(Routes.highlightsPage);
            break;
          case 3: //ai战报生成
            AppPage.to(Routes.aiBattleReportpage,
                arguments: item.businessId.toString());
            break;
          case 6: //奖励到帐
            AppPage.to(Routes.couponsPage);
            break;
          case 8:
            AppPage.to(Routes.vipPage);
            break;
          default:
            break;
        }
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            color: Colours.color191921),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (item.isRead == false)
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    width: 6.w,
                    height: 6.w,
                    decoration: BoxDecoration(
                        color: Colours.red,
                        borderRadius: BorderRadius.circular(3.r)),
                  ),
                ],
              ),
            Row(
              children: [
                Expanded(
                  child: Text(
                    item.title ?? "", // item.sendName ??
                    style: TextStyles.bold
                        .copyWith(fontSize: 14.sp, color: Colours.white),
                  ),
                ),
                Text(
                  item.sendTime ?? "",
                  style: TextStyles.regular
                      .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
                ),
              ],
            ),
            SizedBox(
              height: 10.w,
            ),
            Text(
              item.content ?? "",
              maxLines: 20,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.regular.copyWith(
                  fontSize: 14.sp, color: Colours.color5C5C6E, height: 1.2),
            ),
            SizedBox(
              height: 20.w,
            ),
            if (item.subType == 1 ||
                item.subType == 2 ||
                item.subType == 6 ||
                item.subType == 8)
              const Divider(
                color: Colours.color99292937,
                height: 1,
              ),
            //9
            if (item.subType == 1 ||
                item.subType == 2 ||
                item.subType == 6 ||
                item.subType == 8)
              SizedBox(
                height: 42.w,
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        "查询详情",
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color5C5C6E),
                      ),
                    ),
                    MyImage("ic_arrow_right.png",
                        width: 14.w,
                        height: 14.w,
                        isAssetImage: true,
                        imageColor: Colours.white),
                  ],
                ),
              )
          ],
        ),
      ),
    );
  }

  //官方消息
  Widget _listItemWidget2(MessageListModel item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        AppPage.to(Routes.messageInfoPage, arguments: {
          'messageId': item.messageId.toString(),
          'messageName': logic.messageName.value,
        }).then((onValue) {
          logic.getdataList(controller: logic.refreshController, isLoad: false);
        });
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            color: Colours.color191921),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (item.isRead == false)
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    width: 6.w,
                    height: 6.w,
                    decoration: BoxDecoration(
                        color: Colours.red,
                        borderRadius: BorderRadius.circular(3.r)),
                  ),
                ],
              ),
            Text(
              item.title ?? "",
              maxLines: 20,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.bold
                  .copyWith(fontSize: 14.sp, color: Colours.white, height: 1.2),
            ),
            if ((item.coverImg ?? "") != "")
              SizedBox(
                height: 15.w,
              ),
            if ((item.coverImg ?? "") != "")
              MyImage(
                item.coverImg ?? "",
                width: double.infinity,
                // height: 172.w,
                radius: 8.r,
                errorImage: "error_image_width.png",
                placeholderImage: "error_image_width.png",
                // fit: BoxFit.contain,
              ),
            SizedBox(
              width: double.infinity,
              height: 56.w,
              child: Row(
                children: [
                  WxAssets.images.messageJiqi.image(width: 26.w, height: 26.w),
                  SizedBox(
                    width: 6.w,
                  ),
                  Expanded(
                    child: Text(
                      item.sendName ?? "",
                      style: TextStyles.regular
                          .copyWith(fontSize: 12.sp, color: Colours.white),
                    ),
                  ),
                  Text(
                    item.sendTime ?? "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyles.regular.copyWith(
                        fontSize: 12.sp,
                        color: Colours.color5C5C6E,
                        height: 1.2),
                  ),
                  MyImage("ic_arrow_right.png",
                      width: 14.w,
                      height: 14.w,
                      isAssetImage: true,
                      imageColor: Colours.white),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
