import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/utils.dart';

class BindNewPhoneLogic extends GetxController {
//  什么时候调用 onClose：
//  •	控制器的依赖被释放时触发，例如：
//  •	页面销毁且没有其他地方依赖该控制器。
//  •	显式调用 Get.delete 删除控制器。
  TextEditingController phoneController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  String checkOldPhoneId = '';
  // 倒计时相关状态
  var countdown = 0.obs;
  var isCountdownActive = false.obs;
  Timer? countdownTimer;
  var areaCode = "+86";
  var oldPhoneNum = '';
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('oldPhoneNum')) {
      oldPhoneNum = Get.arguments['oldPhoneNum'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('checkOldPhoneId')) {
      checkOldPhoneId = Get.arguments['checkOldPhoneId'];
    }
  }

  @override
  void onClose() {
    // 清理定时器
    countdownTimer?.cancel();
    super.onClose();
  }

  /// 开始倒计时
  void startCountdown() {
    countdown.value = 60;
    isCountdownActive.value = true;

    countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown.value > 0) {
        countdown.value--;
      } else {
        timer.cancel();
        isCountdownActive.value = false;
      }
    });
  }

  void getCode(BuildContext context) async {
    if (phoneController.text.isEmpty ||
        !Utils.isValidPhoneNumber(phoneController.text)) {
      WxLoading.showToast(S.current.mobile_number_hint);
      return;
    }
    onKeyDismiss();
    WxLoading.show();
    var data = {'phone': phoneController.text, 'region': areaCode};
    var res = await Api().post(ApiUrl.getCode, data: data);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      // 启动一分钟倒计时
      startCountdown();

      // AppPage.to(Routes.loginCode,
      //     arguments: {...data, 'original': state.phoneController.text});
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void sureAction(BuildContext context) async {
    if (codeController.text.length != 4) {
      WxLoading.showToast('请输入有效的验证码');
      return;
    }
    onKeyDismiss();
    WxLoading.show();
    final params = {
      'phone': phoneController.text,
      'region': areaCode,
      'code': codeController.text,
      'checkOldPhoneId': checkOldPhoneId
    };
    var res = await Api().post(ApiUrl.bindNewPhone, data: params);
    WxLoading.dismiss();
    if (!res.isSuccessful()) {
      WxLoading.showToast(res.message);
      return;
    }
    logout();
  }

  Future<void> logout() async {
    Api().post(ApiUrl.logout);
    UserManager.instance.logout();
  }
}
