import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/matches_model.dart';
import 'package:shoot_z/network/model/team_home_model.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/challenge_detail_model.dart';
import 'package:shoot_z/routes/app.dart';

class BattleDetailLogic extends GetxController with WidgetsBindingObserver {
  String challengeId = '';
  var challengeModel = ChallengeDetailModel().obs;
  var matchList = <MatchesModel>[].obs;
  var isLoading = true.obs;
  var teamHomeModel = TeamHomeModel().obs;
  String teamId = '';
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('challengeId')) {
      challengeId = Get.arguments['challengeId'] ?? '';
      challengeDetail();
    } else {
      isLoading.value = false;
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  // 约战详情
  Future<void> challengeDetail() async {
    isLoading.value = true;
    WxLoading.show();
    var url = ApiUrl.getChallengeDetail(challengeId);
    final res = await Api().get(url);
    if (res.isSuccessful()) {
      cc.log("message${res.data}");
      challengeModel.value = ChallengeDetailModel.fromJson(res.data);
      if (challengeModel.value.leftTeamId != '0') {
        getTeamMatchList(challengeModel.value.leftTeamId!);
        getTeamInfo(challengeModel.value.leftTeamId!);
      }
    } else {
      WxLoading.showToast(res.message);
    }
    isLoading.value = false;
    WxLoading.dismiss();
  }

  // 联系发布者
  Future<void> createIntention(Map<String, dynamic> params) async {
    params['challengeId'] = challengeId;
    WxLoading.show();
    cc.log('message${params}');
    final res = await Api().post(ApiUrl.createIntention, data: params);
    if (res.isSuccessful()) {
      WxLoading.showToast('已提交');
      AppPage.back();
    } else {
      WxLoading.showToast(res.message);
    }
    WxLoading.dismiss();
  }

  //获取最近5场的比赛
  getTeamMatchList(String teamId) async {
    Map<String, dynamic> param = {
      'pageIndex': 1,
      'pageSize': 5,
      'teamId': teamId,
    };
    var url = await ApiUrl.getTeamSchedule(teamId);
    var res = await Api().get(url, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["result"] ?? [];
      cc.log("getTeamSchedule${res.data}");
      matchList.value = list.map((e) => MatchesModel.fromJson(e)).toList();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //球队集锦
  //获得球馆主页详情
  getTeamInfo(String teamId) async {
    Map<String, dynamic> param = {
      //  'teamId': teamId.value,
    };
    var url = await ApiUrl.getTeamSummary(teamId);
    var res = await Api().get(url, queryParameters: param);
    if (res.isSuccessful()) {
      teamHomeModel.value = TeamHomeModel.fromJson(res.data);
      cc.log("getTeamInfo${res.data}");
      teamHomeModel.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
