import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/team_player_rank_model.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item1/team_info_item_logic1.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/widgets/ImageDotPainter.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/PhotoView.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的球队 球馆主页->赛程列表

class TeamInfoItemPage1 extends StatefulWidget {
  const TeamInfoItemPage1({super.key});

  @override
  State<TeamInfoItemPage1> createState() => _GamePageState();
}

class _GamePageState extends State<TeamInfoItemPage1> {
  final logic = Get.put(TeamInfoItemLogic1());
  bool _isImagePreloaded = false;
  @override
  void initState() {
    super.initState();
    _preloadImages(); // 如果是同一张图片，可以只加载一次
  }

  Future<void> _preloadImages() async {
    try {
      // 预加载不同状态的图像
      await PreloadedImageDotPainter.preloadImage(
        'assets/images/chart_point.png',
      );
      setState(() => _isImagePreloaded = true);
    } catch (e) {
      debugPrint('Image preloading failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return logic.isFrist.value || (!_isImagePreloaded)
          ? buildLoad()
          : logic.teamHomeModel.value.teamId == null
              ? SizedBox(
                  height: 300.w,
                  child: myNoDataView(
                    context,
                    msg: S.current.No_data_available,
                    imagewidget: WxAssets.images.icGameNo
                        .image(width: 150.w, height: 150.w),
                  ))
              : CustomScrollView(
                  slivers: [
                    _playerListWidget(),
                    _hallOfFameWidget(),
                    _teamRankWidget(context),
                    _teamVideosUnlockWidget(context),
                    _teamPhotoWidget(),
                  ],
                );
    });
  }

  Widget _playerListWidget() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(S.current.Team_members,
              rightName: S.current.all,
              margin: EdgeInsets.only(top: 6.w),
              leftWidget: (logic.teamHomeModel.value.leader == true)
                  ? InkWell(
                      onTap: () {
                        AppPage.to(Routes.auditAddTeamPage, arguments: {
                          'teamId': logic.teamId.value,
                        }).then((onValue) {
                          BusUtils.instance.fire(
                              EventAction(key: EventBusKey.updateTeamPhoto));
                        });
                      },
                      child: SizedBox(
                        height: 40.w,
                        child: Stack(
                          alignment: AlignmentDirectional.centerStart,
                          children: [
                            Container(
                              padding: EdgeInsets.only(
                                  left: 8.w, right: 8.w, top: 4.w, bottom: 4.w),
                              decoration: BoxDecoration(
                                  color: Colours.color2E1575,
                                  borderRadius: BorderRadius.circular(10.r),
                                  border: Border.all(
                                      width: 1.w, color: Colours.color6435E9)),
                              child: Text(
                                S.current.Team_entry_review,
                                style: TextStyles.regular
                                    .copyWith(fontSize: 10.sp),
                              ),
                            ),
                            //hasAuditApply入队审核申请
                            if (logic.teamHomeModel.value.hasAuditApply == true)
                              Positioned(
                                right: 0,
                                top: 10,
                                child: Container(
                                  width: 6.w,
                                  height: 6.w,
                                  decoration: BoxDecoration(
                                    color: Colours.red,
                                    borderRadius: BorderRadius.circular(3.r),
                                  ),
                                ),
                              )
                          ],
                        ),
                      ),
                    )
                  : const SizedBox(), rightOnTap: () {
            if (Platform.isIOS) {
              // iOS特殊处理：暂时禁用可能冲突的手势
              ScaffoldMessenger.of(context).removeCurrentSnackBar();
            }
            AppPage.to(Routes.playersPage, arguments: {
              'teamId': logic.teamId.value,
            });
          }),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 18.w),
            child: Wrap(
              alignment: WrapAlignment.start,
              children: List.generate(
                  min((logic.teamHomeModel.value.members?.length ?? 0), 5),
                  (index) {
                return SizedBox(
                  width: 66.w,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      InkWell(
                          onTap: () {
                            AppPage.to(Routes.careerHighlightsHomePage,
                                arguments: {
                                  'userId': logic.teamHomeModel.value
                                          .members?[index]?.userId ??
                                      '0'
                                });
                          },
                          child: Column(
                            children: [
                              MyImage(
                                logic.teamHomeModel.value.members?[index]
                                        ?.userPhoto ??
                                    "",
                                width: 48.w,
                                height: 48.w,
                                radius: 24.r,
                                errorImage: "error_image_width.png",
                                placeholderImage: "error_image_width.png",
                                hasBorder: true,
                                borderColor: Colors.white,
                                borderWidth: 1.w,
                              ),
                              SizedBox(
                                height: 8.w,
                              ),
                              Text(
                                logic.teamHomeModel.value.members?[index]
                                        ?.userName ??
                                    "",
                                style: TextStyles.regular
                                    .copyWith(fontSize: 10.sp),
                                maxLines: 1,
                              )
                            ],
                          )),
                      if (logic.teamHomeModel.value.leaderUserId.toString() ==
                          (logic.teamHomeModel.value.members?[index]?.userId))
                        Positioned(
                            top: 38.w,
                            left: 21.w,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 3.w, vertical: 2.w),
                              decoration: BoxDecoration(
                                  color: Colours.colorFF661A,
                                  borderRadius: BorderRadius.circular(10.r)),
                              child: Text(
                                S.current.leader,
                                style:
                                    TextStyles.regular.copyWith(fontSize: 8.sp),
                                maxLines: 1,
                              ),
                            ))
                    ],
                  ),
                );
              }),
            ),
          )
        ],
      ),
    );
  }

  Widget _hallOfFameWidget() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(
            S.current.tab_home_bottom,
            margin: EdgeInsets.only(top: 6.w),
            rightWidget: Text(
              "${S.current.max_score}\t${logic.teamHomeModel.value.rankInfo?.highestRankScore ?? 0}${S.current.score2}\t\t${S.current.max_ranking}\t${logic.teamHomeModel.value.rankInfo?.highestRank ?? 0}",
              style: TextStyles.regular
                  .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 5.w),
            child: Text(
              "${S.current.Current_score}\t${logic.teamHomeModel.value.rankInfo?.rankScore ?? 0}${S.current.score2}\t\t${S.current.Current_ranking}\t${logic.teamHomeModel.value.rankInfo?.currentRank ?? 0}",
              style: TextStyles.regular
                  .copyWith(fontSize: 12.sp, color: Colours.white),
            ),
          ),
          Container(
            width: double.infinity,
            height: 181.w,
            padding: const EdgeInsets.all(16.0),
            child: LineChart(logic.lineChartData.value),
          ),
        ],
      ),
    );
  }

  Widget _teamRankWidget(context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(
            S.current.Team_ranking,
            height: 51,
            rightOnTap: () {
              showBottoPlayerRankDialog(context);
            },
          ),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 5.w),
            width: double.infinity,
            height: 36.w,
            decoration: BoxDecoration(
                color: Colours.color1C1827,
                borderRadius: BorderRadius.circular(20.r)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: List.generate(3, (index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.teamRankIndex.value = index;
                    logic.getTeamMembers(index + 1);
                  },
                  child: Container(
                    width: 114.w,
                    height: 32.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: logic.teamRankIndex.value == index
                              ? [Colours.color7B35ED, Colours.colorA253EF]
                              : [Colours.color1C1827, Colours.color1C1827],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight),
                      borderRadius: BorderRadius.all(Radius.circular(20.r)),
                    ),
                    child: Text(
                      logic.teamRankTitle[index],
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          color: Colours.white,
                          fontWeight: index == logic.teamRankIndex.value
                              ? FontWeight.bold
                              : FontWeight.normal),
                    ),
                  ),
                );
              }),
            ),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 5.w),
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8.r)),
            child: ((logic.dataFag["isFrist1"] as bool) &&
                        logic.teamRankIndex.value == 0) ||
                    ((logic.dataFag["isFrist2"] as bool) &&
                        logic.teamRankIndex.value == 1) ||
                    ((logic.dataFag["isFrist3"] as bool) &&
                        logic.teamRankIndex.value == 2)
                ? SizedBox(height: 154.w, child: buildLoad(isShowGif: false))
                : (logic.teamMemberList1.isEmpty &&
                            logic.teamRankIndex.value == 0) ||
                        (logic.teamMemberList2.isEmpty &&
                            logic.teamRankIndex.value == 1) ||
                        (logic.teamMemberList3.isEmpty &&
                            logic.teamRankIndex.value == 2)
                    ? SizedBox(
                        height: 154.w,
                        child: myNoDataView(context,
                            msg: S.current.no_data_team_rank,
                            imagewidget: WxAssets.images.teamInfoNodata
                                .image(width: 107.w, height: 72.w),
                            height: 2.w))
                    : Wrap(
                        spacing: 9.w,
                        children: List.generate(
                            min(
                                4,
                                logic.teamRankIndex.value == 0
                                    ? logic.teamMemberList1.length
                                    : logic.teamRankIndex.value == 1
                                        ? logic.teamMemberList2.length
                                        : logic.teamMemberList3.length),
                            (index) {
                          return GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              //TODO
                              AppPage.to(Routes.careerHighlightsHomePage,
                                  arguments: {
                                    'userId':
                                        logic.teamMemberList1[index].userId
                                  });
                            },
                            child: Container(
                              width: 72.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20.r)),
                              ),
                              child: Column(
                                children: [
                                  MyImage(
                                    (logic.teamRankIndex.value == 0
                                            ? logic
                                                .teamMemberList1[index].avatar
                                            : logic.teamRankIndex.value == 1
                                                ? logic.teamMemberList2[index]
                                                    .avatar
                                                : logic.teamMemberList3[index]
                                                    .avatar) ??
                                        "",
                                    width: 72.w,
                                    height: 72.w,
                                    radius: 8.r,
                                    errorImage: "error_image_width.png",
                                    placeholderImage: "error_image_width.png",
                                  ),
                                  SizedBox(
                                    height: 10.w,
                                  ),
                                  Text(
                                    (logic.teamRankIndex.value == 0
                                            ? logic
                                                .teamMemberList1[index].userName
                                            : logic.teamRankIndex.value == 1
                                                ? logic.teamMemberList2[index]
                                                    .userName
                                                : logic.teamMemberList3[index]
                                                    .userName) ??
                                        "",
                                    style: TextStyles.regular
                                        .copyWith(fontSize: 14.sp),
                                    maxLines: 1,
                                  ),
                                  SizedBox(
                                    height: 10.w,
                                  ),
                                  Text(
                                    "${(logic.teamRankIndex.value == 0 ? logic.teamMemberList1[index].score : logic.teamRankIndex.value == 1 ? logic.teamMemberList2[index].score : logic.teamMemberList3[index].score) ?? ""}",
                                    style: TextStyles.regular.copyWith(
                                        fontSize: 20.sp,
                                        fontWeight: FontWeight.w700),
                                    maxLines: 1,
                                  )
                                ],
                              ),
                            ),
                          );
                        }),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _teamVideosUnlockWidget(context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(
            S.current.Unlock_team_highlights,
            rightName: S.current.more,
            height: 51,
            margin: EdgeInsets.only(top: 6.w),
            rightOnTap: () {
              BusUtils.instance
                  .fire(EventAction(key: EventBusKey.teamChangeTab));
            },
          ),
          if (logic.teamHomeModel.value.videos?.isEmpty ?? true)
            myNoDataView(
              context,
              msg: S.current.no_highlights,
              height: 0,
              imagewidget: WxAssets.images.teamInfoNodata2
                  .image(width: 180.w, height: 120.w),
            ),
          if (!(logic.teamHomeModel.value.videos?.isEmpty ?? true))
            Padding(
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: List.generate(
                    min(2, (logic.teamHomeModel.value.videos?.length ?? 0)),
                    (index) {
                  return GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      if ((logic.teamHomeModel.value.videos?[index]
                                  ?.videoPath ??
                              "") !=
                          "") {
                        AppPage.to(Routes.videoPath, arguments: {
                          "videoPath": (logic.teamHomeModel.value.videos?[index]
                                  ?.videoPath ??
                              ""),
                          "teamName": (logic
                                  .teamHomeModel.value.videos?[index]?.title ??
                              ""),
                          "isShowShareUpdate": "0",
                        });
                      } else {
                        WxLoading.showToast(S.current.No_data_available);
                      }
                    },
                    child: Container(
                      margin: EdgeInsets.only(top: 5.w, bottom: 5.w),
                      width: 168.w,
                      height: 96.w,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r)),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          MyImage(
                            logic.teamHomeModel.value.videos?[index]
                                    ?.videoCover ??
                                "",
                            width: 168.w,
                            height: 96.w,
                            radius: 8.r,
                            errorImage: "error_image_width.png",
                            placeholderImage: "error_image_width.png",
                          ),
                          WxAssets.images.selfieShotPlay
                              .image(width: 32.w, height: 32.w),
                          Positioned(
                            bottom: 5.w,
                            right: 14.w,
                            child: Text(
                              "${logic.teamHomeModel.value.videos?[index]?.duration ?? ""}",
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp, color: Colours.white),
                            ),
                          )
                        ],
                      ),
                    ),
                  );
                }),
              ),
            )
        ],
      ),
    );
  }

  Widget _teamPhotoWidget() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(
            S.current.Team_photo,
            margin: EdgeInsets.only(top: 6.w),
            rightName: (logic.teamHomeModel.value.leader == true)
                ? S.current.Team_photo_set
                : null,
            height: 51,
            rightOnTap: !(logic.teamHomeModel.value.leader == true)
                ? null
                : () {
                    if (logic.teamHomeModel.value.leader == true) {
                      AppPage.to(Routes.teamPhotosPage, arguments: {
                        'teamId': logic.teamId.value,
                      }).then((onValue) {
                        BusUtils.instance.fire(
                            EventAction(key: EventBusKey.updateTeamPhoto));
                      });
                    }
                  },
          ),
          Padding(
            padding: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 55.w),
            child: (logic.teamHomeModel.value.teamPhoto ?? "") != ""
                ? Column(
                    children: List.generate(
                        logic.teamHomeModel.value.teamPhotoList?.length ?? 0,
                        (index) {
                      // /审核状态:，1-审核通过，2-审核拒绝 3-待审核
                      return logic.teamHomeModel.value.teamPhotoList?[index]
                                      ?.auditStatus !=
                                  1 &&
                              (logic.teamHomeModel.value.leader != true)
                          ? const SizedBox()
                          : GestureDetector(
                              onTap: () {
                                // 获取所有 URL
                                Get.to(
                                  PhotoView(
                                    images: logic.allUrls ?? [],
                                    index: index,
                                    flag: 0,
                                  ),
                                );
                              },
                              child: Container(
                                margin: EdgeInsets.only(bottom: 10.w),
                                child: Stack(
                                  children: [
                                    MyImage(
                                      (logic.teamHomeModel.value
                                              .teamPhotoList?[index]?.url ??
                                          ""),
                                      width: double.infinity,
                                      height: 194.w,
                                      radius: 8.r,
                                      fit: BoxFit.fill,
                                      errorImage: "error_image_width.png",
                                      placeholderImage: "error_image_width.png",
                                    ),
                                    if (logic
                                                .teamHomeModel
                                                .value
                                                .teamPhotoList?[index]
                                                ?.auditStatus ==
                                            2 ||
                                        logic
                                                .teamHomeModel
                                                .value
                                                .teamPhotoList?[index]
                                                ?.auditStatus ==
                                            3)
                                      // /审核状态:，1-审核通过，2-审核拒绝 3-待审核
                                      Positioned(
                                          bottom: 0,
                                          child: Container(
                                            width: 345.w,
                                            height: 40.w,
                                            alignment: Alignment.center,
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 8.w, vertical: 4.w),
                                            decoration: BoxDecoration(
                                                color: Color(0x50000000),
                                                borderRadius: BorderRadius.only(
                                                    bottomLeft:
                                                        Radius.circular(8.r),
                                                    bottomRight:
                                                        Radius.circular(8.r))),
                                            child: Text(
                                              (logic
                                                              .teamHomeModel
                                                              .value
                                                              .teamPhotoList?[
                                                                  index]
                                                              ?.auditStatus ??
                                                          0) ==
                                                      2
                                                  ? "审核拒绝"
                                                  : "审核中",
                                              style: TextStyles.regular
                                                  .copyWith(
                                                      color: Colours.white,
                                                      fontSize: 14.sp),
                                            ),
                                          ))
                                  ],
                                ),
                              ),
                            );
                    }),
                  )
                : GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      if (logic.teamHomeModel.value.leader == true) {
                        logic.showDateDialog(context);
                      }
                    },
                    child: WxAssets.images.teamInfoNophoto.image(
                        width: double.infinity.w,
                        height: 194.w,
                        fit: BoxFit.fill),
                  ),
          ),
        ],
      ),
    );
  }

  showBottoPlayerRankDialog(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
              width: double.infinity,
              height: 682,
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              decoration: BoxDecoration(
                  color: Colours.color191921, //Colours.color191921
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.r),
                      topRight: Radius.circular(12.r))),
              child: Column(
                children: [
                  SizedBox(
                    height: 8.w,
                  ),
                  Container(
                    width: 38.w,
                    height: 4.w,
                    decoration: BoxDecoration(
                        color: Colours.colorD8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                  SizedBox(
                    height: 18.w,
                  ),
                  Text(
                    logic.teamRankTitle[logic.teamRankIndex.value],
                    style: TextStyles.regular.copyWith(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colours.white),
                    maxLines: 1,
                  ),
                  SizedBox(
                    height: 10.w,
                  ),
                  Text(
                    S.current.player_rank_remake,
                    style: TextStyles.regular
                        .copyWith(fontSize: 14.sp, color: Colours.color5C5C6E),
                    maxLines: 1,
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  logic.teamRankIndex.value == 0
                      ? Expanded(
                          child: SmartRefresher(
                              controller: logic.refreshController1,
                              footer: buildFooter(),
                              header: buildClassicHeader(),
                              enablePullDown: false,
                              enablePullUp: true,
                              // onRefresh: () {
                              //   WxLoading.showToast("onRefresh");
                              //   logic.refreshController1.refreshCompleted();
                              // },
                              onLoading: () {
                                // WxLoading.showToast("onLoading");
                                logic.getTeamMembers(
                                    logic.teamRankIndex.value + 1,
                                    isLoad: true);
                              },
                              physics: const AlwaysScrollableScrollPhysics(),
                              child: (logic.dataFag["isFrist1"] as bool)
                                  ? buildLoad(isShowGif: false)
                                  : logic.teamMemberList1.isEmpty
                                      ? myNoDataView(context,
                                          msg: S.current.no_data_team_rank,
                                          imagewidget: WxAssets
                                              .images.teamInfoNodata
                                              .image(
                                                  width: 107.w, height: 72.w),
                                          height: 2.w)
                                      : ListView.separated(
                                          scrollDirection: Axis.vertical,
                                          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                          shrinkWrap: true,
                                          padding:
                                              EdgeInsets.only(bottom: 40.w),
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount:
                                              logic.teamMemberList1.length,
                                          itemBuilder: (context, position) {
                                            return _listItemWidget(
                                                logic.teamMemberList1[position],
                                                position);
                                          },
                                          separatorBuilder:
                                              (BuildContext context,
                                                  int index) {
                                            return SizedBox(
                                              height: 15.w,
                                            );
                                          },
                                        )),
                        )
                      : logic.teamRankIndex.value == 1
                          ? Expanded(
                              child: SmartRefresher(
                                  controller: logic.refreshController2,
                                  footer: buildFooter(),
                                  header: buildClassicHeader(),
                                  enablePullDown: false,
                                  enablePullUp: true,
                                  onLoading: () {
                                    logic.getTeamMembers(
                                        logic.teamRankIndex.value + 1,
                                        isLoad: true);
                                  },
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                  child: (logic.dataFag["isFrist2"] as bool)
                                      ? buildLoad(isShowGif: false)
                                      : logic.teamMemberList2.isEmpty
                                          ? myNoDataView(context,
                                              msg: S.current.no_data_team_rank,
                                              imagewidget: WxAssets
                                                  .images.teamInfoNodata
                                                  .image(
                                                      width: 107.w,
                                                      height: 72.w),
                                              height: 2.w)
                                          : ListView.separated(
                                              scrollDirection: Axis.vertical,
                                              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                              shrinkWrap: true,
                                              padding:
                                                  EdgeInsets.only(bottom: 40.w),
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              itemCount:
                                                  logic.teamMemberList2.length,
                                              itemBuilder: (context, position) {
                                                return _listItemWidget(
                                                    logic.teamMemberList2[
                                                        position],
                                                    position);
                                              },
                                              separatorBuilder:
                                                  (BuildContext context,
                                                      int index) {
                                                return SizedBox(
                                                  height: 15.w,
                                                );
                                              },
                                            )),
                            )
                          : Expanded(
                              child: SmartRefresher(
                                  controller: logic.refreshController3,
                                  footer: buildFooter(),
                                  header: buildClassicHeader(),
                                  enablePullDown: false,
                                  enablePullUp: true,
                                  onLoading: () {
                                    logic.getTeamMembers(
                                        logic.teamRankIndex.value + 1,
                                        isLoad: true);
                                  },
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                  child: (logic.dataFag["isFrist3"] as bool)
                                      ? buildLoad(isShowGif: false)
                                      : logic.teamMemberList3.isEmpty
                                          ? myNoDataView(context,
                                              msg: S.current.no_data_team_rank,
                                              imagewidget: WxAssets
                                                  .images.teamInfoNodata
                                                  .image(
                                                      width: 107.w,
                                                      height: 72.w),
                                              height: 2.w)
                                          : ListView.separated(
                                              scrollDirection: Axis.vertical,
                                              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                              shrinkWrap: true,
                                              padding:
                                                  EdgeInsets.only(bottom: 40.w),
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              itemCount:
                                                  logic.teamMemberList3.length,
                                              itemBuilder: (context, position) {
                                                return _listItemWidget(
                                                    logic.teamMemberList3[
                                                        position],
                                                    position);
                                              },
                                              separatorBuilder:
                                                  (BuildContext context,
                                                      int index) {
                                                return SizedBox(
                                                  height: 15.w,
                                                );
                                              },
                                            )),
                            ),
                ],
              ));
        });
      },
    );
  }

  _listItemWidget(TeamPlayerRankModel teamPlayerRankModel, int index) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        AppPage.to(Routes.careerHighlightsHomePage,
            arguments: {'userId': logic.teamMemberList1[index].userId});
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(15.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(16.r)),
            color: Colours.color22222D),
        child: Row(
          children: [
            Text(
              "${index + 1}",
              style: TextStyles.regular.copyWith(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w700,
                  color: Colours.white),
              maxLines: 1,
            ),
            SizedBox(
              width: 15.w,
            ),
            MyImage(
              teamPlayerRankModel.avatar ?? "",
              width: 64.w,
              height: 64.w,
              radius: 8.r,
              errorImage: "error_image_width.png",
              placeholderImage: "error_image_width.png",
            ),
            SizedBox(
              width: 12.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    (logic.teamRankIndex.value == 0
                            ? logic.teamMemberList1[index].userName
                            : logic.teamRankIndex.value == 1
                                ? logic.teamMemberList2[index].userName
                                : logic.teamMemberList3[index].userName) ??
                        "",
                    style: TextStyles.regular.copyWith(fontSize: 14.sp),
                    maxLines: 1,
                  ),
                  SizedBox(
                    height: 16.w,
                  ),
                  Text(
                    S.current
                        .bind_match_num(teamPlayerRankModel.bindMatchNum ?? 0),
                    style: TextStyles.regular.copyWith(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w700,
                        color: Colours.color5C5C6E),
                    maxLines: 1,
                  )
                ],
              ),
            ),
            Text(
              "${teamPlayerRankModel.score ?? ""}",
              style: TextStyles.regular
                  .copyWith(fontSize: 20.sp, fontWeight: FontWeight.w700),
              maxLines: 1,
            )
          ],
        ),
      ),
    );
  }
}
