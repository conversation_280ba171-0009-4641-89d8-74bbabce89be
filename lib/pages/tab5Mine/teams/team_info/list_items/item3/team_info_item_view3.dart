import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/widgets/match_item_widget.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item3/team_info_item_logic3.dart';
import 'package:shoot_z/widgets/view.dart';

///我的球队 球馆主页->赛事列表
class TeamInfoItemPage3 extends StatelessWidget {
  TeamInfoItemPage3({super.key});

  final logic = Get.put(TeamInfoItemLogic3());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _listWidget1(context),
    );
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: logic.dataList.isNotEmpty,
        onRefresh: () {
          logic.getdataList(isLoad: false, controller: logic.refreshController);
        },
        onLoading: () {
          logic.getdataList(controller: logic.refreshController);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? SizedBox(
                    height: 300.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.No_data_available,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 150.w, height: 150.w),
                    ))
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding:
                        EdgeInsets.only(bottom: 40.w, left: 15.w, right: 15.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return MatchItemWidget.fromMatchesModel(
                        model: logic.dataList[position],
                        showStatus: true,
                      );
                    }),
      );
    });
  }
}
