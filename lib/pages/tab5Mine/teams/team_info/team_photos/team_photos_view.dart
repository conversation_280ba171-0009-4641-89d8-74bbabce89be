import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/team_photos/team_photos_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/PhotoView.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///球队合照
class TeamPhotosPage extends StatelessWidget {
  TeamPhotosPage({super.key});
  final logic = Get.put(TeamPhotosLogic());
  @override
  Widget build(BuildContext context) {
    return KeepAliveWidget(
      child: Scaffold(
          backgroundColor: Colours.color0F0F16,
          appBar: MyAppBar(
            title: Text(S.current.Team_photo),
            actions: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  if (logic.isEdit.value != 1) {
                    for (int i = 0; i < logic.dataList.length; i++) {
                      logic.dataList[i].ischeck = false;
                    }
                    logic.isEdit.value = 1;
                    logic.isEdit.refresh();
                  } else {
                    logic.isEdit.value = 0;
                    logic.isEdit.refresh();
                  }
                },
                child: Container(
                    height: 40.w,
                    width: 50.w,
                    padding: EdgeInsets.only(right: 15.w),
                    alignment: Alignment.centerRight,
                    child: WxAssets.images.icDelete
                        .image(width: 20.w, height: 20.w)),
              )
            ],
          ),
          body: _listWidget(context),
          bottomNavigationBar: Obx(() {
            return ((logic.dataFag["isFrist"] as bool) ||
                    logic.dataList.isEmpty)
                ? const SizedBox()
                : logic.isEdit.value != 0
                    ? Container(
                        width: double.infinity,
                        height: 87.w,
                        alignment: Alignment.bottomCenter,
                        padding: EdgeInsets.only(
                            bottom: 25.w, right: 15.w, left: 15.w, top: 10.w),
                        child: Row(
                          children: [
                            Expanded(
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  logic.isEdit.value = 0;
                                },
                                child: Container(
                                  height: 50.w,
                                  width: double.infinity,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      border: Border.all(
                                          width: 1.w, color: Colours.white)),
                                  child: Text(
                                    S.current.cancel,
                                    style: TextStyles.regular
                                        .copyWith(fontSize: 14.sp),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 15.w,
                            ),
                            Expanded(
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  //1删除 2设为封面
                                  List<int?> ids = [];

                                  for (int i = 0;
                                      i < logic.dataList.length;
                                      i++) {
                                    if (logic.dataList[i].ischeck ?? false) {
                                      ids.add(logic.dataList[i].id);
                                    }
                                  }
                                  if (ids.isEmpty) {
                                    WxLoading.showToast(logic.isEdit.value == 1
                                        ? "请选择要删除的合照"
                                        : "请选择设为封面的合照");
                                    return;
                                  }
                                  if (logic.isEdit.value == 1) {
                                    getMyDialog(
                                      S.current.delete_team_photos,
                                      S.current.sure,
                                      content: S.current
                                          .delete_team_photos_tips(ids.length),
                                      () {
                                        AppPage.back();
                                        logic.isEdit.value = 0;
                                        logic.deleteTeamPhotos(ids);
                                      },
                                      isShowClose: false,
                                      btnIsHorizontal: true,
                                      btnText2: S.current.cancel,
                                      onPressed2: () {
                                        AppPage.back();
                                      },
                                    );
                                  } else if (logic.isEdit.value == 2) {
                                    if (ids.length != 1) {
                                      WxLoading.showToast("只能选择一张合照设为封面");
                                      return;
                                    }
                                    logic.isEdit.value = 0;
                                    logic.postTeamPhotoCover(ids.first);
                                    // getMyDialog(
                                    //   S.current.set_team_cover,
                                    //   S.current.sure,
                                    //   content: S.current.set_team_cover_tips,
                                    //   () {
                                    //     AppPage.back();
                                    //     logic.isEdit.value = 0;
                                    //     logic.postTeamPhotoCover(ids.first);
                                    //   },
                                    //   isShowClose: false,
                                    //   btnIsHorizontal: true,
                                    //   btnText2: S.current.cancel,
                                    //   onPressed2: () {
                                    //     AppPage.back();
                                    //   },
                                    //);
                                  }
                                },
                                child: Container(
                                  height: 50.w,
                                  width: double.infinity,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: Colours.color282735,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(28.r)),
                                    gradient: const LinearGradient(
                                      colors: [
                                        Colours.color7732ED,
                                        Colours.colorA555EF
                                      ],
                                      begin: Alignment.bottomLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                  ),
                                  child: Text(
                                    S.current.sure,
                                    style: TextStyles.regular
                                        .copyWith(fontSize: 14.sp),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : Container(
                        width: double.infinity,
                        height: 87.w,
                        alignment: Alignment.bottomCenter,
                        padding: EdgeInsets.only(
                            bottom: 25.w, right: 15.w, left: 15.w, top: 10.w),
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            logic.isEdit.value = 2;
                            for (int i = 0; i < logic.dataList.length; i++) {
                              logic.dataList[i].ischeck = false;
                            }
                          },
                          child: Container(
                            height: 50.w,
                            width: double.infinity,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Colours.color282735,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(28.r)),
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF
                                ],
                                begin: Alignment.bottomLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: Text(
                              S.current.set_cover,
                              style:
                                  TextStyles.regular.copyWith(fontSize: 14.sp),
                            ),
                          ),
                        ),
                      );
          })),
    );
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: false,
        onRefresh: () {
          logic.getdataList(isLoad: false, controller: logic.refreshController);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            :
            // logic.dataList.isEmpty
            //     ? myNoDataView(context,
            //         height: 2, margin: EdgeInsets.only(bottom: 140.w))
            //     : // 分组内容my_team_add
            GridView.builder(
                shrinkWrap: true, //GridView 的大小会根据其内容动态调整，只占用内容所需的空间。
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2, // 每行两个 item
                  crossAxisSpacing: 15.w,
                  mainAxisSpacing: 15.w,
                  childAspectRatio: 165 / 94, // 控制每个 item 的宽高比例
                ),
                itemCount: logic.dataList.length < 9
                    ? logic.dataList.length + 1
                    : logic.dataList.length,
                padding: EdgeInsets.only(
                    bottom: 30.w, left: 15.w, right: 15.w, top: 15.w),
                itemBuilder: (context, position) {
                  return Obx(() {
                    return logic.dataList.length < 9 &&
                            position == (logic.dataList.length)
                        ? GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () async {
                              logic.showDateDialog(context);
                            },
                            child: Container(
                                width: 165.w,
                                color: Colours.color191921,
                                height: 94.w,
                                child: WxAssets.images.myTeamAdd
                                    .image(width: 38.w, height: 38.w)),
                          )
                        : GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () async {
                              // /审核状态:，1-审核通过，2-审核拒绝 3-待审核
                              if (logic.isEdit.value == 1) {
                                //删除
                                if (logic.dataList[position].auditStatus != 3) {
                                  logic.dataList[position].ischeck =
                                      !(logic.dataList[position].ischeck ??
                                          false);
                                  logic.dataList.refresh();
                                }
                              } else if (logic.isEdit.value == 2) {
                                //设为封面
                                for (int i = 0;
                                    i < logic.dataList.length;
                                    i++) {
                                  if (logic.dataList[position].id ==
                                      logic.dataList[i].id) {
                                    if (logic.dataList[position].auditStatus ==
                                        1) {
                                      logic.dataList[i].ischeck =
                                          !(logic.dataList[i].ischeck ?? false);
                                    }
                                  } else {
                                    logic.dataList[i].ischeck = false;
                                  }
                                }

                                logic.dataList.refresh();
                              } else {
                                Get.to(
                                  PhotoView(
                                    images: logic.allUrls,
                                    index: position,
                                    flag: 1,
                                  ),
                                );
                              }
                            },
                            child: Container(
                              width: 165.w,
                              height: 94.w,
                              child: Stack(
                                children: [
                                  MyImage(
                                    logic.dataList[position].url ?? '',
                                    fit: BoxFit.fill,
                                    width: double.infinity,
                                    height: 94.w,
                                    radius: 8.r,
                                    bgColor: Colours.color000000,
                                    errorImage: "error_img_white.png",
                                    placeholderImage: "error_img_white.png",
                                  ),
                                  if (logic.dataList[position].isCover == 1)
                                    Positioned(
                                        child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 8.w, vertical: 4.w),
                                      decoration: BoxDecoration(
                                          gradient: const LinearGradient(
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            colors: [
                                              Colours.colorFFECC1,
                                              Colours.colorE7CEFF,
                                              Colours.colorD1EAFF,
                                            ],
                                          ),
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(8.r),
                                              bottomRight:
                                                  Radius.circular(8.r))),
                                      child: Text(
                                        "封面",
                                        style: TextStyles.regular.copyWith(
                                            color: Colours.color000000,
                                            fontSize: 10.sp),
                                      ),
                                    )),
                                  if (logic.isEdit.value != 0 &&
                                      ((logic.isEdit.value == 1 &&
                                              (logic.dataList[position]
                                                      .auditStatus !=
                                                  3)) ||
                                          (logic.isEdit.value == 2 &&
                                              (logic.dataList[position]
                                                      .auditStatus ==
                                                  1))))
                                    Positioned(
                                        right: 8.w,
                                        top: 8.w,
                                        child: (logic.dataList[position]
                                                    .ischeck ??
                                                false)
                                            ? WxAssets.images.checkOn3.image(
                                                width: 16.w, height: 16.w)
                                            : WxAssets.images.checkOn3Wihte
                                                .image(
                                                    width: 16.w, height: 16.w)),
                                  if (logic.dataList[position].auditStatus ==
                                          2 ||
                                      logic.dataList[position].auditStatus == 3)
                                    // /审核状态:，1-审核通过，2-审核拒绝 3-待审核
                                    Positioned(
                                        bottom: 0,
                                        child: Container(
                                          width: 165.w,
                                          height: 24.w,
                                          alignment: Alignment.center,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 8.w, vertical: 4.w),
                                          decoration: BoxDecoration(
                                              color: Color(0x50000000),
                                              borderRadius: BorderRadius.only(
                                                  bottomLeft:
                                                      Radius.circular(8.r),
                                                  bottomRight:
                                                      Radius.circular(8.r))),
                                          child: Text(
                                            logic.dataList[position]
                                                        .auditStatus ==
                                                    2
                                                ? "审核拒绝"
                                                : "审核中",
                                            style: TextStyles.regular.copyWith(
                                                color: Colours.white,
                                                fontSize: 12.sp),
                                          ),
                                        ))
                                ],
                              ),
                            ),
                          );
                  });
                },
              ),
      );
    });
  }
}
