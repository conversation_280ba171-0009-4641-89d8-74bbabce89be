import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/widgets/permission_dialog.dart';
import 'package:ui_packages/ui_packages.dart';
import 'logic.dart';

class TabPage extends StatefulWidget {
  const TabPage({Key? key}) : super(key: key);

  @override
  State<TabPage> createState() => _TabPageState();
}

class _TabPageState extends State<TabPage> with AutomaticKeepAliveClientMixin {
  final logic = Get.find<TabLogic>();
  final state = Get.find<TabLogic>().state;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      logic.subscribe(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return PopScope(
      canPop: false,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        bottomNavigationBar: Stack(
          clipBehavior: Clip.none,
          children: [
            // Positioned(
            //   top: -18,
            //   left: MediaQuery.of(context).size.width / 2 - 87 / 2, // 居中定位
            //   child: WxAssets.images.tabBg.image(width: 87, height: 18),
            // ),
            Obx(
              () => BottomNavigationBar(
                type: BottomNavigationBarType.fixed,
                currentIndex: state.currentIndex.value,
                onTap: (index) {
                  logic.barOnTap(index);
                },
                selectedItemColor: Colors.white,
                selectedFontSize: 10.sp,
                unselectedFontSize: 10.sp,
                unselectedItemColor: Colours.color767681,
                backgroundColor: Colours.color0E1017,
                items: state.tabbar,
              ),
            ),
            //中间按钮
            Positioned(
              top: 4.w, // 控制浮动按钮的突出高度
              left: MediaQuery.of(context).size.width / 2 - 22.w, // 居中定位
              child: GestureDetector(
                onTap: () async {
                  // await UserManager.instance.pullUserInfo().then((v) async {
                  //   await Future.delayed(const Duration(milliseconds: 200));
                  //   if ((UserManager.instance.userInfo.value?.betaUser ??
                  //       false)) {
                  //     WxLoading.showToast(S.current.Please_open_vip_first);
                  //   } else {}
                  // });
                  if (UserManager.instance.isLogin) {
                    if ((UserManager.instance.userInfo.value?.betaUser ??
                        false)) {
                      AppPage.to(Routes.creationWayPage);
                    } else {
                      if (!LocationUtils.instance.havePermission.value) {
                        showLocationDialog();
                        return;
                      }
                      AppPage.to(Routes.place);
                    }
                  } else {
                    AppPage.to(Routes.login).then((onValue) async {
                      log("milliseconds2=1");
                      await Future.delayed(const Duration(milliseconds: 500));
                      if (UserManager.instance.isLogin) {
                      } else {
                        if ((UserManager.instance.userInfo.value?.betaUser ??
                            false)) {
                          log("milliseconds2=2");
                          AppPage.to(Routes.creationWayPage);
                        } else {
                          if (!LocationUtils.instance.havePermission.value) {
                            showLocationDialog();
                            return;
                          }
                          log("milliseconds2=3");
                          if (UserManager.instance.isLogin) {
                            AppPage.to(Routes.place);
                          }
                        }
                      }
                    });
                  }
                },
                child: WxAssets.images.tabImg2.image(width: 44.w, height: 44.w),
              ),
            ),
          ],
        ),
        body: PageView(
          physics: const NeverScrollableScrollPhysics(),
          controller: state.pageController,
          children: state.pages,
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
