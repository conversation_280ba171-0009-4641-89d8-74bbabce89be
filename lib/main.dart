import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:ui';
import 'package:app_links/app_links.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/generated/l10n.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/utils/pay/fluwx_utils.dart';
import 'package:shoot_z/utils/pay/pay_utils.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:ui_packages/generated/l10n.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart' as ccc;
import 'package:utils_package/utils_package.dart';
import 'generated/l10n.dart';
import 'network/config.dart';
import 'network/interceptor.dart';
import 'pages/login/user.dart';
import 'routes/page.dart';
import 'routes/route.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/gestures.dart';

const platform = MethodChannel('com.shootz.orientation');
const proxyKey = 'PROXY';
// 使用全局变量或服务类管理 AppLinks 实例
var appLinks; // 立即初始化
// 设置横屏
Future<void> _setLandscape() async {
  try {
    await platform.invokeMethod('setLandscape');
  } on PlatformException catch (e) {
    print("Failed to change orientation: '${e.message}'.");
  }
}

Future<void> initDeepLinks() async {
  appLinks = AppLinks(); // 立即初始化
  // 获取初始链接（应用未启动时点击）
  final Uri? initialUri = await appLinks.getInitialLink();
  if (initialUri != null) {
    log('initDeepLinks初始链接: $initialUri');
    handleDeepLink(initialUri);
  }

  // 监听后续链接（应用已运行时点击）
  appLinks.uriLinkStream.listen((Uri? uri) {
    if (uri != null) {
      log('initDeepLinks 后续链接: $uri');
      handleDeepLink(uri);
    }
  });
}

void handleDeepLink(Uri uri) {
  // 解析所有查询参数
  final params = uri.queryParameters;

  log('initDeepLinks === 深度链接参数 ===');
  log('initDeepLinks 完整链接: $uri');
  log('initDeepLinks Scheme: ${uri.scheme}');
  log('initDeepLinks Host: ${uri.host}');
  log('initDeepLinks Path: ${uri.path}');
  log('initDeepLinks 参数:');
  params.forEach((key, value) => log('initDeepLinks  $key: $value'));

  // 处理特定参数
  final source = params['source'] ?? '';
  final campaign = params['campaign'] ?? '';
  final medium = params['medium'] ?? '';
  final term = params['term'] ?? '';
  final content = params['content'] ?? '';

  UserManager.instance.source.value = source; // 来源标识 applinks
  UserManager.instance.campaign.value = campaign; // 活动标识
  UserManager.instance.medium.value = medium; // 媒介类型
  UserManager.instance.term.value = term; // 关键词
  UserManager.instance.content.value = content; // 内容标识
  UserManager.instance.postChannelSubmit(0);
}

Future<void> main() async {
  // // 显式释放GPU资源
  // void _releaseGpuResources() {
  //   // 适用于Android的GPU资源释放
  //   if (Platform.isAndroid) {
  //     try {
  //       const channel = MethodChannel('performance_info');
  //       channel.invokeMethod('releaseResources');
  //     } catch (e) {
  //       debugPrint('释放GPU资源失败: $e');
  //     }
  //   }
  // }
  debugPrintGestureArenaDiagnostics = true; // 开启手势检测日志
  //多窗口
  var views = PlatformDispatcher.instance.views.toList();
  if (views.isNotEmpty) {
    var first = views.first;
    if (first.physicalSize.isEmpty) {
      first.platformDispatcher.onMetricsChanged = () {
        if (first.physicalSize.isEmpty) return;
        first.platformDispatcher.onMetricsChanged = null;
        runMyApp();
      };
    } else {
      runMyApp();
    }
  }
}

final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

void runMyApp() async {
  // 全局捕获同步异常
  // FlutterError.onError = (FlutterErrorDetails details) {
  //   FlutterError.presentError(details);
  //   // 自定义处理，例如打印日志
  //   print('Caught FlutterError: ${details.exception}');
  // };
  // runZonedGuarded(() async{

  final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  // 设置图片缓存的最大大小为10MB（根据应用需求调整）
  // PaintingBinding.instance.imageCache.maximumSizeBytes = 10 << 20; // 10MB
  PaintingBinding.instance.imageCache.maximumSizeBytes = 200 << 20; // 200MB
  PaintingBinding.instance.imageCache.maximumSize = 100; // 100张图片
  final databasePath = await ccc.getApplicationDocumentsDirectory();
  //final appDatabase =
  await $FloorAppDatabase
      .databaseBuilder(p.join(databasePath.path, 'app_database.db'))
      .build();
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  if (GetPlatform.isIOS) _setLandscape();
  //启动图停留
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  await FluwxUtils.instance.register();
  if (const String.fromEnvironment('env', defaultValue: 'dev') != 'pro') {
    final proxy = await WxStorage.instance.getString(proxyKey);
    if (proxy?.isNotEmpty ?? false) {
      WxEnv.instance.httpProxy = 'PROXY $proxy';
    }
  }

  //网络请求拦截器，处理401
  Api().dio.interceptors.add(TokenInterceptor());
  //初始化
  await WxAppInfoUtils.instance.setup();
  await UserManager.instance.setup();
  await WxEnv.instance.setup();

  if (Platform.isIOS) {
    Get.config(
      enableLog: true,
      defaultTransition: Transition.cupertino, // 使用 iOS 更适配的动画
      defaultPopGesture: true, // 允许 iOS 手势返回
      defaultDurationTransition: const Duration(milliseconds: 200), // 调整动画速度
    );
  } else {
    final result = await WxStorage.instance.getBool('PrivacyDialog');
    if (result != null) {
      LocationUtils.instance.setup();
    } else {
      UserManager.instance.showPrivacyDialog.value = true;
    }
  }
  // 获取渠道信息
  final String channel =
      (Platform.isIOS) ? "appstore" : await Utils.getChannel();
  UserManager.instance.channel.value = channel; // 来源标识 applinks
  log("initChannelConfig=$channel");
  initDeepLinks();
//   渠道英文
// 抖音 dy
// 视频号sph
// 公众号gzh
// 小红书xhs
// B站 blbl
// 麓谷篮球场  lqc01
  runApp(
    ScreenUtilInit(
      //等比适配屏幕
      designSize: const Size(375, 812),
      builder: (BuildContext context, Widget? child) {
        if (GetPlatform.isAndroid) {
          LucaThemeUtils.setAndroidFullScreen();
        } else {
          LucaThemeUtils.setStatusBarLight();
        }
        return GetMaterialApp(
          title: '球秀',
          debugShowCheckedModeBanner: false,
          initialRoute:
              UserManager.instance.isLogin ? Routes.loading : Routes.login,
          unknownRoute: Pages.notfound,
          smartManagement: SmartManagement.full, // 智能依赖管理
          checkerboardRasterCacheImages: false,
          showPerformanceOverlay: false,
          defaultTransition: Transition.rightToLeft,
          getPages: Pages.pages(),
          routingCallback: (routing) {
            // 正确获取当前路由名称
            debugPrint('Current route: ${routing?.current}');
          },
          navigatorObservers: [routeObserver],
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
            CommonS.delegate,
            UiS.delegate,
            S.delegate,
          ],
          supportedLocales: S.delegate.supportedLocales,
          theme: ThemeData(
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
              scaffoldBackgroundColor: Colours.bg_color,
              primaryColor: Colours.app_main,
              appBarTheme: AppBarTheme(
                  scrolledUnderElevation:
                      0.0, //解决使用ListView 。滚动时 MyAppBar 改变颜色问题
                  backgroundColor: Colours.bg_color,
                  centerTitle: true,
                  shadowColor: Colors.transparent,
                  iconTheme: const IconThemeData(
                    color: Colors.white, // 设置返回箭头的颜色
                    size: 24, // 设置返回箭头的大小
                    weight: 400,
                  ),
                  // foregroundColor: Colors.white,//导航栏文字颜色
                  systemOverlayStyle: SystemUiOverlayStyle.light.copyWith(
                    //如果使用 MyAppBar，需要通过 MyAppBarTheme.systemOverlayStyle 或直接设置 MyAppBar.systemOverlayStyle 来控制状态栏样式
                    statusBarColor: Colors.transparent,
                    statusBarBrightness: Brightness.dark,
                    // iOS 浅色图标 在 iOS 上，状态栏的图标颜色是通过 背景亮度（statusBarBrightness） 决定的
                    statusBarIconBrightness:
                        Brightness.light, // Android 状态栏浅色图标（白色）
                  ),
                  titleTextStyle: TextStyle(
                      //设置导航栏字体style
                      color: Colours.text,
                      fontSize: 16.sp,
                      fontWeight: AppFontWeight.medium())),
              textTheme: TextTheme(
                displaySmall: TextStyles.display12,
                displayMedium: TextStyles.display14,
                displayLarge: TextStyles.display16,
                titleMedium: TextStyles.titleMedium18,
              )),
          color: Colors.white,
          builder: (context, widget) {
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
              child: WxLoading.init()(context, widget),
            );
          },
          onInit: () {
            PayUtils.instance.listen();
            ConfigUtils.instance.setup();
          },
        );
      },
    ),
  );
  // }, (error, stackTrace) {
  //   // 自定义处理，例如上传日志
  //   print('Caught async error: $error');
  // });
}

// 在 main.dart 中设置
