/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// Directory path: assets/images/3.0x
  $AssetsImages30xGen get a3 => const $AssetsImages30xGen();

  /// File path: assets/images/Invitation_dialog1.png
  AssetGenImage get invitationDialog1 =>
      const AssetGenImage('assets/images/Invitation_dialog1.png');

  /// File path: assets/images/Invitation_dialog2.png
  AssetGenImage get invitationDialog2 =>
      const AssetGenImage('assets/images/Invitation_dialog2.png');

  /// File path: assets/images/Invitation_dialog3.png
  AssetGenImage get invitationDialog3 =>
      const AssetGenImage('assets/images/Invitation_dialog3.png');

  /// File path: assets/images/Invitation_dialog4.png
  AssetGenImage get invitationDialog4 =>
      const AssetGenImage('assets/images/Invitation_dialog4.png');

  /// File path: assets/images/Receive_vip.png
  AssetGenImage get receiveVip =>
      const AssetGenImage('assets/images/Receive_vip.png');

  /// File path: assets/images/address_check.png
  AssetGenImage get addressCheck =>
      const AssetGenImage('assets/images/address_check.png');

  /// File path: assets/images/address_delete.png
  AssetGenImage get addressDelete =>
      const AssetGenImage('assets/images/address_delete.png');

  /// File path: assets/images/address_edit.png
  AssetGenImage get addressEdit =>
      const AssetGenImage('assets/images/address_edit.png');

  /// File path: assets/images/address_location.png
  AssetGenImage get addressLocation =>
      const AssetGenImage('assets/images/address_location.png');

  /// File path: assets/images/address_nodata.png
  AssetGenImage get addressNodata =>
      const AssetGenImage('assets/images/address_nodata.png');

  /// File path: assets/images/address_ok.png
  AssetGenImage get addressOk =>
      const AssetGenImage('assets/images/address_ok.png');

  /// File path: assets/images/address_uncheck.png
  AssetGenImage get addressUncheck =>
      const AssetGenImage('assets/images/address_uncheck.png');

  /// File path: assets/images/advanced_arena.png
  AssetGenImage get advancedArena =>
      const AssetGenImage('assets/images/advanced_arena.png');

  /// File path: assets/images/lamp_icon.png
  AssetGenImage get lampIcon =>
      const AssetGenImage('assets/images/lamp_icon.png');

  /// File path: assets/images/ai.png
  AssetGenImage get ai => const AssetGenImage('assets/images/ai.png');

  /// File path: assets/images/ai2.png
  AssetGenImage get ai2 => const AssetGenImage('assets/images/ai2.png');

  /// File path: assets/images/ai3.png
  AssetGenImage get ai3 => const AssetGenImage('assets/images/ai3.png');

  /// File path: assets/images/ai4.png
  AssetGenImage get ai4 => const AssetGenImage('assets/images/ai4.png');

  /// File path: assets/images/aiReport_empty.png
  AssetGenImage get aiReportEmpty =>
      const AssetGenImage('assets/images/aiReport_empty.png');

  /// File path: assets/images/ai_bg.png
  AssetGenImage get aiBg => const AssetGenImage('assets/images/ai_bg.png');

  /// File path: assets/images/ai_img1.png
  AssetGenImage get aiImg1 => const AssetGenImage('assets/images/ai_img1.png');

  /// File path: assets/images/ai_report_icon1.png
  AssetGenImage get aiReportIcon1 =>
      const AssetGenImage('assets/images/ai_report_icon1.png');

  /// File path: assets/images/ai_report_icon2.png
  AssetGenImage get aiReportIcon2 =>
      const AssetGenImage('assets/images/ai_report_icon2.png');

  /// File path: assets/images/ai_report_icon3.png
  AssetGenImage get aiReportIcon3 =>
      const AssetGenImage('assets/images/ai_report_icon3.png');

  /// File path: assets/images/ai_report_icon4.png
  AssetGenImage get aiReportIcon4 =>
      const AssetGenImage('assets/images/ai_report_icon4.png');

  /// File path: assets/images/album_empty.png
  AssetGenImage get albumEmpty =>
      const AssetGenImage('assets/images/album_empty.png');

  /// File path: assets/images/apple_logo.png
  AssetGenImage get appleLogo =>
      const AssetGenImage('assets/images/apple_logo.png');

  /// File path: assets/images/arrow_back_round.png
  AssetGenImage get arrowBackRound =>
      const AssetGenImage('assets/images/arrow_back_round.png');

  /// File path: assets/images/arrow_down.png
  AssetGenImage get arrowDown =>
      const AssetGenImage('assets/images/arrow_down.png');

  /// File path: assets/images/arrow_left.png
  AssetGenImage get arrowLeft =>
      const AssetGenImage('assets/images/arrow_left.png');

  /// File path: assets/images/arrow_right.png
  AssetGenImage get arrowRight =>
      const AssetGenImage('assets/images/arrow_right.png');

  /// File path: assets/images/battle_empty_icon.png
  AssetGenImage get battleEmptyIcon =>
      const AssetGenImage('assets/images/battle_empty_icon.png');

  /// File path: assets/images/call_phone.png
  AssetGenImage get callPhone =>
      const AssetGenImage('assets/images/call_phone.png');

  /// File path: assets/images/career_bg.png
  AssetGenImage get careerBg =>
      const AssetGenImage('assets/images/career_bg.png');

  /// File path: assets/images/cart.png
  AssetGenImage get cart => const AssetGenImage('assets/images/cart.png');

  /// File path: assets/images/chart_point.png
  AssetGenImage get chartPoint =>
      const AssetGenImage('assets/images/chart_point.png');

  /// File path: assets/images/check.png
  AssetGenImage get check => const AssetGenImage('assets/images/check.png');

  /// File path: assets/images/check_on_3.png
  AssetGenImage get checkOn3 =>
      const AssetGenImage('assets/images/check_on_3.png');

  /// File path: assets/images/check_on_3_wihte.png
  AssetGenImage get checkOn3Wihte =>
      const AssetGenImage('assets/images/check_on_3_wihte.png');

  /// File path: assets/images/choiceNo.png
  AssetGenImage get choiceNo =>
      const AssetGenImage('assets/images/choiceNo.png');

  /// File path: assets/images/choiceYes.png
  AssetGenImage get choiceYes =>
      const AssetGenImage('assets/images/choiceYes.png');

  /// File path: assets/images/chooseYes2.png
  AssetGenImage get chooseYes2 =>
      const AssetGenImage('assets/images/chooseYes2.png');

  /// File path: assets/images/clock_failure.png
  AssetGenImage get clockFailure =>
      const AssetGenImage('assets/images/clock_failure.png');

  /// File path: assets/images/clock_successful.png
  AssetGenImage get clockSuccessful =>
      const AssetGenImage('assets/images/clock_successful.png');

  /// File path: assets/images/close2.png
  AssetGenImage get close2 => const AssetGenImage('assets/images/close2.png');

  /// File path: assets/images/composition_failure.png
  AssetGenImage get compositionFailure =>
      const AssetGenImage('assets/images/composition_failure.png');

  /// File path: assets/images/contribute_icon.png
  AssetGenImage get contributeIcon =>
      const AssetGenImage('assets/images/contribute_icon.png');

  /// File path: assets/images/coupons_bg.png
  AssetGenImage get couponsBg =>
      const AssetGenImage('assets/images/coupons_bg.png');

  /// File path: assets/images/coupons_bg2.png
  AssetGenImage get couponsBg2 =>
      const AssetGenImage('assets/images/coupons_bg2.png');

  /// File path: assets/images/coupons_guoqi.png
  AssetGenImage get couponsGuoqi =>
      const AssetGenImage('assets/images/coupons_guoqi.png');

  /// File path: assets/images/coupons_nodata.png
  AssetGenImage get couponsNodata =>
      const AssetGenImage('assets/images/coupons_nodata.png');

  /// File path: assets/images/coupons_time.png
  AssetGenImage get couponsTime =>
      const AssetGenImage('assets/images/coupons_time.png');

  /// File path: assets/images/coupons_used.png
  AssetGenImage get couponsUsed =>
      const AssetGenImage('assets/images/coupons_used.png');

  /// File path: assets/images/creation_way1.png
  AssetGenImage get creationWay1 =>
      const AssetGenImage('assets/images/creation_way1.png');

  /// File path: assets/images/creation_way2.png
  AssetGenImage get creationWay2 =>
      const AssetGenImage('assets/images/creation_way2.png');

  /// File path: assets/images/daka1.png
  AssetGenImage get daka1 => const AssetGenImage('assets/images/daka1.png');

  /// File path: assets/images/daka2.png
  AssetGenImage get daka2 => const AssetGenImage('assets/images/daka2.png');

  /// File path: assets/images/daka3.png
  AssetGenImage get daka3 => const AssetGenImage('assets/images/daka3.png');

  /// File path: assets/images/delete.png
  AssetGenImage get delete => const AssetGenImage('assets/images/delete.png');

  /// File path: assets/images/dialog_invitation.png
  AssetGenImage get dialogInvitation =>
      const AssetGenImage('assets/images/dialog_invitation.png');

  /// File path: assets/images/dialog_talk1.png
  AssetGenImage get dialogTalk1 =>
      const AssetGenImage('assets/images/dialog_talk1.png');

  /// File path: assets/images/dialog_team_info1.png
  AssetGenImage get dialogTeamInfo1 =>
      const AssetGenImage('assets/images/dialog_team_info1.png');

  /// File path: assets/images/dialog_team_info2.png
  AssetGenImage get dialogTeamInfo2 =>
      const AssetGenImage('assets/images/dialog_team_info2.png');

  /// File path: assets/images/dialog_team_info3.png
  AssetGenImage get dialogTeamInfo3 =>
      const AssetGenImage('assets/images/dialog_team_info3.png');

  /// File path: assets/images/dialog_team_info4.png
  AssetGenImage get dialogTeamInfo4 =>
      const AssetGenImage('assets/images/dialog_team_info4.png');

  /// File path: assets/images/dialog_team_info5.png
  AssetGenImage get dialogTeamInfo5 =>
      const AssetGenImage('assets/images/dialog_team_info5.png');

  /// File path: assets/images/dialoglocation.png
  AssetGenImage get dialoglocation =>
      const AssetGenImage('assets/images/dialoglocation.png');

  /// File path: assets/images/dialogvideo.png
  AssetGenImage get dialogvideo =>
      const AssetGenImage('assets/images/dialogvideo.png');

  /// File path: assets/images/document.png
  AssetGenImage get document =>
      const AssetGenImage('assets/images/document.png');

  /// File path: assets/images/document2.png
  AssetGenImage get document2 =>
      const AssetGenImage('assets/images/document2.png');

  /// File path: assets/images/edit_icon.png
  AssetGenImage get editIcon =>
      const AssetGenImage('assets/images/edit_icon.png');

  /// File path: assets/images/error_image.png
  AssetGenImage get errorImage =>
      const AssetGenImage('assets/images/error_image.png');

  /// File path: assets/images/error_image_width.png
  AssetGenImage get errorImageWidth =>
      const AssetGenImage('assets/images/error_image_width.png');

  /// File path: assets/images/error_img_white.png
  AssetGenImage get errorImgWhite =>
      const AssetGenImage('assets/images/error_img_white.png');

  /// File path: assets/images/event_reservation1.png
  AssetGenImage get eventReservation1 =>
      const AssetGenImage('assets/images/event_reservation1.png');

  /// File path: assets/images/event_reservation2.png
  AssetGenImage get eventReservation2 =>
      const AssetGenImage('assets/images/event_reservation2.png');

  /// File path: assets/images/event_reservation3.png
  AssetGenImage get eventReservation3 =>
      const AssetGenImage('assets/images/event_reservation3.png');

  /// File path: assets/images/event_reservation5.png
  AssetGenImage get eventReservation5 =>
      const AssetGenImage('assets/images/event_reservation5.png');

  /// File path: assets/images/event_reservation6.png
  AssetGenImage get eventReservation6 =>
      const AssetGenImage('assets/images/event_reservation6.png');

  /// File path: assets/images/event_reservation7.png
  AssetGenImage get eventReservation7 =>
      const AssetGenImage('assets/images/event_reservation7.png');

  /// File path: assets/images/feelback.png
  AssetGenImage get feelback =>
      const AssetGenImage('assets/images/feelback.png');

  /// File path: assets/images/fire.png
  AssetGenImage get fire => const AssetGenImage('assets/images/fire.png');

  /// File path: assets/images/five_stars_icon.png
  AssetGenImage get fiveStarsIcon =>
      const AssetGenImage('assets/images/five_stars_icon.png');

  /// File path: assets/images/four_stars_icon.png
  AssetGenImage get fourStarsIcon =>
      const AssetGenImage('assets/images/four_stars_icon.png');

  /// File path: assets/images/goal_add.png
  AssetGenImage get goalAdd =>
      const AssetGenImage('assets/images/goal_add.png');

  /// File path: assets/images/goal_ai.png
  AssetGenImage get goalAi => const AssetGenImage('assets/images/goal_ai.png');

  /// File path: assets/images/goal_ai_text.png
  AssetGenImage get goalAiText =>
      const AssetGenImage('assets/images/goal_ai_text.png');

  /// File path: assets/images/goal_bg.png
  AssetGenImage get goalBg => const AssetGenImage('assets/images/goal_bg.png');

  /// File path: assets/images/goal_bg1.png
  AssetGenImage get goalBg1 =>
      const AssetGenImage('assets/images/goal_bg1.png');

  /// File path: assets/images/goal_bg2.png
  AssetGenImage get goalBg2 =>
      const AssetGenImage('assets/images/goal_bg2.png');

  /// File path: assets/images/goal_help.png
  AssetGenImage get goalHelp =>
      const AssetGenImage('assets/images/goal_help.png');

  /// File path: assets/images/goal_help2.png
  AssetGenImage get goalHelp2 =>
      const AssetGenImage('assets/images/goal_help2.png');

  /// File path: assets/images/goal_remove.png
  AssetGenImage get goalRemove =>
      const AssetGenImage('assets/images/goal_remove.png');

  /// File path: assets/images/google_logo.png
  AssetGenImage get googleLogo =>
      const AssetGenImage('assets/images/google_logo.png');

  /// File path: assets/images/half_add.png
  AssetGenImage get halfAdd =>
      const AssetGenImage('assets/images/half_add.png');

  /// File path: assets/images/half_shoot1.png
  AssetGenImage get halfShoot1 =>
      const AssetGenImage('assets/images/half_shoot1.png');

  /// File path: assets/images/half_shoot2.png
  AssetGenImage get halfShoot2 =>
      const AssetGenImage('assets/images/half_shoot2.png');

  /// File path: assets/images/half_shoot3.png
  AssetGenImage get halfShoot3 =>
      const AssetGenImage('assets/images/half_shoot3.png');

  /// File path: assets/images/home_img_bg.png
  AssetGenImage get homeImgBg =>
      const AssetGenImage('assets/images/home_img_bg.png');

  /// File path: assets/images/home_img_bottom.png
  AssetGenImage get homeImgBottom =>
      const AssetGenImage('assets/images/home_img_bottom.png');

  /// File path: assets/images/home_img_bottom_long.png
  AssetGenImage get homeImgBottomLong =>
      const AssetGenImage('assets/images/home_img_bottom_long.png');

  /// File path: assets/images/home_message.png
  AssetGenImage get homeMessage =>
      const AssetGenImage('assets/images/home_message.png');

  /// File path: assets/images/home_title_bg.png
  AssetGenImage get homeTitleBg =>
      const AssetGenImage('assets/images/home_title_bg.png');

  /// File path: assets/images/home_top_bg.png
  AssetGenImage get homeTopBg =>
      const AssetGenImage('assets/images/home_top_bg.png');

  /// File path: assets/images/home_yue_zhan.png
  AssetGenImage get homeYueZhan =>
      const AssetGenImage('assets/images/home_yue_zhan.png');

  /// File path: assets/images/ic_Apr.png
  AssetGenImage get icApr => const AssetGenImage('assets/images/ic_Apr.png');

  /// File path: assets/images/ic_Aug.png
  AssetGenImage get icAug => const AssetGenImage('assets/images/ic_Aug.png');

  /// File path: assets/images/ic_Dec.png
  AssetGenImage get icDec => const AssetGenImage('assets/images/ic_Dec.png');

  /// File path: assets/images/ic_Feb.png
  AssetGenImage get icFeb => const AssetGenImage('assets/images/ic_Feb.png');

  /// File path: assets/images/ic_Jan.png
  AssetGenImage get icJan => const AssetGenImage('assets/images/ic_Jan.png');

  /// File path: assets/images/ic_Jul.png
  AssetGenImage get icJul => const AssetGenImage('assets/images/ic_Jul.png');

  /// File path: assets/images/ic_Jun.png
  AssetGenImage get icJun => const AssetGenImage('assets/images/ic_Jun.png');

  /// File path: assets/images/ic_Mar.png
  AssetGenImage get icMar => const AssetGenImage('assets/images/ic_Mar.png');

  /// File path: assets/images/ic_May.png
  AssetGenImage get icMay => const AssetGenImage('assets/images/ic_May.png');

  /// File path: assets/images/ic_Nov.png
  AssetGenImage get icNov => const AssetGenImage('assets/images/ic_Nov.png');

  /// File path: assets/images/ic_Oct.png
  AssetGenImage get icOct => const AssetGenImage('assets/images/ic_Oct.png');

  /// File path: assets/images/ic_Sep.png
  AssetGenImage get icSep => const AssetGenImage('assets/images/ic_Sep.png');

  /// File path: assets/images/ic_arrow_down.png
  AssetGenImage get icArrowDown =>
      const AssetGenImage('assets/images/ic_arrow_down.png');

  /// File path: assets/images/ic_arrow_right.png
  AssetGenImage get icArrowRight =>
      const AssetGenImage('assets/images/ic_arrow_right.png');

  /// File path: assets/images/ic_bs_no.png
  AssetGenImage get icBsNo => const AssetGenImage('assets/images/ic_bs_no.png');

  /// File path: assets/images/ic_bs_sel.png
  AssetGenImage get icBsSel =>
      const AssetGenImage('assets/images/ic_bs_sel.png');

  /// File path: assets/images/ic_camera.png
  AssetGenImage get icCamera =>
      const AssetGenImage('assets/images/ic_camera.png');

  /// File path: assets/images/ic_circle_bs.png
  AssetGenImage get icCircleBs =>
      const AssetGenImage('assets/images/ic_circle_bs.png');

  /// File path: assets/images/ic_city_arrow.png
  AssetGenImage get icCityArrow =>
      const AssetGenImage('assets/images/ic_city_arrow.png');

  /// File path: assets/images/ic_close.png
  AssetGenImage get icClose =>
      const AssetGenImage('assets/images/ic_close.png');

  /// File path: assets/images/ic_close2.png
  AssetGenImage get icClose2 =>
      const AssetGenImage('assets/images/ic_close2.png');

  /// File path: assets/images/ic_close3.png
  AssetGenImage get icClose3 =>
      const AssetGenImage('assets/images/ic_close3.png');

  /// File path: assets/images/ic_close_dialog.png
  AssetGenImage get icCloseDialog =>
      const AssetGenImage('assets/images/ic_close_dialog.png');

  /// File path: assets/images/ic_delete.png
  AssetGenImage get icDelete =>
      const AssetGenImage('assets/images/ic_delete.png');

  /// File path: assets/images/ic_dfx.png
  AssetGenImage get icDfx => const AssetGenImage('assets/images/ic_dfx.png');

  /// File path: assets/images/ic_download.png
  AssetGenImage get icDownload =>
      const AssetGenImage('assets/images/ic_download.png');

  /// File path: assets/images/ic_edit.png
  AssetGenImage get icEdit => const AssetGenImage('assets/images/ic_edit.png');

  /// File path: assets/images/ic_eidt_head.png
  AssetGenImage get icEidtHead =>
      const AssetGenImage('assets/images/ic_eidt_head.png');

  /// File path: assets/images/ic_fxz.png
  AssetGenImage get icFxz => const AssetGenImage('assets/images/ic_fxz.png');

  /// File path: assets/images/ic_game_lock.png
  AssetGenImage get icGameLock =>
      const AssetGenImage('assets/images/ic_game_lock.png');

  /// File path: assets/images/ic_game_no.png
  AssetGenImage get icGameNo =>
      const AssetGenImage('assets/images/ic_game_no.png');

  /// File path: assets/images/ic_game_unlock.png
  AssetGenImage get icGameUnlock =>
      const AssetGenImage('assets/images/ic_game_unlock.png');

  /// File path: assets/images/ic_game_vs.png
  AssetGenImage get icGameVs =>
      const AssetGenImage('assets/images/ic_game_vs.png');

  /// File path: assets/images/ic_gou.png
  AssetGenImage get icGou => const AssetGenImage('assets/images/ic_gou.png');

  /// File path: assets/images/ic_highlights_arrow.png
  AssetGenImage get icHighlightsArrow =>
      const AssetGenImage('assets/images/ic_highlights_arrow.png');

  /// File path: assets/images/ic_highlights_no.png
  AssetGenImage get icHighlightsNo =>
      const AssetGenImage('assets/images/ic_highlights_no.png');

  /// File path: assets/images/ic_hl_hcz.png
  AssetGenImage get icHlHcz =>
      const AssetGenImage('assets/images/ic_hl_hcz.png');

  /// File path: assets/images/ic_hl_item_bottom.png
  AssetGenImage get icHlItemBottom =>
      const AssetGenImage('assets/images/ic_hl_item_bottom.png');

  /// File path: assets/images/ic_hl_location.png
  AssetGenImage get icHlLocation =>
      const AssetGenImage('assets/images/ic_hl_location.png');

  /// File path: assets/images/ic_hl_new.png
  AssetGenImage get icHlNew =>
      const AssetGenImage('assets/images/ic_hl_new.png');

  /// File path: assets/images/ic_hl_pdz.png
  AssetGenImage get icHlPdz =>
      const AssetGenImage('assets/images/ic_hl_pdz.png');

  /// File path: assets/images/ic_hl_people.png
  AssetGenImage get icHlPeople =>
      const AssetGenImage('assets/images/ic_hl_people.png');

  /// File path: assets/images/ic_hl_wait.png
  AssetGenImage get icHlWait =>
      const AssetGenImage('assets/images/ic_hl_wait.png');

  /// File path: assets/images/ic_home_location_hint.png
  AssetGenImage get icHomeLocationHint =>
      const AssetGenImage('assets/images/ic_home_location_hint.png');

  /// File path: assets/images/ic_home_no.png
  AssetGenImage get icHomeNo =>
      const AssetGenImage('assets/images/ic_home_no.png');

  /// File path: assets/images/ic_home_sel.png
  AssetGenImage get icHomeSel =>
      const AssetGenImage('assets/images/ic_home_sel.png');

  /// File path: assets/images/ic_home_tj.png
  AssetGenImage get icHomeTj =>
      const AssetGenImage('assets/images/ic_home_tj.png');

  /// File path: assets/images/ic_invite_code_bg.png
  AssetGenImage get icInviteCodeBg =>
      const AssetGenImage('assets/images/ic_invite_code_bg.png');

  /// File path: assets/images/ic_invite_code_card.png
  AssetGenImage get icInviteCodeCard =>
      const AssetGenImage('assets/images/ic_invite_code_card.png');

  /// File path: assets/images/ic_invite_code_des.png
  AssetGenImage get icInviteCodeDes =>
      const AssetGenImage('assets/images/ic_invite_code_des.png');

  /// File path: assets/images/ic_invite_code_hint.png
  AssetGenImage get icInviteCodeHint =>
      const AssetGenImage('assets/images/ic_invite_code_hint.png');

  /// File path: assets/images/ic_invite_code_jf.png
  AssetGenImage get icInviteCodeJf =>
      const AssetGenImage('assets/images/ic_invite_code_jf.png');

  /// File path: assets/images/ic_invite_code_skip.png
  AssetGenImage get icInviteCodeSkip =>
      const AssetGenImage('assets/images/ic_invite_code_skip.png');

  /// File path: assets/images/ic_invite_code_title.png
  AssetGenImage get icInviteCodeTitle =>
      const AssetGenImage('assets/images/ic_invite_code_title.png');

  /// File path: assets/images/ic_invite_code_tm.png
  AssetGenImage get icInviteCodeTm =>
      const AssetGenImage('assets/images/ic_invite_code_tm.png');

  /// File path: assets/images/ic_invite_code_yqhy.png
  AssetGenImage get icInviteCodeYqhy =>
      const AssetGenImage('assets/images/ic_invite_code_yqhy.png');

  /// File path: assets/images/ic_kf_hint.png
  AssetGenImage get icKfHint =>
      const AssetGenImage('assets/images/ic_kf_hint.png');

  /// File path: assets/images/ic_kf_logo.png
  AssetGenImage get icKfLogo =>
      const AssetGenImage('assets/images/ic_kf_logo.png');

  /// File path: assets/images/ic_kf_wx.png
  AssetGenImage get icKfWx => const AssetGenImage('assets/images/ic_kf_wx.png');

  /// File path: assets/images/ic_location.png
  AssetGenImage get icLocation =>
      const AssetGenImage('assets/images/ic_location.png');

  /// File path: assets/images/match_live_icon.png
  AssetGenImage get matchLiveIcon =>
      const AssetGenImage('assets/images/match_live_icon.png');

  /// File path: assets/images/match_live_highlighting.png
  AssetGenImage get matchLiveHighlighting =>
      const AssetGenImage('assets/images/match_live_highlighting.png');

  /// File path: assets/images/data_report_icon.png
  AssetGenImage get dataReportIcon =>
      const AssetGenImage('assets/images/data_report_icon.png');

  /// File path: assets/images/data_report_highlighting.png
  AssetGenImage get dataReportHighlighting =>
      const AssetGenImage('assets/images/data_report_highlighting.png');

  /// File path: assets/images/highlighting_moments_highlighting.png
  AssetGenImage get highlightingMomentsHighlighting => const AssetGenImage(
      'assets/images/highlighting_moments_highlighting.png');

  /// File path: assets/images/highlighting_moments.png
  AssetGenImage get highlightingMoments =>
      const AssetGenImage('assets/images/highlighting_moments.png');

  /// File path: assets/images/ic_location_hint.png
  AssetGenImage get icLocationHint =>
      const AssetGenImage('assets/images/ic_location_hint.png');

  /// File path: assets/images/phone_icon.png
  AssetGenImage get phoneIcon =>
      const AssetGenImage('assets/images/phone_icon.png');

  /// File path: assets/images/location_icon.png
  AssetGenImage get locationIcon =>
      const AssetGenImage('assets/images/location_icon.png');

  /// File path: assets/images/ic_man.png
  AssetGenImage get icMan => const AssetGenImage('assets/images/ic_man.png');

  /// File path: assets/images/ic_mine_bg.png
  AssetGenImage get icMineBg =>
      const AssetGenImage('assets/images/ic_mine_bg.png');

  /// File path: assets/images/ic_mine_btn_bg.png
  AssetGenImage get icMineBtnBg =>
      const AssetGenImage('assets/images/ic_mine_btn_bg.png');

  /// File path: assets/images/ic_mine_jf.png
  AssetGenImage get icMineJf =>
      const AssetGenImage('assets/images/ic_mine_jf.png');

  /// File path: assets/images/ic_mine_kf.png
  AssetGenImage get icMineKf =>
      const AssetGenImage('assets/images/ic_mine_kf.png');

  /// File path: assets/images/ic_mine_no.png
  AssetGenImage get icMineNo =>
      const AssetGenImage('assets/images/ic_mine_no.png');

  /// File path: assets/images/ic_mine_order.png
  AssetGenImage get icMineOrder =>
      const AssetGenImage('assets/images/ic_mine_order.png');

  /// File path: assets/images/ic_mine_sel.png
  AssetGenImage get icMineSel =>
      const AssetGenImage('assets/images/ic_mine_sel.png');

  /// File path: assets/images/ic_mine_settings.png
  AssetGenImage get icMineSettings =>
      const AssetGenImage('assets/images/ic_mine_settings.png');

  /// File path: assets/images/ic_mine_tj.png
  AssetGenImage get icMineTj =>
      const AssetGenImage('assets/images/ic_mine_tj.png');

  /// File path: assets/images/ic_mine_vip.png
  AssetGenImage get icMineVip =>
      const AssetGenImage('assets/images/ic_mine_vip.png');

  /// File path: assets/images/ic_mine_vip_no.png
  AssetGenImage get icMineVipNo =>
      const AssetGenImage('assets/images/ic_mine_vip_no.png');

  /// File path: assets/images/ic_mine_wdl.png
  AssetGenImage get icMineWdl =>
      const AssetGenImage('assets/images/ic_mine_wdl.png');

  /// File path: assets/images/ic_more_all.png
  AssetGenImage get icMoreAll =>
      const AssetGenImage('assets/images/ic_more_all.png');

  /// File path: assets/images/ic_my_order.png
  AssetGenImage get icMyOrder =>
      const AssetGenImage('assets/images/ic_my_order.png');

  /// File path: assets/images/ic_my_team.png
  AssetGenImage get icMyTeam =>
      const AssetGenImage('assets/images/ic_my_team.png');

  /// File path: assets/images/ic_photo_permission.png
  AssetGenImage get icPhotoPermission =>
      const AssetGenImage('assets/images/ic_photo_permission.png');

  /// File path: assets/images/ic_place_search.png
  AssetGenImage get icPlaceSearch =>
      const AssetGenImage('assets/images/ic_place_search.png');

  /// File path: assets/images/ic_question.png
  AssetGenImage get icQuestion =>
      const AssetGenImage('assets/images/ic_question.png');

  /// File path: assets/images/ic_ranking_1.png
  AssetGenImage get icRanking1 =>
      const AssetGenImage('assets/images/ic_ranking_1.png');

  /// File path: assets/images/ic_ranking_2.png
  AssetGenImage get icRanking2 =>
      const AssetGenImage('assets/images/ic_ranking_2.png');

  /// File path: assets/images/ic_ranking_3.png
  AssetGenImage get icRanking3 =>
      const AssetGenImage('assets/images/ic_ranking_3.png');

  /// File path: assets/images/ic_ranking_bg.png
  AssetGenImage get icRankingBg =>
      const AssetGenImage('assets/images/ic_ranking_bg.png');

  /// File path: assets/images/ic_ranking_empty.png
  AssetGenImage get icRankingEmpty =>
      const AssetGenImage('assets/images/ic_ranking_empty.png');

  /// File path: assets/images/ic_ranking_indicator.png
  AssetGenImage get icRankingIndicator =>
      const AssetGenImage('assets/images/ic_ranking_indicator.png');

  /// File path: assets/images/ic_ranking_list_bg.png
  AssetGenImage get icRankingListBg =>
      const AssetGenImage('assets/images/ic_ranking_list_bg.png');

  /// File path: assets/images/ic_ranking_location.png
  AssetGenImage get icRankingLocation =>
      const AssetGenImage('assets/images/ic_ranking_location.png');

  /// File path: assets/images/ic_ranking_qd.png
  AssetGenImage get icRankingQd =>
      const AssetGenImage('assets/images/ic_ranking_qd.png');

  /// File path: assets/images/ic_ranking_qy.png
  AssetGenImage get icRankingQy =>
      const AssetGenImage('assets/images/ic_ranking_qy.png');

  /// File path: assets/images/ic_ranking_self.png
  AssetGenImage get icRankingSelf =>
      const AssetGenImage('assets/images/ic_ranking_self.png');

  /// File path: assets/images/ic_ranking_switch_bg.png
  AssetGenImage get icRankingSwitchBg =>
      const AssetGenImage('assets/images/ic_ranking_switch_bg.png');

  /// File path: assets/images/ic_record_no.png
  AssetGenImage get icRecordNo =>
      const AssetGenImage('assets/images/ic_record_no.png');

  /// File path: assets/images/ic_report_hint.png
  AssetGenImage get icReportHint =>
      const AssetGenImage('assets/images/ic_report_hint.png');

  /// File path: assets/images/ic_report_unlock_hint.png
  AssetGenImage get icReportUnlockHint =>
      const AssetGenImage('assets/images/ic_report_unlock_hint.png');

  /// File path: assets/images/ic_search.png
  AssetGenImage get icSearch =>
      const AssetGenImage('assets/images/ic_search.png');

  /// File path: assets/images/ic_search_delete.png
  AssetGenImage get icSearchDelete =>
      const AssetGenImage('assets/images/ic_search_delete.png');

  /// File path: assets/images/ic_search_no.png
  AssetGenImage get icSearchNo =>
      const AssetGenImage('assets/images/ic_search_no.png');

  /// File path: assets/images/ic_share.png
  AssetGenImage get icShare =>
      const AssetGenImage('assets/images/ic_share.png');

  /// File path: assets/images/arena_detail_address_bg.png
  AssetGenImage get arenaDetailAddressBg =>
      const AssetGenImage('assets/images/arena_detail_address_bg.png');

  /// File path: assets/images/ic_share2.png
  AssetGenImage get icShare2 =>
      const AssetGenImage('assets/images/ic_share2.png');

  /// File path: assets/images/ic_sssy_fail.png
  AssetGenImage get icSssyFail =>
      const AssetGenImage('assets/images/ic_sssy_fail.png');

  /// File path: assets/images/ic_sssy_mvp.png
  AssetGenImage get icSssyMvp =>
      const AssetGenImage('assets/images/ic_sssy_mvp.png');

  /// File path: assets/images/ic_sssy_successfully.png
  AssetGenImage get icSssySuccessfully =>
      const AssetGenImage('assets/images/ic_sssy_successfully.png');

  /// File path: assets/images/ic_team_dfw.png
  AssetGenImage get icTeamDfw =>
      const AssetGenImage('assets/images/ic_team_dfw.png');

  /// File path: assets/images/ic_team_fqw.png
  AssetGenImage get icTeamFqw =>
      const AssetGenImage('assets/images/ic_team_fqw.png');

  /// File path: assets/images/ic_team_kt_bg.png
  AssetGenImage get icTeamKtBg =>
      const AssetGenImage('assets/images/ic_team_kt_bg.png');

  /// File path: assets/images/ic_team_lbw.png
  AssetGenImage get icTeamLbw =>
      const AssetGenImage('assets/images/ic_team_lbw.png');

  /// File path: assets/images/ic_team_mvp.png
  AssetGenImage get icTeamMvp =>
      const AssetGenImage('assets/images/ic_team_mvp.png');

  /// File path: assets/images/ic_team_mvp2.png
  AssetGenImage get icTeamMvp2 =>
      const AssetGenImage('assets/images/ic_team_mvp2.png');

  /// File path: assets/images/ic_team_mvp_bg.png
  AssetGenImage get icTeamMvpBg =>
      const AssetGenImage('assets/images/ic_team_mvp_bg.png');

  /// File path: assets/images/ic_team_mvp_qy.png
  AssetGenImage get icTeamMvpQy =>
      const AssetGenImage('assets/images/ic_team_mvp_qy.png');

  /// File path: assets/images/ic_team_qy.png
  AssetGenImage get icTeamQy =>
      const AssetGenImage('assets/images/ic_team_qy.png');

  /// File path: assets/images/ic_team_sfw.png
  AssetGenImage get icTeamSfw =>
      const AssetGenImage('assets/images/ic_team_sfw.png');

  /// File path: assets/images/ic_team_sl.png
  AssetGenImage get icTeamSl =>
      const AssetGenImage('assets/images/ic_team_sl.png');

  /// File path: assets/images/ic_team_vip.png
  AssetGenImage get icTeamVip =>
      const AssetGenImage('assets/images/ic_team_vip.png');

  /// File path: assets/images/ic_team_zgw.png
  AssetGenImage get icTeamZgw =>
      const AssetGenImage('assets/images/ic_team_zgw.png');

  /// File path: assets/images/ic_time.png
  AssetGenImage get icTime => const AssetGenImage('assets/images/ic_time.png');

  /// File path: assets/images/ic_time_white.png
  AssetGenImage get icTimeWhite =>
      const AssetGenImage('assets/images/ic_time_white.png');

  /// File path: assets/images/ic_tjm.png
  AssetGenImage get icTjm => const AssetGenImage('assets/images/ic_tjm.png');

  /// File path: assets/images/ic_video_download.png
  AssetGenImage get icVideoDownload =>
      const AssetGenImage('assets/images/ic_video_download.png');

  /// File path: assets/images/ic_video_scz.png
  AssetGenImage get icVideoScz =>
      const AssetGenImage('assets/images/ic_video_scz.png');

  /// File path: assets/images/ic_vip_10s.png
  AssetGenImage get icVip10s =>
      const AssetGenImage('assets/images/ic_vip_10s.png');

  /// File path: assets/images/ic_vip_arrow.png
  AssetGenImage get icVipArrow =>
      const AssetGenImage('assets/images/ic_vip_arrow.png');

  /// File path: assets/images/ic_vip_fm.png
  AssetGenImage get icVipFm =>
      const AssetGenImage('assets/images/ic_vip_fm.png');

  /// File path: assets/images/ic_vip_free.png
  AssetGenImage get icVipFree =>
      const AssetGenImage('assets/images/ic_vip_free.png');

  /// File path: assets/images/ic_vip_gp.png
  AssetGenImage get icVipGp =>
      const AssetGenImage('assets/images/ic_vip_gp.png');

  /// File path: assets/images/ic_vip_hint.png
  AssetGenImage get icVipHint =>
      const AssetGenImage('assets/images/ic_vip_hint.png');

  /// File path: assets/images/ic_vip_hint_bg.png
  AssetGenImage get icVipHintBg =>
      const AssetGenImage('assets/images/ic_vip_hint_bg.png');

  /// File path: assets/images/ic_vip_hy.png
  AssetGenImage get icVipHy =>
      const AssetGenImage('assets/images/ic_vip_hy.png');

  /// File path: assets/images/ic_vip_logo.png
  AssetGenImage get icVipLogo =>
      const AssetGenImage('assets/images/ic_vip_logo.png');

  /// File path: assets/images/ic_vip_new.png
  AssetGenImage get icVipNew =>
      const AssetGenImage('assets/images/ic_vip_new.png');

  /// File path: assets/images/ic_vip_new_price.png
  AssetGenImage get icVipNewPrice =>
      const AssetGenImage('assets/images/ic_vip_new_price.png');

  /// File path: assets/images/ic_vip_normal.png
  AssetGenImage get icVipNormal =>
      const AssetGenImage('assets/images/ic_vip_normal.png');

  /// File path: assets/images/ic_vip_pop.png
  AssetGenImage get icVipPop =>
      const AssetGenImage('assets/images/ic_vip_pop.png');

  /// File path: assets/images/ic_vip_sel.png
  AssetGenImage get icVipSel =>
      const AssetGenImage('assets/images/ic_vip_sel.png');

  /// File path: assets/images/ic_vip_ssq.png
  AssetGenImage get icVipSsq =>
      const AssetGenImage('assets/images/ic_vip_ssq.png');

  /// File path: assets/images/ic_vip_ssq_tag.png
  AssetGenImage get icVipSsqTag =>
      const AssetGenImage('assets/images/ic_vip_ssq_tag.png');

  /// File path: assets/images/ic_vip_svip.png
  AssetGenImage get icVipSvip =>
      const AssetGenImage('assets/images/ic_vip_svip.png');

  /// File path: assets/images/ic_vip_svip_sel.png
  AssetGenImage get icVipSvipSel =>
      const AssetGenImage('assets/images/ic_vip_svip_sel.png');

  /// File path: assets/images/ic_vip_sy.png
  AssetGenImage get icVipSy =>
      const AssetGenImage('assets/images/ic_vip_sy.png');

  /// File path: assets/images/ic_vip_title.png
  AssetGenImage get icVipTitle =>
      const AssetGenImage('assets/images/ic_vip_title.png');

  /// File path: assets/images/ic_vip_vip.png
  AssetGenImage get icVipVip =>
      const AssetGenImage('assets/images/ic_vip_vip.png');

  /// File path: assets/images/ic_vip_vip_sel.png
  AssetGenImage get icVipVipSel =>
      const AssetGenImage('assets/images/ic_vip_vip_sel.png');

  /// File path: assets/images/ic_vip_xc.png
  AssetGenImage get icVipXc =>
      const AssetGenImage('assets/images/ic_vip_xc.png');

  /// File path: assets/images/ic_vip_yj.png
  AssetGenImage get icVipYj =>
      const AssetGenImage('assets/images/ic_vip_yj.png');

  /// File path: assets/images/ic_woman.png
  AssetGenImage get icWoman =>
      const AssetGenImage('assets/images/ic_woman.png');

  /// File path: assets/images/ic_ysc.png
  AssetGenImage get icYsc => const AssetGenImage('assets/images/ic_ysc.png');

  /// File path: assets/images/img_check_in.png
  AssetGenImage get imgCheckIn =>
      const AssetGenImage('assets/images/img_check_in.png');

  /// File path: assets/images/img_check_in2.png
  AssetGenImage get imgCheckIn2 =>
      const AssetGenImage('assets/images/img_check_in2.png');

  /// File path: assets/images/img_copy.png
  AssetGenImage get imgCopy =>
      const AssetGenImage('assets/images/img_copy.png');

  /// File path: assets/images/img_id.png
  AssetGenImage get imgId => const AssetGenImage('assets/images/img_id.png');

  /// File path: assets/images/img_leader.png
  AssetGenImage get imgLeader =>
      const AssetGenImage('assets/images/img_leader.png');

  /// File path: assets/images/img_order.png
  AssetGenImage get imgOrder =>
      const AssetGenImage('assets/images/img_order.png');

  /// File path: assets/images/img_player1.png
  AssetGenImage get imgPlayer1 =>
      const AssetGenImage('assets/images/img_player1.png');

  /// File path: assets/images/img_player2.png
  AssetGenImage get imgPlayer2 =>
      const AssetGenImage('assets/images/img_player2.png');

  /// File path: assets/images/img_player5.png
  AssetGenImage get imgPlayer5 =>
      const AssetGenImage('assets/images/img_player5.png');

  /// File path: assets/images/img_player_data.png
  AssetGenImage get imgPlayerData =>
      const AssetGenImage('assets/images/img_player_data.png');

  /// File path: assets/images/img_player_defen.png
  AssetGenImage get imgPlayerDefen =>
      const AssetGenImage('assets/images/img_player_defen.png');

  /// File path: assets/images/img_player_faqiu.png
  AssetGenImage get imgPlayerFaqiu =>
      const AssetGenImage('assets/images/img_player_faqiu.png');

  /// File path: assets/images/img_player_head.png
  AssetGenImage get imgPlayerHead =>
      const AssetGenImage('assets/images/img_player_head.png');

  /// File path: assets/images/img_player_lanban.png
  AssetGenImage get imgPlayerLanban =>
      const AssetGenImage('assets/images/img_player_lanban.png');

  /// File path: assets/images/img_player_sanfen.png
  AssetGenImage get imgPlayerSanfen =>
      const AssetGenImage('assets/images/img_player_sanfen.png');

  /// File path: assets/images/img_player_zhugong.png
  AssetGenImage get imgPlayerZhugong =>
      const AssetGenImage('assets/images/img_player_zhugong.png');

  /// File path: assets/images/img_unbind.png
  AssetGenImage get imgUnbind =>
      const AssetGenImage('assets/images/img_unbind.png');

  /// File path: assets/images/img_unlock1.png
  AssetGenImage get imgUnlock1 =>
      const AssetGenImage('assets/images/img_unlock1.png');

  /// File path: assets/images/img_unlock2.png
  AssetGenImage get imgUnlock2 =>
      const AssetGenImage('assets/images/img_unlock2.png');

  /// File path: assets/images/img_unlock3.png
  AssetGenImage get imgUnlock3 =>
      const AssetGenImage('assets/images/img_unlock3.png');

  /// File path: assets/images/img_unlock_bg.png
  AssetGenImage get imgUnlockBg =>
      const AssetGenImage('assets/images/img_unlock_bg.png');

  /// File path: assets/images/img_unlock_coupons.png
  AssetGenImage get imgUnlockCoupons =>
      const AssetGenImage('assets/images/img_unlock_coupons.png');

  /// File path: assets/images/indicator_icon.png
  AssetGenImage get indicatorIcon =>
      const AssetGenImage('assets/images/indicator_icon.png');

  /// File path: assets/images/join_us.png
  AssetGenImage get joinUs => const AssetGenImage('assets/images/join_us.png');

  /// File path: assets/images/juxing.png
  AssetGenImage get juxing => const AssetGenImage('assets/images/juxing.png');

  /// File path: assets/images/loading_gif.gif
  AssetGenImage get loadingGif =>
      const AssetGenImage('assets/images/loading_gif.gif');

  /// File path: assets/images/login_bg.png
  AssetGenImage get loginBg =>
      const AssetGenImage('assets/images/login_bg.png');

  /// File path: assets/images/login_top_bg.png
  AssetGenImage get loginTopBg =>
      const AssetGenImage('assets/images/login_top_bg.png');

  /// File path: assets/images/match_bg.png
  AssetGenImage get matchBg =>
      const AssetGenImage('assets/images/match_bg.png');

  /// File path: assets/images/message_guanfang.png
  AssetGenImage get messageGuanfang =>
      const AssetGenImage('assets/images/message_guanfang.png');

  /// File path: assets/images/message_jiqi.png
  AssetGenImage get messageJiqi =>
      const AssetGenImage('assets/images/message_jiqi.png');

  /// File path: assets/images/message_system.png
  AssetGenImage get messageSystem =>
      const AssetGenImage('assets/images/message_system.png');

  /// File path: assets/images/mine_bg.png
  AssetGenImage get mineBg => const AssetGenImage('assets/images/mine_bg.png');

  /// File path: assets/images/mine_card.png
  AssetGenImage get mineCard =>
      const AssetGenImage('assets/images/mine_card.png');

  /// File path: assets/images/mine_career_bg.png
  AssetGenImage get mineCareerBg =>
      const AssetGenImage('assets/images/mine_career_bg.png');

  /// File path: assets/images/mine_cdkey.png
  AssetGenImage get mineCdkey =>
      const AssetGenImage('assets/images/mine_cdkey.png');

  /// File path: assets/images/mine_game.png
  AssetGenImage get mineGame =>
      const AssetGenImage('assets/images/mine_game.png');

  /// File path: assets/images/mine_join.png
  AssetGenImage get mineJoin =>
      const AssetGenImage('assets/images/mine_join.png');

  /// File path: assets/images/mine_kefu.png
  AssetGenImage get mineKefu =>
      const AssetGenImage('assets/images/mine_kefu.png');

  /// File path: assets/images/mine_order.png
  AssetGenImage get mineOrder =>
      const AssetGenImage('assets/images/mine_order.png');

  /// File path: assets/images/mine_points.png
  AssetGenImage get minePoints =>
      const AssetGenImage('assets/images/mine_points.png');

  /// File path: assets/images/mine_points_bg.png
  AssetGenImage get minePointsBg =>
      const AssetGenImage('assets/images/mine_points_bg.png');

  /// File path: assets/images/mine_ranking_bg.png
  AssetGenImage get mineRankingBg =>
      const AssetGenImage('assets/images/mine_ranking_bg.png');

  /// File path: assets/images/mine_set.png
  AssetGenImage get mineSet =>
      const AssetGenImage('assets/images/mine_set.png');

  /// File path: assets/images/mine_svip.png
  AssetGenImage get mineSvip =>
      const AssetGenImage('assets/images/mine_svip.png');

  /// File path: assets/images/mine_svip_icon.png
  AssetGenImage get mineSvipIcon =>
      const AssetGenImage('assets/images/mine_svip_icon.png');

  /// File path: assets/images/mine_team.png
  AssetGenImage get mineTeam =>
      const AssetGenImage('assets/images/mine_team.png');

  /// File path: assets/images/mine_tuijian.png
  AssetGenImage get mineTuijian =>
      const AssetGenImage('assets/images/mine_tuijian.png');

  /// File path: assets/images/mine_update.png
  AssetGenImage get mineUpdate =>
      const AssetGenImage('assets/images/mine_update.png');

  /// File path: assets/images/more_higlights_bg.png
  AssetGenImage get moreHiglightsBg =>
      const AssetGenImage('assets/images/more_higlights_bg.png');

  /// File path: assets/images/mine_update2.png
  AssetGenImage get mineUpdate2 =>
      const AssetGenImage('assets/images/mine_update2.png');

  /// File path: assets/images/mine_vip.png
  AssetGenImage get mineVip =>
      const AssetGenImage('assets/images/mine_vip.png');

  /// File path: assets/images/mine_vip1.png
  AssetGenImage get mineVip1 =>
      const AssetGenImage('assets/images/mine_vip1.png');

  /// File path: assets/images/mine_vip2.png
  AssetGenImage get mineVip2 =>
      const AssetGenImage('assets/images/mine_vip2.png');

  /// File path: assets/images/mine_vip_arrow.png
  AssetGenImage get mineVipArrow =>
      const AssetGenImage('assets/images/mine_vip_arrow.png');

  /// File path: assets/images/mobile3.png
  AssetGenImage get mobile3 => const AssetGenImage('assets/images/mobile3.png');

  /// File path: assets/images/my_team_add.png
  AssetGenImage get myTeamAdd =>
      const AssetGenImage('assets/images/my_team_add.png');

  /// File path: assets/images/my_team_head.png
  AssetGenImage get myTeamHead =>
      const AssetGenImage('assets/images/my_team_head.png');

  /// File path: assets/images/my_team_head3.png
  AssetGenImage get myTeamHead3 =>
      const AssetGenImage('assets/images/my_team_head3.png');

  /// File path: assets/images/my_team_head4.png
  AssetGenImage get myTeamHead4 =>
      const AssetGenImage('assets/images/my_team_head4.png');

  /// File path: assets/images/my_team_head_add.png
  AssetGenImage get myTeamHeadAdd =>
      const AssetGenImage('assets/images/my_team_head_add.png');

  /// File path: assets/images/no_data2.png
  AssetGenImage get noData2 =>
      const AssetGenImage('assets/images/no_data2.png');

  /// File path: assets/images/no_data_arena2.png
  AssetGenImage get noDataArena2 =>
      const AssetGenImage('assets/images/no_data_arena2.png');

  /// File path: assets/images/no_data_people.png
  AssetGenImage get noDataPeople =>
      const AssetGenImage('assets/images/no_data_people.png');

  /// File path: assets/images/no_goal.png
  AssetGenImage get noGoal => const AssetGenImage('assets/images/no_goal.png');

  /// File path: assets/images/no_videos.png
  AssetGenImage get noVideos =>
      const AssetGenImage('assets/images/no_videos.png');

  /// File path: assets/images/number_one_bg.png
  AssetGenImage get numberOneBg =>
      const AssetGenImage('assets/images/number_one_bg.png');

  /// File path: assets/images/number_one_headCircle.png
  AssetGenImage get numberOneHeadCircle =>
      const AssetGenImage('assets/images/number_one_headCircle.png');

  /// File path: assets/images/number_three_headCircle.png
  AssetGenImage get numberThreeHeadCircle =>
      const AssetGenImage('assets/images/number_three_headCircle.png');

  /// File path: assets/images/number_two_headCircle.png
  AssetGenImage get numberTwoHeadCircle =>
      const AssetGenImage('assets/images/number_two_headCircle.png');

  /// File path: assets/images/one_stars_icon.png
  AssetGenImage get oneStarsIcon =>
      const AssetGenImage('assets/images/one_stars_icon.png');

  /// File path: assets/images/option_ai.png
  AssetGenImage get optionAi =>
      const AssetGenImage('assets/images/option_ai.png');

  /// File path: assets/images/option_document.png
  AssetGenImage get optionDocument =>
      const AssetGenImage('assets/images/option_document.png');

  /// File path: assets/images/option_download.png
  AssetGenImage get optionDownload =>
      const AssetGenImage('assets/images/option_download.png');

  /// File path: assets/images/option_goal_ add.png
  AssetGenImage get optionGoalAdd =>
      const AssetGenImage('assets/images/option_goal_ add.png');

  /// File path: assets/images/option_goal_ live.png
  AssetGenImage get optionGoalLive =>
      const AssetGenImage('assets/images/option_goal_ live.png');

  /// File path: assets/images/option_goal_ primary.png
  AssetGenImage get optionGoalPrimary =>
      const AssetGenImage('assets/images/option_goal_ primary.png');

  /// File path: assets/images/option_goal_ rwater.png
  AssetGenImage get optionGoalRwater =>
      const AssetGenImage('assets/images/option_goal_ rwater.png');

  /// File path: assets/images/option_goal_ time.png
  AssetGenImage get optionGoalTime =>
      const AssetGenImage('assets/images/option_goal_ time.png');

  /// File path: assets/images/option_goal_set.png
  AssetGenImage get optionGoalSet =>
      const AssetGenImage('assets/images/option_goal_set.png');

  /// File path: assets/images/option_share.png
  AssetGenImage get optionShare =>
      const AssetGenImage('assets/images/option_share.png');

  /// File path: assets/images/option_video.png
  AssetGenImage get optionVideo =>
      const AssetGenImage('assets/images/option_video.png');

  /// File path: assets/images/option_video_hui.png
  AssetGenImage get optionVideoHui =>
      const AssetGenImage('assets/images/option_video_hui.png');

  /// File path: assets/images/page_top_bg.png
  AssetGenImage get pageTopBg =>
      const AssetGenImage('assets/images/page_top_bg.png');

  /// File path: assets/images/points2.png
  AssetGenImage get points2 => const AssetGenImage('assets/images/points2.png');

  /// File path: assets/images/points2_hui.png
  AssetGenImage get points2Hui =>
      const AssetGenImage('assets/images/points2_hui.png');

  /// File path: assets/images/points_color.png
  AssetGenImage get pointsColor =>
      const AssetGenImage('assets/images/points_color.png');

  /// File path: assets/images/points_details_bg.png
  AssetGenImage get pointsDetailsBg =>
      const AssetGenImage('assets/images/points_details_bg.png');

  /// File path: assets/images/points_help.png
  AssetGenImage get pointsHelp =>
      const AssetGenImage('assets/images/points_help.png');

  /// File path: assets/images/points_help_white.png
  AssetGenImage get pointsHelpWhite =>
      const AssetGenImage('assets/images/points_help_white.png');

  /// File path: assets/images/points_notification.png
  AssetGenImage get pointsNotification =>
      const AssetGenImage('assets/images/points_notification.png');

  /// File path: assets/images/points_sign.png
  AssetGenImage get pointsSign =>
      const AssetGenImage('assets/images/points_sign.png');

  /// File path: assets/images/points_sign_no.png
  AssetGenImage get pointsSignNo =>
      const AssetGenImage('assets/images/points_sign_no.png');

  /// File path: assets/images/points_size.png
  AssetGenImage get pointsSize =>
      const AssetGenImage('assets/images/points_size.png');

  /// File path: assets/images/ponits_bg.png
  AssetGenImage get ponitsBg =>
      const AssetGenImage('assets/images/ponits_bg.png');

  /// File path: assets/images/ponits_exchange.png
  AssetGenImage get ponitsExchange =>
      const AssetGenImage('assets/images/ponits_exchange.png');

  /// File path: assets/images/purpose_empty.png
  AssetGenImage get purposeEmpty =>
      const AssetGenImage('assets/images/purpose_empty.png');

  /// File path: assets/images/query_icon.png
  AssetGenImage get queryIcon =>
      const AssetGenImage('assets/images/query_icon.png');

  /// File path: assets/images/ranking_other_bg.png
  AssetGenImage get rankingOtherBg =>
      const AssetGenImage('assets/images/ranking_other_bg.png');

  /// File path: assets/images/ranking_select_bg.png
  AssetGenImage get rankingSelectBg =>
      const AssetGenImage('assets/images/ranking_select_bg.png');

  /// File path: assets/images/rankings_bg.png
  AssetGenImage get rankingsBg =>
      const AssetGenImage('assets/images/rankings_bg.png');

  /// File path: assets/images/rankings_big_bg.png
  AssetGenImage get rankingsBigBg =>
      const AssetGenImage('assets/images/rankings_big_bg.png');

  /// File path: assets/images/roundName_bg.png
  AssetGenImage get roundNameBg =>
      const AssetGenImage('assets/images/roundName_bg.png');

  /// File path: assets/images/rules_icon.png
  AssetGenImage get rulesIcon =>
      const AssetGenImage('assets/images/rules_icon.png');

  /// File path: assets/images/select_icon.png
  AssetGenImage get selectIcon =>
      const AssetGenImage('assets/images/select_icon.png');

  /// File path: assets/images/select_indicator_icon.png
  AssetGenImage get selectIndicatorIcon =>
      const AssetGenImage('assets/images/select_indicator_icon.png');

  /// File path: assets/images/select_time.png
  AssetGenImage get selectTime =>
      const AssetGenImage('assets/images/select_time.png');

  /// File path: assets/images/selfie_shot1.png
  AssetGenImage get selfieShot1 =>
      const AssetGenImage('assets/images/selfie_shot1.png');

  /// File path: assets/images/selfie_shot2.png
  AssetGenImage get selfieShot2 =>
      const AssetGenImage('assets/images/selfie_shot2.png');

  /// File path: assets/images/selfie_shot_pause.png
  AssetGenImage get selfieShotPause =>
      const AssetGenImage('assets/images/selfie_shot_pause.png');

  /// File path: assets/images/selfie_shot_play.png
  AssetGenImage get selfieShotPlay =>
      const AssetGenImage('assets/images/selfie_shot_play.png');

  /// File path: assets/images/selfie_shot_share.png
  AssetGenImage get selfieShotShare =>
      const AssetGenImage('assets/images/selfie_shot_share.png');

  /// File path: assets/images/selfie_shot_tips.png
  AssetGenImage get selfieShotTips =>
      const AssetGenImage('assets/images/selfie_shot_tips.png');

  /// File path: assets/images/selfie_shot_update.png
  AssetGenImage get selfieShotUpdate =>
      const AssetGenImage('assets/images/selfie_shot_update.png');

  /// File path: assets/images/set.png
  AssetGenImage get set => const AssetGenImage('assets/images/set.png');

  /// File path: assets/images/share3.png
  AssetGenImage get share3 => const AssetGenImage('assets/images/share3.png');

  /// File path: assets/images/share_send_icon.png
  AssetGenImage get shareSendIcon =>
      const AssetGenImage('assets/images/share_send_icon.png');

  /// File path: assets/images/shoot_place_holder_img1.png
  AssetGenImage get shootPlaceHolderImg1 =>
      const AssetGenImage('assets/images/shoot_place_holder_img1.png');

  /// File path: assets/images/shoot_place_holder_img2.png
  AssetGenImage get shootPlaceHolderImg2 =>
      const AssetGenImage('assets/images/shoot_place_holder_img2.png');

  /// File path: assets/images/sign_up_icon.png
  AssetGenImage get signUpIcon =>
      const AssetGenImage('assets/images/sign_up_icon.png');

  /// File path: assets/images/site_report_bg1.png
  AssetGenImage get siteReportBg1 =>
      const AssetGenImage('assets/images/site_report_bg1.png');

  /// File path: assets/images/site_report_video.png
  AssetGenImage get siteReportVideo =>
      const AssetGenImage('assets/images/site_report_video.png');

  /// File path: assets/images/site_report_video2.png
  AssetGenImage get siteReportVideo2 =>
      const AssetGenImage('assets/images/site_report_video2.png');

  /// File path: assets/images/star_empty.png
  AssetGenImage get starEmpty =>
      const AssetGenImage('assets/images/star_empty.png');

  /// File path: assets/images/star_full.png
  AssetGenImage get starFull =>
      const AssetGenImage('assets/images/star_full.png');

  /// File path: assets/images/svip_adv1.png
  AssetGenImage get svipAdv1 =>
      const AssetGenImage('assets/images/svip_adv1.png');

  /// File path: assets/images/svip_adv2.png
  AssetGenImage get svipAdv2 =>
      const AssetGenImage('assets/images/svip_adv2.png');

  /// File path: assets/images/svip_adv3.png
  AssetGenImage get svipAdv3 =>
      const AssetGenImage('assets/images/svip_adv3.png');

  /// File path: assets/images/switch_off.png
  AssetGenImage get switchOff =>
      const AssetGenImage('assets/images/switch_off.png');

  /// File path: assets/images/switch_on.png
  AssetGenImage get switchOn =>
      const AssetGenImage('assets/images/switch_on.png');

  /// File path: assets/images/tab_bg.png
  AssetGenImage get tabBg => const AssetGenImage('assets/images/tab_bg.png');

  /// File path: assets/images/tab_home_img_play.png
  AssetGenImage get tabHomeImgPlay =>
      const AssetGenImage('assets/images/tab_home_img_play.png');

  /// File path: assets/images/tab_img2.png
  AssetGenImage get tabImg2 =>
      const AssetGenImage('assets/images/tab_img2.png');

  /// File path: assets/images/tab_img3.png
  AssetGenImage get tabImg3 =>
      const AssetGenImage('assets/images/tab_img3.png');

  /// File path: assets/images/tab_img3_no.png
  AssetGenImage get tabImg3No =>
      const AssetGenImage('assets/images/tab_img3_no.png');

  /// File path: assets/images/team_data1.png
  AssetGenImage get teamData1 =>
      const AssetGenImage('assets/images/team_data1.png');

  /// File path: assets/images/team_data2.png
  AssetGenImage get teamData2 =>
      const AssetGenImage('assets/images/team_data2.png');

  /// File path: assets/images/team_data3.png
  AssetGenImage get teamData3 =>
      const AssetGenImage('assets/images/team_data3.png');

  /// File path: assets/images/team_data4.png
  AssetGenImage get teamData4 =>
      const AssetGenImage('assets/images/team_data4.png');

  /// File path: assets/images/team_data5.png
  AssetGenImage get teamData5 =>
      const AssetGenImage('assets/images/team_data5.png');

  /// File path: assets/images/team_data6.png
  AssetGenImage get teamData6 =>
      const AssetGenImage('assets/images/team_data6.png');

  /// File path: assets/images/team_empty_icon.png
  AssetGenImage get teamEmptyIcon =>
      const AssetGenImage('assets/images/team_empty_icon.png');

  /// File path: assets/images/team_info_bg.png
  AssetGenImage get teamInfoBg =>
      const AssetGenImage('assets/images/team_info_bg.png');

  /// File path: assets/images/team_info_bg2.png
  AssetGenImage get teamInfoBg2 =>
      const AssetGenImage('assets/images/team_info_bg2.png');

  /// File path: assets/images/team_info_nodata.png
  AssetGenImage get teamInfoNodata =>
      const AssetGenImage('assets/images/team_info_nodata.png');

  /// File path: assets/images/team_info_nodata2.png
  AssetGenImage get teamInfoNodata2 =>
      const AssetGenImage('assets/images/team_info_nodata2.png');

  /// File path: assets/images/team_info_nophoto.png
  AssetGenImage get teamInfoNophoto =>
      const AssetGenImage('assets/images/team_info_nophoto.png');

  /// File path: assets/images/team_info_qrcode.png
  AssetGenImage get teamInfoQrcode =>
      const AssetGenImage('assets/images/team_info_qrcode.png');

  /// File path: assets/images/team_main.png
  AssetGenImage get teamMain =>
      const AssetGenImage('assets/images/team_main.png');

  /// File path: assets/images/team_refresh.png
  AssetGenImage get teamRefresh =>
      const AssetGenImage('assets/images/team_refresh.png');

  /// File path: assets/images/three_stars_icon.png
  AssetGenImage get threeStarsIcon =>
      const AssetGenImage('assets/images/three_stars_icon.png');

  /// File path: assets/images/tips.png
  AssetGenImage get tips => const AssetGenImage('assets/images/tips.png');

  /// File path: assets/images/title_tag.png
  AssetGenImage get titleTag =>
      const AssetGenImage('assets/images/title_tag.png');

  /// File path: assets/images/top_title_image.png
  AssetGenImage get topTitleImage =>
      const AssetGenImage('assets/images/top_title_image.png');

  /// File path: assets/images/two_stars_icon.png
  AssetGenImage get twoStarsIcon =>
      const AssetGenImage('assets/images/two_stars_icon.png');

  /// File path: assets/images/uncheck.png
  AssetGenImage get uncheck => const AssetGenImage('assets/images/uncheck.png');

  /// File path: assets/images/unlock_bg2.png
  AssetGenImage get unlockBg2 =>
      const AssetGenImage('assets/images/unlock_bg2.png');

  /// File path: assets/images/unlock_bg3.png
  AssetGenImage get unlockBg3 =>
      const AssetGenImage('assets/images/unlock_bg3.png');

  /// File path: assets/images/unrecorded.png
  AssetGenImage get unrecorded =>
      const AssetGenImage('assets/images/unrecorded.png');

  /// File path: assets/images/unselect_icon.png
  AssetGenImage get unselectIcon =>
      const AssetGenImage('assets/images/unselect_icon.png');

  /// File path: assets/images/updateversion1.png
  AssetGenImage get updateversion1 =>
      const AssetGenImage('assets/images/updateversion1.png');

  /// File path: assets/images/updateversiontitle.png
  AssetGenImage get updateversiontitle =>
      const AssetGenImage('assets/images/updateversiontitle.png');

  /// File path: assets/images/video_play.png
  AssetGenImage get videoPlay =>
      const AssetGenImage('assets/images/video_play.png');

  /// File path: assets/images/vip_Invitation_dialog1.png
  AssetGenImage get vipInvitationDialog1 =>
      const AssetGenImage('assets/images/vip_Invitation_dialog1.png');

  /// File path: assets/images/vip_Invitation_dialog2.png
  AssetGenImage get vipInvitationDialog2 =>
      const AssetGenImage('assets/images/vip_Invitation_dialog2.png');

  /// File path: assets/images/vip_Invitation_dialog3.png
  AssetGenImage get vipInvitationDialog3 =>
      const AssetGenImage('assets/images/vip_Invitation_dialog3.png');

  /// File path: assets/images/vip_Invitation_dialog4.png
  AssetGenImage get vipInvitationDialog4 =>
      const AssetGenImage('assets/images/vip_Invitation_dialog4.png');

  /// File path: assets/images/vip_Invitation_dialog5.png
  AssetGenImage get vipInvitationDialog5 =>
      const AssetGenImage('assets/images/vip_Invitation_dialog5.png');

  /// File path: assets/images/vip_Invitation_dialog6.png
  AssetGenImage get vipInvitationDialog6 =>
      const AssetGenImage('assets/images/vip_Invitation_dialog6.png');

  /// File path: assets/images/vip_Invitation_dialog7.png
  AssetGenImage get vipInvitationDialog7 =>
      const AssetGenImage('assets/images/vip_Invitation_dialog7.png');

  /// File path: assets/images/vip_dialog01.png
  AssetGenImage get vipDialog01 =>
      const AssetGenImage('assets/images/vip_dialog01.png');

  /// File path: assets/images/vip_dialog02.png
  AssetGenImage get vipDialog02 =>
      const AssetGenImage('assets/images/vip_dialog02.png');

  /// File path: assets/images/vip_dialog03.png
  AssetGenImage get vipDialog03 =>
      const AssetGenImage('assets/images/vip_dialog03.png');

  /// File path: assets/images/vip_dialog04.png
  AssetGenImage get vipDialog04 =>
      const AssetGenImage('assets/images/vip_dialog04.png');

  /// File path: assets/images/vip_dialog05.png
  AssetGenImage get vipDialog05 =>
      const AssetGenImage('assets/images/vip_dialog05.png');

  /// File path: assets/images/vip_dialog06.png
  AssetGenImage get vipDialog06 =>
      const AssetGenImage('assets/images/vip_dialog06.png');

  /// File path: assets/images/vip_dialog07.png
  AssetGenImage get vipDialog07 =>
      const AssetGenImage('assets/images/vip_dialog07.png');

  /// File path: assets/images/vip_dialog08.png
  AssetGenImage get vipDialog08 =>
      const AssetGenImage('assets/images/vip_dialog08.png');

  /// File path: assets/images/vip_dialog09.png
  AssetGenImage get vipDialog09 =>
      const AssetGenImage('assets/images/vip_dialog09.png');

  /// File path: assets/images/vip_dialog10.png
  AssetGenImage get vipDialog10 =>
      const AssetGenImage('assets/images/vip_dialog10.png');

  /// File path: assets/images/vip_dialog11.png
  AssetGenImage get vipDialog11 =>
      const AssetGenImage('assets/images/vip_dialog11.png');

  /// File path: assets/images/vip_dialog12.png
  AssetGenImage get vipDialog12 =>
      const AssetGenImage('assets/images/vip_dialog12.png');

  /// File path: assets/images/vipdialog.png
  AssetGenImage get vipdialog =>
      const AssetGenImage('assets/images/vipdialog.png');

  /// File path: assets/images/vipdialog1.png
  AssetGenImage get vipdialog1 =>
      const AssetGenImage('assets/images/vipdialog1.png');

  /// File path: assets/images/vipdialog2.png
  AssetGenImage get vipdialog2 =>
      const AssetGenImage('assets/images/vipdialog2.png');

  /// File path: assets/images/watermark.png
  AssetGenImage get watermark =>
      const AssetGenImage('assets/images/watermark.png');

  /// File path: assets/images/wx_logo.png
  AssetGenImage get wxLogo => const AssetGenImage('assets/images/wx_logo.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        invitationDialog1,
        invitationDialog2,
        invitationDialog3,
        invitationDialog4,
        receiveVip,
        addressCheck,
        addressDelete,
        addressEdit,
        addressLocation,
        addressNodata,
        addressOk,
        addressUncheck,
        advancedArena,
        lampIcon,
        ai,
        ai2,
        ai3,
        ai4,
        aiReportEmpty,
        aiBg,
        aiImg1,
        aiReportIcon1,
        aiReportIcon2,
        aiReportIcon3,
        aiReportIcon4,
        albumEmpty,
        appleLogo,
        arrowBackRound,
        arrowDown,
        arrowLeft,
        arrowRight,
        battleEmptyIcon,
        callPhone,
        careerBg,
        cart,
        chartPoint,
        check,
        checkOn3,
        checkOn3Wihte,
        choiceNo,
        choiceYes,
        chooseYes2,
        clockFailure,
        clockSuccessful,
        close2,
        compositionFailure,
        contributeIcon,
        couponsBg,
        couponsBg2,
        couponsGuoqi,
        couponsNodata,
        couponsTime,
        couponsUsed,
        creationWay1,
        creationWay2,
        daka1,
        daka2,
        daka3,
        delete,
        dialogInvitation,
        dialogTalk1,
        dialogTeamInfo1,
        dialogTeamInfo2,
        dialogTeamInfo3,
        dialogTeamInfo4,
        dialogTeamInfo5,
        dialoglocation,
        dialogvideo,
        document,
        document2,
        editIcon,
        errorImage,
        errorImageWidth,
        errorImgWhite,
        eventReservation1,
        eventReservation2,
        eventReservation3,
        eventReservation5,
        eventReservation6,
        eventReservation7,
        feelback,
        fire,
        fiveStarsIcon,
        fourStarsIcon,
        goalAdd,
        goalAi,
        goalAiText,
        goalBg,
        goalBg1,
        goalBg2,
        goalHelp,
        goalHelp2,
        goalRemove,
        googleLogo,
        halfAdd,
        halfShoot1,
        halfShoot2,
        halfShoot3,
        homeImgBg,
        homeImgBottom,
        homeImgBottomLong,
        homeMessage,
        homeTitleBg,
        homeTopBg,
        homeYueZhan,
        icApr,
        icAug,
        icDec,
        icFeb,
        icJan,
        icJul,
        icJun,
        icMar,
        icMay,
        icNov,
        icOct,
        icSep,
        icArrowDown,
        icArrowRight,
        icBsNo,
        icBsSel,
        icCamera,
        icCircleBs,
        icCityArrow,
        icClose,
        icClose2,
        icClose3,
        icCloseDialog,
        icDelete,
        icDfx,
        icDownload,
        icEdit,
        icEidtHead,
        icFxz,
        icGameLock,
        icGameNo,
        icGameUnlock,
        icGameVs,
        icGou,
        icHighlightsArrow,
        icHighlightsNo,
        icHlHcz,
        icHlItemBottom,
        icHlLocation,
        icHlNew,
        icHlPdz,
        icHlPeople,
        icHlWait,
        icHomeLocationHint,
        icHomeNo,
        icHomeSel,
        icHomeTj,
        icInviteCodeBg,
        icInviteCodeCard,
        icInviteCodeDes,
        icInviteCodeHint,
        icInviteCodeJf,
        icInviteCodeSkip,
        icInviteCodeTitle,
        icInviteCodeTm,
        icInviteCodeYqhy,
        icKfHint,
        icKfLogo,
        icKfWx,
        icLocation,
        matchLiveIcon,
        matchLiveHighlighting,
        icLocationHint,
        icMan,
        icMineBg,
        icMineBtnBg,
        icMineJf,
        icMineKf,
        icMineNo,
        icMineOrder,
        icMineSel,
        icMineSettings,
        icMineTj,
        icMineVip,
        icMineVipNo,
        icMineWdl,
        icMoreAll,
        icMyOrder,
        icMyTeam,
        icPhotoPermission,
        icPlaceSearch,
        icQuestion,
        icRanking1,
        icRanking2,
        icRanking3,
        icRankingBg,
        icRankingEmpty,
        icRankingIndicator,
        icRankingListBg,
        icRankingLocation,
        icRankingQd,
        icRankingQy,
        icRankingSelf,
        icRankingSwitchBg,
        icRecordNo,
        icReportHint,
        icReportUnlockHint,
        icSearch,
        icSearchDelete,
        icSearchNo,
        icShare,
        arenaDetailAddressBg,
        icShare2,
        icSssyFail,
        icSssyMvp,
        icSssySuccessfully,
        icTeamDfw,
        icTeamFqw,
        icTeamKtBg,
        icTeamLbw,
        icTeamMvp,
        icTeamMvp2,
        icTeamMvpBg,
        icTeamMvpQy,
        icTeamQy,
        icTeamSfw,
        icTeamSl,
        icTeamVip,
        icTeamZgw,
        icTime,
        icTimeWhite,
        icTjm,
        icVideoDownload,
        icVideoScz,
        icVip10s,
        icVipArrow,
        icVipFm,
        icVipFree,
        icVipGp,
        icVipHint,
        icVipHintBg,
        icVipHy,
        icVipLogo,
        icVipNew,
        icVipNewPrice,
        icVipNormal,
        icVipPop,
        icVipSel,
        icVipSsq,
        icVipSsqTag,
        icVipSvip,
        icVipSvipSel,
        icVipSy,
        icVipTitle,
        icVipVip,
        icVipVipSel,
        icVipXc,
        icVipYj,
        icWoman,
        icYsc,
        imgCheckIn,
        imgCheckIn2,
        imgCopy,
        imgId,
        imgLeader,
        imgOrder,
        imgPlayer1,
        imgPlayer2,
        imgPlayer5,
        imgPlayerData,
        imgPlayerDefen,
        imgPlayerFaqiu,
        imgPlayerHead,
        imgPlayerLanban,
        imgPlayerSanfen,
        imgPlayerZhugong,
        imgUnbind,
        imgUnlock1,
        imgUnlock2,
        imgUnlock3,
        imgUnlockBg,
        imgUnlockCoupons,
        indicatorIcon,
        joinUs,
        juxing,
        loadingGif,
        loginBg,
        loginTopBg,
        matchBg,
        messageGuanfang,
        messageJiqi,
        messageSystem,
        mineBg,
        mineCard,
        mineCareerBg,
        mineCdkey,
        mineGame,
        mineJoin,
        mineKefu,
        mineOrder,
        minePoints,
        minePointsBg,
        mineRankingBg,
        mineSet,
        mineSvip,
        mineSvipIcon,
        mineTeam,
        mineTuijian,
        mineUpdate,
        moreHiglightsBg,
        mineUpdate2,
        mineVip,
        mineVip1,
        mineVip2,
        mineVipArrow,
        mobile3,
        myTeamAdd,
        myTeamHead,
        myTeamHead3,
        myTeamHead4,
        myTeamHeadAdd,
        noData2,
        noDataArena2,
        noDataPeople,
        noGoal,
        noVideos,
        numberOneBg,
        numberOneHeadCircle,
        numberThreeHeadCircle,
        numberTwoHeadCircle,
        oneStarsIcon,
        optionAi,
        optionDocument,
        optionDownload,
        optionGoalAdd,
        optionGoalLive,
        optionGoalPrimary,
        optionGoalRwater,
        optionGoalTime,
        optionGoalSet,
        optionShare,
        optionVideo,
        optionVideoHui,
        pageTopBg,
        points2,
        points2Hui,
        pointsColor,
        pointsDetailsBg,
        pointsHelp,
        pointsHelpWhite,
        pointsNotification,
        pointsSign,
        pointsSignNo,
        pointsSize,
        ponitsBg,
        ponitsExchange,
        purposeEmpty,
        queryIcon,
        rankingOtherBg,
        rankingSelectBg,
        rankingsBg,
        rankingsBigBg,
        roundNameBg,
        rulesIcon,
        selectIcon,
        selectIndicatorIcon,
        selectTime,
        selfieShot1,
        selfieShot2,
        selfieShotPause,
        selfieShotPlay,
        selfieShotShare,
        selfieShotTips,
        selfieShotUpdate,
        set,
        share3,
        shareSendIcon,
        shootPlaceHolderImg1,
        shootPlaceHolderImg2,
        signUpIcon,
        siteReportBg1,
        siteReportVideo,
        siteReportVideo2,
        starEmpty,
        starFull,
        svipAdv1,
        svipAdv2,
        svipAdv3,
        switchOff,
        switchOn,
        tabBg,
        tabHomeImgPlay,
        tabImg2,
        tabImg3,
        tabImg3No,
        teamData1,
        teamData2,
        teamData3,
        teamData4,
        teamData5,
        teamData6,
        teamEmptyIcon,
        teamInfoBg,
        teamInfoBg2,
        teamInfoNodata,
        teamInfoNodata2,
        teamInfoNophoto,
        teamInfoQrcode,
        teamMain,
        teamRefresh,
        threeStarsIcon,
        tips,
        titleTag,
        topTitleImage,
        twoStarsIcon,
        uncheck,
        unlockBg2,
        unlockBg3,
        unrecorded,
        unselectIcon,
        updateversion1,
        updateversiontitle,
        videoPlay,
        vipInvitationDialog1,
        vipInvitationDialog2,
        vipInvitationDialog3,
        vipInvitationDialog4,
        vipInvitationDialog5,
        vipInvitationDialog6,
        vipInvitationDialog7,
        vipDialog01,
        vipDialog02,
        vipDialog03,
        vipDialog04,
        vipDialog05,
        vipDialog06,
        vipDialog07,
        vipDialog08,
        vipDialog09,
        vipDialog10,
        vipDialog11,
        vipDialog12,
        vipdialog,
        vipdialog1,
        vipdialog2,
        watermark,
        wxLogo
      ];
}

class $AssetsImages30xGen {
  const $AssetsImages30xGen();

  /// File path: assets/images/3.0x/ic_launcher.png
  AssetGenImage get icLauncher =>
      const AssetGenImage('assets/images/3.0x/ic_launcher.png');

  /// List of all assets
  List<AssetGenImage> get values => [icLauncher];
}

class WxAssets {
  const WxAssets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
