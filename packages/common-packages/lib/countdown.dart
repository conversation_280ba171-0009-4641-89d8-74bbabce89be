import 'dart:async';

import 'package:flutter_common/wx_logger.dart';

class Countdown {
  final int seconds;
  final void Function() onFinish;
  final void Function(int t)? onTick;

  late int _lastTickTime;
  late int _cd;
  Timer? _timer;
  bool _running = false;

  Countdown(this.seconds, {required this.onFinish, this.onTick}) {
    _cd = seconds * 1000;
  }

  ///开始
  start() {
    if (_running) {
      return;
    }
    _lastTickTime = DateTime.now().millisecondsSinceEpoch;
    _running = true;

    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) async {
      if (!_running) {
        return;
      }
      _exec();
    });
  }

  /// 结算一次时间
  _exec() {
    int now = DateTime.now().millisecondsSinceEpoch;

    var pass = now - _lastTickTime; //过去的毫秒数
    var last = _cd;
    _cd -= pass;

    var tickCount = (last / 1000).floor() - (_cd / 1000).floor(); //过去的秒数，补时回调

    if (_cd <= 0) {
      tickCount -= 1; //因为最后一下直接回调完成。
    }

    _lastTickTime = now;
    for (var i = 0; i < tickCount && onTick != null; i++) {
      onTick!(((last - i) / 1000).floor());
    }

    if (_cd <= 0) {
      onFinish();
      cancel(balance: false);
    }
  }

  /// 取消
  /// balance = 结算最后一次
  cancel({bool balance = true}) {
    if (_running) {
      if (balance) {
        _exec();
      }
      _running = false;
      _timer?.cancel();
    }
  }

  /// 暂停
  pause() {
    if (_timer?.isActive ?? false) {
      _running = false;
      _exec(); //立即结算一次时间
    }
  }

  /// 恢复
  resume() {
    if (_timer?.isActive ?? false) {
      _lastTickTime = DateTime.now().millisecondsSinceEpoch;
      _running = true;
    }
  }

  /// 快进
  fastForward(double seconds) {
    if ((_timer?.isActive ?? false) && _running) {
      _lastTickTime -= (seconds * 1000).floor();
    }
  }

  isRunning() {
    return _running;
  }
}
