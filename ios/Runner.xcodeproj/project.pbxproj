// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0190924C2DD19D0A00A12F13 /* TimerManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0190924B2DD19D0A00A12F13 /* TimerManager.swift */; };
		019092F82DD1F00800A12F13 /* AnnotationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 019092F72DD1F00800A12F13 /* AnnotationView.swift */; };
		019092FA2DD2FA4D00A12F13 /* LowBatteryWarningVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 019092F92DD2FA4D00A12F13 /* LowBatteryWarningVC.swift */; };
		019092FB2DD2FA4D00A12F13 /* LowBatteryWarningVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 019092F92DD2FA4D00A12F13 /* LowBatteryWarningVC.swift */; };
		019093902DD3330D00A12F13 /* Homography.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0190938E2DD3330D00A12F13 /* Homography.swift */; };
		019093922DD6DA3000A12F13 /* CustomAlertViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 019093912DD6DA3000A12F13 /* CustomAlertViewController.swift */; };
		019093932DD6DA3000A12F13 /* CustomAlertViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 019093912DD6DA3000A12F13 /* CustomAlertViewController.swift */; };
		01C64C312E17C24800AF17CF /* DocumentManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 01C64C302E17C24800AF17CF /* DocumentManager.swift */; };
		01C64CC82E18FE9E00AF17CF /* clsf_goal.tflite in Resources */ = {isa = PBXBuildFile; fileRef = 01C64CC72E18FE9E00AF17CF /* clsf_goal.tflite */; };
		01C64D0F2E190B7300AF17CF /* ShootClassifierService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 01C64D0E2E190B7300AF17CF /* ShootClassifierService.swift */; };
		01C64D122E190C2300AF17CF /* ShootClassifierInferenceConfigurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 01C64D112E190C2200AF17CF /* ShootClassifierInferenceConfigurationManager.swift */; };
		01C64D132E190C2300AF17CF /* ShootClassifierConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 01C64D102E190C2200AF17CF /* ShootClassifierConstants.swift */; };
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 331C807B294A618700263BE5 /* RunnerTests.swift */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		8E7070292DC0795800125669 /* MyCustomViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070282DC0795800125669 /* MyCustomViewController.swift */; };
		8E70702A2DC0795800125669 /* MyCustomViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070282DC0795800125669 /* MyCustomViewController.swift */; };
		8E70702F2DC09FE500125669 /* SessionDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70702E2DC09FE500125669 /* SessionDetailViewController.swift */; };
		8E7070302DC09FE500125669 /* SessionDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70702E2DC09FE500125669 /* SessionDetailViewController.swift */; };
		8E7070322DC09FF200125669 /* RecordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070312DC09FF200125669 /* RecordViewController.swift */; };
		8E7070332DC09FF200125669 /* RecordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070312DC09FF200125669 /* RecordViewController.swift */; };
		8E7070352DC0A01700125669 /* GoalCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070342DC0A01700125669 /* GoalCollectionViewCell.swift */; };
		8E7070362DC0A01700125669 /* GoalCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070342DC0A01700125669 /* GoalCollectionViewCell.swift */; };
		8E7070382DC0A12F00125669 /* CameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070372DC0A12F00125669 /* CameraViewController.swift */; };
		8E7070392DC0A12F00125669 /* CameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070372DC0A12F00125669 /* CameraViewController.swift */; };
		8E70703F2DC0A28B00125669 /* ClassifierConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70703A2DC0A28B00125669 /* ClassifierConstants.swift */; };
		8E7070402DC0A28B00125669 /* InferenceConfigurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70703D2DC0A28B00125669 /* InferenceConfigurationManager.swift */; };
		8E7070412DC0A28B00125669 /* GoalClassifierInferenceConfigurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70703C2DC0A28B00125669 /* GoalClassifierInferenceConfigurationManager.swift */; };
		8E7070422DC0A28B00125669 /* DefaultConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70703B2DC0A28B00125669 /* DefaultConstants.swift */; };
		8E7070582DC0A2A100125669 /* LogDisplayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070552DC0A2A100125669 /* LogDisplayView.swift */; };
		8E7070592DC0A2A100125669 /* OverlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070562DC0A2A100125669 /* OverlayView.swift */; };
		8E70705A2DC0A2A100125669 /* LogService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70704C2DC0A2A100125669 /* LogService.swift */; };
		8E70705B2DC0A2A100125669 /* VideoRecordingService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070502DC0A2A100125669 /* VideoRecordingService.swift */; };
		8E70705C2DC0A2A100125669 /* GoalDetectorService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70704B2DC0A2A100125669 /* GoalDetectorService.swift */; };
		8E70705D2DC0A2A100125669 /* CameraFeedService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070482DC0A2A100125669 /* CameraFeedService.swift */; };
		8E70705E2DC0A2A100125669 /* GoalClassifierService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70704A2DC0A2A100125669 /* GoalClassifierService.swift */; };
		8E70705F2DC0A2A100125669 /* VideoBufferCache.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70704F2DC0A2A100125669 /* VideoBufferCache.swift */; };
		8E7070602DC0A2A100125669 /* HoopRegionOverlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070542DC0A2A100125669 /* HoopRegionOverlayView.swift */; };
		8E7070612DC0A2A100125669 /* ShootDetectorService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70704E2DC0A2A100125669 /* ShootDetectorService.swift */; };
		8E7070622DC0A2A100125669 /* AIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070472DC0A2A100125669 /* AIService.swift */; };
		8E7070632DC0A2A100125669 /* DatabaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070492DC0A2A100125669 /* DatabaseManager.swift */; };
		8E7070642DC0A2A100125669 /* ObjectDetectorService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E70704D2DC0A2A100125669 /* ObjectDetectorService.swift */; };
		8E7070652DC0A2A100125669 /* extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7070522DC0A2A100125669 /* extension.swift */; };
		8E7070692DC0A2A100125669 /* efficientdet_lite0.tflite in Resources */ = {isa = PBXBuildFile; fileRef = 8E7070432DC0A2A100125669 /* efficientdet_lite0.tflite */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		987354E9B92D1AE4325342B1 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3D335D36194D0D0A7A221226 /* Pods_Runner.framework */; };
		A77A57E32D07D95B009BFBDE /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A77A57E22D07D95B009BFBDE /* StoreKit.framework */; };
		DFDD85E0C0375137A466D1E3 /* Pods_RunnerTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6692FED639AF2004AD04B1BB /* Pods_RunnerTests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		331C8085294A63A400263BE5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97C146ED1CF9000F007C117D;
			remoteInfo = Runner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0190924B2DD19D0A00A12F13 /* TimerManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimerManager.swift; sourceTree = "<group>"; };
		019092F72DD1F00800A12F13 /* AnnotationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnnotationView.swift; sourceTree = "<group>"; };
		019092F92DD2FA4D00A12F13 /* LowBatteryWarningVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LowBatteryWarningVC.swift; sourceTree = "<group>"; };
		0190938E2DD3330D00A12F13 /* Homography.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Homography.swift; sourceTree = "<group>"; };
		019093912DD6DA3000A12F13 /* CustomAlertViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomAlertViewController.swift; sourceTree = "<group>"; };
		01C64C302E17C24800AF17CF /* DocumentManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentManager.swift; sourceTree = "<group>"; };
		01C64CC72E18FE9E00AF17CF /* clsf_goal.tflite */ = {isa = PBXFileReference; lastKnownFileType = file; path = clsf_goal.tflite; sourceTree = "<group>"; };
		01C64D0E2E190B7300AF17CF /* ShootClassifierService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShootClassifierService.swift; sourceTree = "<group>"; };
		01C64D102E190C2200AF17CF /* ShootClassifierConstants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShootClassifierConstants.swift; sourceTree = "<group>"; };
		01C64D112E190C2200AF17CF /* ShootClassifierInferenceConfigurationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShootClassifierInferenceConfigurationManager.swift; sourceTree = "<group>"; };
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		1C6CB06CFD71574B659A1992 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		331C807B294A618700263BE5 /* RunnerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunnerTests.swift; sourceTree = "<group>"; };
		331C8081294A63A400263BE5 /* RunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3D335D36194D0D0A7A221226 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		49F58BE224B438102DB7E2CC /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		6692FED639AF2004AD04B1BB /* Pods_RunnerTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7482B9B9442FAE203FF588F8 /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		7B590A7A8B2CDEF01990284C /* Runner.entitlements */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		8AD0F18B5B319A242E9C4BE0 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		8E7070282DC0795800125669 /* MyCustomViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyCustomViewController.swift; sourceTree = "<group>"; };
		8E70702E2DC09FE500125669 /* SessionDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SessionDetailViewController.swift; sourceTree = "<group>"; };
		8E7070312DC09FF200125669 /* RecordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordViewController.swift; sourceTree = "<group>"; };
		8E7070342DC0A01700125669 /* GoalCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoalCollectionViewCell.swift; sourceTree = "<group>"; };
		8E7070372DC0A12F00125669 /* CameraViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CameraViewController.swift; sourceTree = "<group>"; };
		8E70703A2DC0A28B00125669 /* ClassifierConstants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClassifierConstants.swift; sourceTree = "<group>"; };
		8E70703B2DC0A28B00125669 /* DefaultConstants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DefaultConstants.swift; sourceTree = "<group>"; };
		8E70703C2DC0A28B00125669 /* GoalClassifierInferenceConfigurationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoalClassifierInferenceConfigurationManager.swift; sourceTree = "<group>"; };
		8E70703D2DC0A28B00125669 /* InferenceConfigurationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InferenceConfigurationManager.swift; sourceTree = "<group>"; };
		8E7070432DC0A2A100125669 /* efficientdet_lite0.tflite */ = {isa = PBXFileReference; lastKnownFileType = file; path = efficientdet_lite0.tflite; sourceTree = "<group>"; };
		8E7070472DC0A2A100125669 /* AIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIService.swift; sourceTree = "<group>"; };
		8E7070482DC0A2A100125669 /* CameraFeedService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CameraFeedService.swift; sourceTree = "<group>"; };
		8E7070492DC0A2A100125669 /* DatabaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DatabaseManager.swift; sourceTree = "<group>"; };
		8E70704A2DC0A2A100125669 /* GoalClassifierService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoalClassifierService.swift; sourceTree = "<group>"; };
		8E70704B2DC0A2A100125669 /* GoalDetectorService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoalDetectorService.swift; sourceTree = "<group>"; };
		8E70704C2DC0A2A100125669 /* LogService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LogService.swift; sourceTree = "<group>"; };
		8E70704D2DC0A2A100125669 /* ObjectDetectorService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ObjectDetectorService.swift; sourceTree = "<group>"; };
		8E70704E2DC0A2A100125669 /* ShootDetectorService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShootDetectorService.swift; sourceTree = "<group>"; };
		8E70704F2DC0A2A100125669 /* VideoBufferCache.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoBufferCache.swift; sourceTree = "<group>"; };
		8E7070502DC0A2A100125669 /* VideoRecordingService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoRecordingService.swift; sourceTree = "<group>"; };
		8E7070522DC0A2A100125669 /* extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = extension.swift; sourceTree = "<group>"; };
		8E7070542DC0A2A100125669 /* HoopRegionOverlayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HoopRegionOverlayView.swift; sourceTree = "<group>"; };
		8E7070552DC0A2A100125669 /* LogDisplayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LogDisplayView.swift; sourceTree = "<group>"; };
		8E7070562DC0A2A100125669 /* OverlayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OverlayView.swift; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9C043255F0D30EAA533AE941 /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		A77A57E22D07D95B009BFBDE /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		B81C84AE1634288B01F8FA4E /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A77A57E32D07D95B009BFBDE /* StoreKit.framework in Frameworks */,
				987354E9B92D1AE4325342B1 /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A90F9374844C5B55B2F2D282 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DFDD85E0C0375137A466D1E3 /* Pods_RunnerTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0190938F2DD3330D00A12F13 /* Transform */ = {
			isa = PBXGroup;
			children = (
				0190938E2DD3330D00A12F13 /* Homography.swift */,
			);
			path = Transform;
			sourceTree = "<group>";
		};
		331C8082294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C807B294A618700263BE5 /* RunnerTests.swift */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		8E70703E2DC0A28B00125669 /* Configs */ = {
			isa = PBXGroup;
			children = (
				01C64D102E190C2200AF17CF /* ShootClassifierConstants.swift */,
				01C64D112E190C2200AF17CF /* ShootClassifierInferenceConfigurationManager.swift */,
				8E70703A2DC0A28B00125669 /* ClassifierConstants.swift */,
				8E70703B2DC0A28B00125669 /* DefaultConstants.swift */,
				8E70703C2DC0A28B00125669 /* GoalClassifierInferenceConfigurationManager.swift */,
				8E70703D2DC0A28B00125669 /* InferenceConfigurationManager.swift */,
			);
			path = Configs;
			sourceTree = "<group>";
		};
		8E7070512DC0A2A100125669 /* Services */ = {
			isa = PBXGroup;
			children = (
				01C64D0E2E190B7300AF17CF /* ShootClassifierService.swift */,
				8E7070472DC0A2A100125669 /* AIService.swift */,
				8E7070482DC0A2A100125669 /* CameraFeedService.swift */,
				8E7070492DC0A2A100125669 /* DatabaseManager.swift */,
				8E70704A2DC0A2A100125669 /* GoalClassifierService.swift */,
				8E70704B2DC0A2A100125669 /* GoalDetectorService.swift */,
				8E70704C2DC0A2A100125669 /* LogService.swift */,
				8E70704D2DC0A2A100125669 /* ObjectDetectorService.swift */,
				8E70704E2DC0A2A100125669 /* ShootDetectorService.swift */,
				8E70704F2DC0A2A100125669 /* VideoBufferCache.swift */,
				8E7070502DC0A2A100125669 /* VideoRecordingService.swift */,
				01C64C302E17C24800AF17CF /* DocumentManager.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		8E7070532DC0A2A100125669 /* utils */ = {
			isa = PBXGroup;
			children = (
				0190938F2DD3330D00A12F13 /* Transform */,
				8E7070522DC0A2A100125669 /* extension.swift */,
				0190924B2DD19D0A00A12F13 /* TimerManager.swift */,
			);
			path = utils;
			sourceTree = "<group>";
		};
		8E7070572DC0A2A100125669 /* views */ = {
			isa = PBXGroup;
			children = (
				8E7070542DC0A2A100125669 /* HoopRegionOverlayView.swift */,
				8E7070552DC0A2A100125669 /* LogDisplayView.swift */,
				8E7070562DC0A2A100125669 /* OverlayView.swift */,
				019092F72DD1F00800A12F13 /* AnnotationView.swift */,
			);
			path = views;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				019093912DD6DA3000A12F13 /* CustomAlertViewController.swift */,
				019092F92DD2FA4D00A12F13 /* LowBatteryWarningVC.swift */,
				8E7070372DC0A12F00125669 /* CameraViewController.swift */,
				8E7070342DC0A01700125669 /* GoalCollectionViewCell.swift */,
				8E7070312DC09FF200125669 /* RecordViewController.swift */,
				8E70702E2DC09FE500125669 /* SessionDetailViewController.swift */,
				8E7070282DC0795800125669 /* MyCustomViewController.swift */,
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				331C8082294A63A400263BE5 /* RunnerTests */,
				D542DA2A7975128E18D54B8A /* Pods */,
				CA0F3CD10FAF8E3DB74E8721 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				331C8081294A63A400263BE5 /* RunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				01C64CC72E18FE9E00AF17CF /* clsf_goal.tflite */,
				8E7070432DC0A2A100125669 /* efficientdet_lite0.tflite */,
				8E7070512DC0A2A100125669 /* Services */,
				8E7070532DC0A2A100125669 /* utils */,
				8E7070572DC0A2A100125669 /* views */,
				8E70703E2DC0A28B00125669 /* Configs */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
				7B590A7A8B2CDEF01990284C /* Runner.entitlements */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		CA0F3CD10FAF8E3DB74E8721 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A77A57E22D07D95B009BFBDE /* StoreKit.framework */,
				3D335D36194D0D0A7A221226 /* Pods_Runner.framework */,
				6692FED639AF2004AD04B1BB /* Pods_RunnerTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		D542DA2A7975128E18D54B8A /* Pods */ = {
			isa = PBXGroup;
			children = (
				8AD0F18B5B319A242E9C4BE0 /* Pods-Runner.debug.xcconfig */,
				49F58BE224B438102DB7E2CC /* Pods-Runner.release.xcconfig */,
				1C6CB06CFD71574B659A1992 /* Pods-Runner.profile.xcconfig */,
				7482B9B9442FAE203FF588F8 /* Pods-RunnerTests.debug.xcconfig */,
				9C043255F0D30EAA533AE941 /* Pods-RunnerTests.release.xcconfig */,
				B81C84AE1634288B01F8FA4E /* Pods-RunnerTests.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		331C8080294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */;
			buildPhases = (
				72FD430361AE1FA5C0B8E7DD /* [CP] Check Pods Manifest.lock */,
				331C807D294A63A400263BE5 /* Sources */,
				331C807F294A63A400263BE5 /* Resources */,
				A90F9374844C5B55B2F2D282 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				331C8086294A63A400263BE5 /* PBXTargetDependency */,
			);
			name = RunnerTests;
			productName = RunnerTests;
			productReference = 331C8081294A63A400263BE5 /* RunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				CAE1B0120E76377566C4B646 /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				1DFC9DE3D1888CE16F08AB8E /* [CP] Embed Pods Frameworks */,
				27CF8942C0BBFF4C03776FD4 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					331C8080294A63A400263BE5 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 97C146ED1CF9000F007C117D;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				331C8080294A63A400263BE5 /* RunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		331C807F294A63A400263BE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E7070692DC0A2A100125669 /* efficientdet_lite0.tflite in Resources */,
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				01C64CC82E18FE9E00AF17CF /* clsf_goal.tflite in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1DFC9DE3D1888CE16F08AB8E /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		27CF8942C0BBFF4C03776FD4 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		72FD430361AE1FA5C0B8E7DD /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RunnerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		CAE1B0120E76377566C4B646 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		331C807D294A63A400263BE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */,
				8E7070362DC0A01700125669 /* GoalCollectionViewCell.swift in Sources */,
				019092FB2DD2FA4D00A12F13 /* LowBatteryWarningVC.swift in Sources */,
				8E7070292DC0795800125669 /* MyCustomViewController.swift in Sources */,
				019093932DD6DA3000A12F13 /* CustomAlertViewController.swift in Sources */,
				8E7070322DC09FF200125669 /* RecordViewController.swift in Sources */,
				8E7070392DC0A12F00125669 /* CameraViewController.swift in Sources */,
				8E7070302DC09FE500125669 /* SessionDetailViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E70702A2DC0795800125669 /* MyCustomViewController.swift in Sources */,
				019092F82DD1F00800A12F13 /* AnnotationView.swift in Sources */,
				8E7070352DC0A01700125669 /* GoalCollectionViewCell.swift in Sources */,
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				8E7070332DC09FF200125669 /* RecordViewController.swift in Sources */,
				8E7070382DC0A12F00125669 /* CameraViewController.swift in Sources */,
				0190924C2DD19D0A00A12F13 /* TimerManager.swift in Sources */,
				8E7070582DC0A2A100125669 /* LogDisplayView.swift in Sources */,
				019093902DD3330D00A12F13 /* Homography.swift in Sources */,
				8E7070592DC0A2A100125669 /* OverlayView.swift in Sources */,
				01C64D122E190C2300AF17CF /* ShootClassifierInferenceConfigurationManager.swift in Sources */,
				01C64D132E190C2300AF17CF /* ShootClassifierConstants.swift in Sources */,
				8E70705A2DC0A2A100125669 /* LogService.swift in Sources */,
				8E70705B2DC0A2A100125669 /* VideoRecordingService.swift in Sources */,
				8E70705C2DC0A2A100125669 /* GoalDetectorService.swift in Sources */,
				8E70705D2DC0A2A100125669 /* CameraFeedService.swift in Sources */,
				01C64C312E17C24800AF17CF /* DocumentManager.swift in Sources */,
				8E70705E2DC0A2A100125669 /* GoalClassifierService.swift in Sources */,
				8E70705F2DC0A2A100125669 /* VideoBufferCache.swift in Sources */,
				8E7070602DC0A2A100125669 /* HoopRegionOverlayView.swift in Sources */,
				019092FA2DD2FA4D00A12F13 /* LowBatteryWarningVC.swift in Sources */,
				8E7070612DC0A2A100125669 /* ShootDetectorService.swift in Sources */,
				8E7070622DC0A2A100125669 /* AIService.swift in Sources */,
				8E7070632DC0A2A100125669 /* DatabaseManager.swift in Sources */,
				8E7070642DC0A2A100125669 /* ObjectDetectorService.swift in Sources */,
				8E7070652DC0A2A100125669 /* extension.swift in Sources */,
				8E70702F2DC09FE500125669 /* SessionDetailViewController.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
				01C64D0F2E190B7300AF17CF /* ShootClassifierService.swift in Sources */,
				8E70703F2DC0A28B00125669 /* ClassifierConstants.swift in Sources */,
				019093922DD6DA3000A12F13 /* CustomAlertViewController.swift in Sources */,
				8E7070402DC0A28B00125669 /* InferenceConfigurationManager.swift in Sources */,
				8E7070412DC0A28B00125669 /* GoalClassifierInferenceConfigurationManager.swift in Sources */,
				8E7070422DC0A28B00125669 /* DefaultConstants.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		331C8086294A63A400263BE5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97C146ED1CF9000F007C117D /* Runner */;
			targetProxy = 331C8085294A63A400263BE5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8A596TBVCR;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.shootZ.app.shootz;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				RESOURCES_TARGETED_DEVICE_FAMILY = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		331C8088294A63A400263BE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7482B9B9442FAE203FF588F8 /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shootZ.app.shootz.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Debug;
		};
		331C8089294A63A400263BE5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9C043255F0D30EAA533AE941 /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shootZ.app.shootz.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Release;
		};
		331C808A294A63A400263BE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B81C84AE1634288B01F8FA4E /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shootZ.app.shootz.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8A596TBVCR;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.shootZ.app.shootz;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				RESOURCES_TARGETED_DEVICE_FAMILY = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8A596TBVCR;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.shootZ.app.shootz;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				RESOURCES_TARGETED_DEVICE_FAMILY = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				331C8088294A63A400263BE5 /* Debug */,
				331C8089294A63A400263BE5 /* Release */,
				331C808A294A63A400263BE5 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
